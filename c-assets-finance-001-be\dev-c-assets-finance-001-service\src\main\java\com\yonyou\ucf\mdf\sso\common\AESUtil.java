package com.yonyou.ucf.mdf.sso.common;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Random;

/**
 * @className: AESUtil
 * @author: wjc
 * @date: 2025/5/20 10:35
 * @Version: 1.0
 * @description:
 */
public class AESUtil {
    private static final Logger logger = LoggerFactory.getLogger(AESUtil.class);

    private static final Base64 BASE64 = new Base64();
    private static byte[] aesKey;
    private static final Random RANDOM = new Random();
    /**
     * 固定密钥
     */
    private static final String KEY = "df073ddab11waf18a219ba8be45d7da3";

    public static void main(String[] args)  {
        String message = "{tenantId:0000LB7GORZKFK09HO0000, thirdUcId:e0ya1i2f, type:feishu}";
        String encrypt = encrypt(message);
        logger.info("len={},encryptedMap = {}", encrypt.length(), encrypt);
        //String decrypt = decrypt(encrypt);
        String decrypt = decrypt("jblCuMuuqAIQpX9PZIWkPEtHiMWvmSzEJ9Ej33tPdyGELX396Dg0yS1O+ggbSQ88lat8MWP3Qm0Jc15+7krhlBrm0wg0OloJ+8b+2lKIZJhwb6ypUis5b35DySVBCY4FN5jbzK+V+ArLGzUKY6YJIhqs7TPZjsICObJKoYIytOA");
        logger.info("decrypt = " + decrypt);
    }

    /**
     * 对明文加密.
     *
     * @param plaintext 需要加密的明文
     * @return 加密后base64编码的字符串
     */
    public static String encrypt(String plaintext) {
        try {
            String random = Utils.getRandomStr(16);
            aesKey = Base64.decodeBase64(KEY);
            byte[] randomBytes = random.getBytes(StandardCharsets.UTF_8);
            byte[] plainTextBytes = plaintext.getBytes(StandardCharsets.UTF_8);
            byte[] lengthByte = Utils.int2Bytes(plainTextBytes.length);
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            byteStream.write(randomBytes);
            byteStream.write(lengthByte);
            byteStream.write(plainTextBytes);
            byte[] padBytes = PKCS7Padding.getPaddingBytes(byteStream.size());
            byteStream.write(padBytes);
            byte[] unencrypted = byteStream.toByteArray();
            byteStream.close();
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(aesKey, 0, 16);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);
            byte[] encrypted = cipher.doFinal(unencrypted);
            return BASE64.encodeToString(encrypted);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "encrypt fails";
    }

    /**
     * 对密文进行解密.
     *
     * @param text 需要解密的密文
     * @return 解密得到的明文
     */
    public static String decrypt(String text)  {
        try {
            aesKey = Base64.decodeBase64(KEY);
            // 设置解密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16));
            cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
            // 使用BASE64对密文进行解码
            byte[] encrypted = Base64.decodeBase64(text);
            // 解密
            byte[] originalArr = cipher.doFinal(encrypted);
            // 去除补位字符
            byte[] bytes = PKCS7Padding.removePaddingBytes(originalArr);
            // 分离16位随机字符串,网络字节序和corpId
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
            int plainTextLegth = Utils.bytes2int(networkOrder);
            return new String(Arrays.copyOfRange(bytes, 20, 20 + plainTextLegth), StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException("Decrypt Failed");
        }
    }

    /**
     * 内部类
     */
    public static class Utils {
        public static String getRandomStr(int count) {
            String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < count; ++i) {
                int number = RANDOM.nextInt(base.length());
                sb.append(base.charAt(number));
            }
            return sb.toString();
        }

        public static byte[] int2Bytes(int count) {
            return new byte[]{(byte) (count >> 24 & 255), (byte) (count >> 16 & 255), (byte) (count >> 8 & 255),
                    (byte) (count & 255)};
        }

        public static int bytes2int(byte[] byteArr) {
            int count = 0;
            for (int i = 0; i < 4; ++i) {
                count <<= 8;
                count |= byteArr[i] & 255;
            }
            return count;
        }
    }

    /**
     * 内部类
     */
    static class PKCS7Padding {
        private static byte[] getPaddingBytes(int count) {
            int amountToPad = 32 - count % 32;
            char padChr = chr(amountToPad);
            StringBuilder tmp = new StringBuilder();
            for (int index = 0; index < amountToPad; ++index) {
                tmp.append(padChr);
            }
            return tmp.toString().getBytes(StandardCharsets.UTF_8);
        }

        private static byte[] removePaddingBytes(byte[] decrypted) {
            int pad = decrypted[decrypted.length - 1];
            if (pad < 1 || pad > 32) {
                pad = 0;
            }
            return Arrays.copyOfRange(decrypted, 0, decrypted.length - pad);
        }

        private static char chr(int a) {
            byte target = (byte) (a & 255);
            return (char) target;
        }
    }


}
