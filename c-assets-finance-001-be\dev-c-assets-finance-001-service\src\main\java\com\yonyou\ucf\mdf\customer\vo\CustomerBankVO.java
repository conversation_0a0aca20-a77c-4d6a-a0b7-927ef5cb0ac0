package com.yonyou.ucf.mdf.customer.vo;

import java.time.LocalDateTime;
import lombok.Data;

/**
 * 客户档案银行信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerBankVO {
    /**
     * 银行信息id；新增时不传，更新、删除时必传
     * 示例：12456
     */
    private Long id;

    /**
     * 国家；填写国家名称
     * 示例：国家
     */
    private String countryName;

    /**
     * 省份；填写省份编码
     * 示例：省份
     */
    private String provinceCode;

    /**
     * 币种；必填；填写币种名称
     * 示例：币种
     */
    private String currencyName;

    /**
     * 账户类型；必填；0：对公账号，1：对私账号
     * 示例：账户类型
     */
    private String accountType;

    /**
     * 银行类别；必填；填写银行类别名称
     * 示例：银行类别
     */
    private String bankName;

    /**
     * 银行网点；必填；填写银行网点名称
     * 示例：银行网点
     */
    private String openBankName;

    /**
     * 银行账号；必填
     * 示例：银行账号
     */
    private String bankAccount;

    /**
     * 账户名称；必填
     */
    private MultiLanguageVO bankAccountName;

    /**
     * 联行号
     * 示例：联行号
     */
    private String jointLineNo;

    /**
     * 备注
     * 示例：备注
     */
    private String remarks;

    /**
     * 开户地址
     */
    private MultiLanguageVO accountOpeningAddress;

    /**
     * 开户日期；例如：YYYY-MM-DD
     * 示例：2023-05-19 16:52:31
     */
    private LocalDateTime accountOpeningDate;

    /**
     * 启用状态；银行信息页签下；false：启用，true：停用
     * 示例：false
     */
    private Boolean stopStatus;

    /**
     * 默认银行账号；必填；银行信息页签下；银行信息的默认值只能设置一个；true：是；false：否
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案银行信息特征
     */
    private Object agentFinancialCharacter;

    /**
     * 客户档案银行信息实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;

    /**
     * 来源数据唯一标识
     * 示例：123456
     */
    private String sourceUnique;
} 