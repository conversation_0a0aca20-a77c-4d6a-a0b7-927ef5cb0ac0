package com.yonyou.ucf.mdf.kingdee.vo;

import com.alibaba.fastjson.JSON;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金蝶企业银行账号VO
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */

@EqualsAndHashCode
@Data
public class KingdeeBankAccount {

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 编码
	 */
	private String number;

	/**
	 * 账户性质类型
	 */
	private String accountBankType;

	/**
	 * 开户单位编码
	 */
	private String companyNum;

	/**
	 * 开户单位名称
	 */
	private String companyName;

	/**
	 * 开户行编码
	 */
	private String bankNum;

	/**
	 * 开户行名称
	 */
	private String bankName;

	/**
	 * 创建者编码
	 */
	private String creatorNum;

	/**
	 * 创建者名称
	 */
	private String creatorName;

	/**
	 * 账户名称
	 */
	private String acctname;

	/**
	 * 收支类型
	 */
	private String accountType;

	/**
	 * 银行账号
	 */
	private String bankAccountNumber;

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}