package com.yonyou.ucf.mdf.sync.util;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.service.CodeMappingCache;

/**
 * <AUTHOR>
 *
 *         2025年6月12日
 */
@Service
public class CodeTranslator {

	@Autowired
	private CodeMappingCache cache;

	/**
	 * 带自动初始化的翻译方法
	 * 
	 * @param mappingType
	 * @param mappingKey
	 * @return
	 */
	public String translate(MappingTypeEnum mappingType, String mappingKey) {
		if (StringUtils.isBlank(mappingKey)) {
			return null;
		}
		String targetCode = cache.getMapping(mappingType, mappingKey);
		if (targetCode == null) {
			synchronized (this) {
				// 双重检查
				targetCode = cache.getMapping(mappingType, mappingKey);
				if (targetCode == null) {
					cache.refresh(mappingType);
					targetCode = cache.getMapping(mappingType, mappingKey);
				}
			}
		}
		return targetCode;
	}

	/**
	 * 批量翻译（减少缓存访问次数）
	 * 
	 * @param mappingType
	 * @param mappingKey
	 * @return
	 */
	public Map<String, String> batchTranslate(MappingTypeEnum mappingType, List<String> mappingKey) {
		Map<String, String> result = new LinkedHashMap<>();
//		cache.preload(mappingType); // 预热缓存
		if (CollectionUtils.isEmpty(mappingKey)) {
			return result;
		}

		mappingKey.forEach(code -> result.put(code, translate(mappingType, code)));
		return result;
	}
}
