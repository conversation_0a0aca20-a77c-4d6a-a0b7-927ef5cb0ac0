package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户角色信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerRoleVO {
    /**
     * 营销伙伴；true：是，false：否
     * 示例：false
     */
    private Boolean merchantOptions;

    /**
     * 业务角色；不填默认为1为业务默认值；2：B2B商城客户，3：2B商家，4：零售加盟商，5：2C商家，6：伙伴客户。
     * 选择多个业务角色 半角逗号分隔；
     * 没有开通订货管理，不能录入B2B商城客户；
     * 没有开通渠道运营中心1.0，不能录入2B商家；
     * 没有开通友零售，不能录入零售加盟商；
     * 没有开通U商城，不能录入2C商家；
     * 没有开通伙伴管理，不能录入伙伴客户；
     * 营销伙伴为否，业务角色只允许填写【B2B商城客户】
     * 示例："1"
     */
    private String businessRole;
} 