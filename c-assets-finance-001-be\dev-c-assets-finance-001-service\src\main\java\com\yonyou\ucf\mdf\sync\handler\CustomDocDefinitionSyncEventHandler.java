package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocDefinition;
import com.yonyou.ucf.mdf.customdoc.service.ICustomDocDefinitionService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.DocTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.CustomDocDefinitionSyncEvent;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncLog;
import com.yonyou.ucf.mdf.sync.service.CustomDocDefinitionConverter;
import com.yonyou.ucf.mdf.sync.service.CustomDocSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.CustomDocSyncLogService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案定义同步事件处理器
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomDocDefinitionSyncEventHandler implements EventHandler<CustomDocDefinitionSyncEvent> {

	private final CustomDocDefinitionConverter customDocDefinitionConverter;
	private final ICustomDocDefinitionService customDocDefinitionService;
	private final CustomDocSyncLogService customDocSyncLogService;
	private final CustomDocSyncFailRecordService customDocSyncFailRecordService;
	private final EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(CustomDocDefinitionSyncEvent event, long sequence, boolean endOfBatch) throws Exception {
		KingdeeCustomDocDefinition kingdeeCustomDocDefinition = event.getKingdeeCustomDocDefinition();
		log.info("开始处理自定义档案定义同步事件，金蝶自定义档案定义：{}，序列号：{}", kingdeeCustomDocDefinition, sequence);
		CustomDocDefinition customDocDefinition = null;
		CustomDocSyncLog syncLog = null;
		long begin = System.currentTimeMillis();
		try {
			// 1. 转换数据
			customDocDefinition = customDocDefinitionConverter.convert(kingdeeCustomDocDefinition);
			if (customDocDefinition == null) {
				return;
			}

			// 2. 保存数据
			ResponseResult<CustomDocDefinition> responeResult = customDocDefinitionService
					.saveAndUpdate(customDocDefinition);

			// 3. 记录同步日志
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog = new CustomDocSyncLog();
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setListcode(customDocDefinition.getCode());
			syncLog.setListname(customDocDefinition.getName());
			syncLog.setDocType(DocTypeEnum.CUSTOM_DOC_SETTING.getCode());
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeCustomDocDefinition));
			syncLog.setRequestData(JSONObject.toJSONString(customDocDefinition));
			syncLog.setResponeData(JSONObject.toJSONString(responeResult));
			syncLog.setCostTime(costTime);
			if (!responeResult.isSuccess()) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(responeResult.getMessage());
			} else {
				syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
			}

			customDocSyncLogService.logSuccess(syncLog);

			log.info("自定义档案定义同步事件处理完成，金蝶自定义档案定义：{}，序列号：{}", event.getKingdeeCustomDocDefinition(), sequence);
		} catch (Exception e) {
			log.error("自定义档案定义同步事件处理失败，金蝶自定义档案定义：{}，序列号：{}", event.getKingdeeCustomDocDefinition(), sequence, e);
			// 记录失败日志
			if (syncLog == null) {
				syncLog = new CustomDocSyncLog();
			}
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog.setDocType(DocTypeEnum.CUSTOM_DOC_SETTING.getCode());
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg("自定义档案定义同步事件处理失败" + e.getMessage());
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			if (customDocDefinition != null) {
				syncLog.setListcode(customDocDefinition.getCode());
				syncLog.setListname(customDocDefinition.getName());
				syncLog.setRequestData(JSONObject.toJSONString(customDocDefinition));
			}
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeCustomDocDefinition));
			syncLog.setCostTime(costTime);
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			customDocSyncLogService.logError(syncLog);
		}

		// 保存失败记录
		CustomDocSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			customDocSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_CUSTOM_DOC_DEFINITION, kingdeeCustomDocDefinition.getNumber(),
					kingdeeCustomDocDefinition);
			ehCacheUtil.removeFail(CacheConstant.CACHE_CUSTOM_DOC_DEFINITION, kingdeeCustomDocDefinition.getNumber());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_CUSTOM_DOC_DEFINITION, kingdeeCustomDocDefinition.getNumber(),
					kingdeeCustomDocDefinition);
		}
	}

	/**
	 * 删除同步失败记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(CustomDocSyncLog syncLog) {
		customDocSyncFailRecordService.deleteBySyncLog(syncLog);
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private CustomDocSyncFailRecord convertFailRecord(CustomDocSyncLog syncLog) {
		if (syncLog == null) {
			return null;
		}
		CustomDocSyncFailRecord failRecord = new CustomDocSyncFailRecord();
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		failRecord.setListcode(syncLog.getListcode());
		failRecord.setListname(syncLog.getListname());
		failRecord.setCode(syncLog.getCode());
		failRecord.setName(syncLog.getName());
		failRecord.setDocType(syncLog.getDocType());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setRetryCount(0);
		return failRecord;
	}
}