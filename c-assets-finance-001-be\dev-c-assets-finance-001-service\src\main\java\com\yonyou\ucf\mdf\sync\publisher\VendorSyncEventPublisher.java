package com.yonyou.ucf.mdf.sync.publisher;

import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeVendor;
import com.yonyou.ucf.mdf.sync.event.VendorSyncEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 供应商同步事件发布者
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VendorSyncEventPublisher {

	private final Disruptor<VendorSyncEvent> vendorSyncDisruptor;
	private final RingBuffer<VendorSyncEvent> ringBuffer;

	/**
	 * 发布供应商同步事件
	 * 
	 * @param kingdeeVendor 金蝶供应商数据
	 */
	public void publish(KingdeeVendor kingdeeVendor) {
		try {
			// 获取下一个序列号
			long sequence = ringBuffer.next();
			try {
				// 获取该序列号对应的事件对象
				VendorSyncEvent event = ringBuffer.get(sequence);
				// 填充事件数据
				event.setKingdeeVendor(kingdeeVendor);
				event.setSyncTime(String.valueOf(System.currentTimeMillis()));
			} finally {
				// 发布事件
				ringBuffer.publish(sequence);
			}
		} catch (Exception e) {
			log.error("发布供应商同步事件失败，金蝶供应商：{}", kingdeeVendor, e);
			throw e;
		}
	}

	/**
	 * 关闭Disruptor
	 */
	public void shutdown() {
		log.error("供应商同步事件发布关闭");
		vendorSyncDisruptor.shutdown();
	}
}