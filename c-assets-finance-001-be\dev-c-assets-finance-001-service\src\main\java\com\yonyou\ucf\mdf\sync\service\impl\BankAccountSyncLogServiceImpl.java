package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.sync.model.BankAccountSyncLog;
import com.yonyou.ucf.mdf.sync.service.BankAccountSyncLogService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月3日
 */
@Slf4j
@Service
public class BankAccountSyncLogServiceImpl implements BankAccountSyncLogService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public void logSuccess(BankAccountSyncLog syncLog) {
		log.info("保存企业银行账户同步成功日志");
		try {
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			saveAsRobot(billDOs);
		} catch (Exception e) {
			log.error("企业银行账户同步成功日志保存失败 - {}", JSONObject.toJSONString(syncLog));
			throw new RuntimeException("保存企业银行账户同步日志报错！", e);
		}
	}

	@Override
	public void logError(BankAccountSyncLog syncLog) {
		log.info("保存企业银行账户同步失败日志");
		try {
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			saveAsRobot(billDOs);
		} catch (Exception e) {
			log.error("企业银行账户同步失败日志保存失败 - {}", JSONObject.toJSONString(syncLog));
			throw new RuntimeException("保存企业银行账户同步日志报错！", e);
		}
	}

	/**
	 * 使用机器人保存，因为定时任务没有登录上下文
	 * 
	 * @param billDOs
	 * @return
	 */
	private List<IBillDO> saveAsRobot(List<IBillDO> billDOs) {
		return RobotExecutors.runAs(tenantId, () -> {
			try {
				return billCommonRepository.commonSaveBill(billDOs, "BankAccountSyncLog");
			} catch (Exception e) {
				log.error("保存企业银行账户同步日志报错！" + e.getMessage(), e);
			}
			return null;
		});
	}

}
