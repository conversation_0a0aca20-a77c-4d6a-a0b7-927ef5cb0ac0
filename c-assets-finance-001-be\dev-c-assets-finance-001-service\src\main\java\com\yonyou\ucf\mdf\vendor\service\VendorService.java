package com.yonyou.ucf.mdf.vendor.service;

import java.util.List;

import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.vendor.vo.VendorBatchResult;
import com.yonyou.ucf.mdf.vendor.vo.VendorVO;

/**
 * 供应商服务接口
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
public interface VendorService {

    /**
     * 批量保存供应商信息
     * 
     * @param vendorVOList 供应商信息列表
     * @return 批量保存结果
     */
    ResponseResult<VendorBatchResult> batchSaveVendor(List<VendorVO> vendorVOList);
} 