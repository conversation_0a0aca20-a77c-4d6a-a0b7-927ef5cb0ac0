package com.yonyou.ucf.mdf.sync.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest.QueryParams;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankAccount;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeResponse;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.publisher.BankAccountSyncEventPublisher;
import com.yonyou.ucf.mdf.sync.service.BankAccountSyncService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 企业银行账户同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Service
public class BankAccountSyncServiceImpl implements BankAccountSyncService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Autowired
	private BankAccountSyncEventPublisher bankAccountSyncEventPublisher;

	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 同步起始页
	 */
	private Integer dataStart = 0;

	/**
	 * 同步每页获取数量
	 */
	@Value("${kingdee.sync.pageSize}")
	private Integer pageSize = 100;

	@Override
	public void syncBankAccount(LocalDateTime startTime, LocalDateTime endTime) {
		log.error("开始同步企业银行账户数据，时间范围：{} - {}", startTime, endTime);
		try {
			// 1. 构建金蝶接口请求参数
			KingdeeEasRequest.QueryParams queryParams = new KingdeeEasRequest.QueryParams();
			queryParams.setModifytimeStart(startTime);
			queryParams.setModifytimeEnd(endTime);

			KingdeeEasRequest<KingdeeEasRequest.QueryParams> request = new KingdeeEasRequest<>();
			request.setData(queryParams);
			request.setDataStart(dataStart);
			request.setPageSize(pageSize);

			// 2. 调用金蝶接口获取数据
			String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpBankAccount",
					request);
			if (StringUtils.isBlank(result)) {
				log.error("未获取到企业银行账户数据");
				return;
			}
			KingdeeResponse<KingdeeBankAccount> response = JSON.parseObject(result,
					new TypeReference<KingdeeResponse<KingdeeBankAccount>>() {
					});

			if (response == null || response.getRows() == null) {
				log.error("未获取到企业银行账户数据");
				return;
			}

			// 3. 发布同步事件
			publishEvent(response.getRows());

			// 4. 如果还有更多数据，继续获取
			if (response.getRows().size() == pageSize) {
				request.setDataStart(request.getDataStart() + pageSize);
				syncBankAccount(request);
			}
			log.error("同步企业银行账户数据结束------");
		} catch (Exception e) {
			log.error("同步企业银行账户数据失败", e);
			throw new RuntimeException("同步企业银行账户数据失败", e);
		}
	}

	/**
	 * 递归调用
	 * 
	 * @param request 请求参数
	 */
	private void syncBankAccount(KingdeeEasRequest<QueryParams> request) {
		// 2. 调用金蝶接口获取数据
		String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpBankAccount", request);
		if (StringUtils.isBlank(result)) {
			log.error("未获取到企业银行账户数据");
			return;
		}
		KingdeeResponse<KingdeeBankAccount> response = JSON.parseObject(result,
				new TypeReference<KingdeeResponse<KingdeeBankAccount>>() {
				});

		if (response == null || response.getRows() == null) {
			log.error("未获取到企业银行账户数据");
			return;
		}

		// 3. 发布同步事件
		publishEvent(response.getRows());

		// 4. 如果还有更多数据，继续获取
		if (response.getRows().size() == pageSize) {
			request.setDataStart(request.getDataStart() + pageSize);
			syncBankAccount(request);
		}
	}

	@Override
	public void publishEvent(List<KingdeeBankAccount> kingdeeBankAccounts) {
		for (KingdeeBankAccount kingdeeBankAccount : kingdeeBankAccounts) {
			try {
				KingdeeBankAccount failBankAccount = (KingdeeBankAccount) ehCacheUtil
						.getFail(CacheConstant.CACHE_BANK_ACCOUNT, kingdeeBankAccount.getNumber());
				if (failBankAccount != null) {
					bankAccountSyncEventPublisher.publish(kingdeeBankAccount);
				} else {
					KingdeeBankAccount successBankAccount = (KingdeeBankAccount) ehCacheUtil
							.getSuccess(CacheConstant.CACHE_BANK_ACCOUNT, kingdeeBankAccount.getNumber());
					if (successBankAccount == null) {
						bankAccountSyncEventPublisher.publish(kingdeeBankAccount);
					} else if (!successBankAccount.equals(kingdeeBankAccount)) {
						bankAccountSyncEventPublisher.publish(kingdeeBankAccount);
					}
				}
			} catch (Exception e) {
				log.error("发布企业银行账户同步事件失败，企业银行账户编码：{}", kingdeeBankAccount.getNumber(), e);
			}
		}
	}
}