package com.yonyou.ucf.mdf.sync.event;

import org.springframework.stereotype.Component;

import com.lmax.disruptor.EventFactory;

/**
 * 固定资产同步事件工厂
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Component
public class FixedAssetSyncEventFactory implements EventFactory<FixedAssetSyncEvent> {

	@Override
	public FixedAssetSyncEvent newInstance() {
		// 返回基础事件对象，发布者会设置具体属性
		return new FixedAssetSyncEvent();
	}
}
