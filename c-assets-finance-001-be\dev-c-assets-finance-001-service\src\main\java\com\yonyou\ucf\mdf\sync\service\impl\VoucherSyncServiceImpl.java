package com.yonyou.ucf.mdf.sync.service.impl;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.kingdee.voucher.service.KingdeeVoucherService;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucherQueryParams;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.publisher.VoucherSyncEventPublisher;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class VoucherSyncServiceImpl implements VoucherSyncService {

	@Autowired
	private KingdeeVoucherService kingdeeVoucherService;

	@Autowired
	private VoucherSyncEventPublisher voucherSyncEventPublisher;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 期间格式：yyyyMM
	 */
	private static final DateTimeFormatter PERIOD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

	@Override
	public void syncVoucher(String fromPeriodNumber, String toPeriodNumber, String companyNumber,
			String voucherNumber) {
		log.info("开始同步凭证数据，期间范围：{} - {}，会计主体：{}", fromPeriodNumber, toPeriodNumber, companyNumber);
		try {
			// 解析开始和结束期间
			YearMonth fromPeriod = YearMonth.parse(fromPeriodNumber, PERIOD_FORMATTER);
			YearMonth toPeriod = YearMonth.parse(toPeriodNumber, PERIOD_FORMATTER);

			// 按月循环处理
			YearMonth currentPeriod = fromPeriod;
			while (!currentPeriod.isAfter(toPeriod)) {
				String periodNumber = currentPeriod.format(PERIOD_FORMATTER);
				log.info("开始处理{}期间的凭证数据", periodNumber);

				// 1. 构建金蝶凭证查询参数
				KingdeeVoucherQueryParams queryParams = new KingdeeVoucherQueryParams();
				queryParams.setIsColumnAlias(false);
				queryParams.setIsExpCashflow(true);
				/**
				 * 0 对应的状态是 TEMP（暂存） 1 对应的状态是 SUBMITTED（已提交） 2 对应的状态是 CANCELLED（已取消） 3 对应的状态是
				 * AUDITTED（已审核） 5 对应的状态是 POSTED（已记账）
				 */
				queryParams.setBizStatus("3;5");
				queryParams.setFromPeriodNumber(periodNumber);
				queryParams.setToPeriodNumber(periodNumber);
				queryParams.setCompanyNumber(companyNumber);
				if (StringUtils.isNotBlank(voucherNumber)) {
					queryParams.setVoucherNumber(voucherNumber);
				}

				// 2. 调用金蝶凭证服务查询凭证数据
				List<KingdeeVoucher> vouchers = kingdeeVoucherService.queryVouchers(queryParams);
				if (vouchers != null && !vouchers.isEmpty()) {
					// 3. 发布同步事件
					publishEvent(vouchers);
					log.info("{}期间的凭证数据处理完成，共处理{}条凭证", periodNumber, vouchers.size());
				} else {
					log.info("{}期间未获取到凭证数据", periodNumber);
				}

				// 移动到下一个月
				currentPeriod = currentPeriod.plusMonths(1);
			}

			log.info("同步凭证数据完成，期间范围：{} - {}", fromPeriodNumber, toPeriodNumber);
		} catch (Exception e) {
			log.error("同步凭证数据失败", e);
			throw new RuntimeException("同步凭证数据失败", e);
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void publishEvent(List<KingdeeVoucher> kingdeeVouchers) {
		if (kingdeeVouchers == null || kingdeeVouchers.isEmpty()) {
			return;
		}

		// 按凭证id分组
		Map<String, List<KingdeeVoucher>> groupedVouchers = kingdeeVouchers.stream()
				.collect(Collectors.groupingBy(KingdeeVoucher::getVoucherId));

		// 遍历分组后的数据并发布事件
		groupedVouchers.forEach((key, vouchers) -> {
			try {
				List<KingdeeVoucher> failVoucher = (List<KingdeeVoucher>) ehCacheUtil
						.getFail(CacheConstant.CACHE_VOUCHER, key);
				if (failVoucher != null) {
					voucherSyncEventPublisher.publish(vouchers);
				} else {
					List<KingdeeVoucher> successVoucher = (List<KingdeeVoucher>) ehCacheUtil
							.getSuccess(CacheConstant.CACHE_VOUCHER, key);
					if (successVoucher == null) {
						voucherSyncEventPublisher.publish(vouchers);
					} else {
						// 比较两个列表是否相等
						boolean isEqual = compareVoucherLists(successVoucher, vouchers);
						if (!isEqual) {
							voucherSyncEventPublisher.publish(vouchers);
						}
					}
				}
				// 发布该组的所有凭证
//				voucherSyncEventPublisher.publish(vouchers);
			} catch (Exception e) {
				log.error("发布凭证同步事件失败，分组key：{}", key, e);
			}
		});
	}

	/**
	 * 比较两个凭证列表是否相等
	 * 
	 * @param list1 第一个凭证列表
	 * @param list2 第二个凭证列表
	 * @return 如果两个列表包含相同的凭证（不考虑顺序），返回true；否则返回false
	 */
	private boolean compareVoucherLists(List<KingdeeVoucher> list1, List<KingdeeVoucher> list2) {
		if (list1 == list2) {
			return true;
		}
		if (list1 == null || list2 == null || list1.size() != list2.size()) {
			return false;
		}

		// 使用List的containsAll方法比较两个列表是否包含相同的元素
		// 由于KingdeeVoucher类有@EqualsAndHashCode注解，会正确比较对象内容
		return list1.containsAll(list2) && list2.containsAll(list1);
	}
}
