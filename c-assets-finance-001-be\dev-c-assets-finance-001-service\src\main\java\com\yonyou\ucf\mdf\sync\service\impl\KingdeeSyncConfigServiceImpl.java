package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.KingdeeSyncConfigService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

/**
 * 同步数据系统配置查询接口实现类
 * 
 * <AUTHOR>
 *
 *         2025年5月29日
 */
@Service
public class KingdeeSyncConfigServiceImpl implements KingdeeSyncConfigService {

	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, List<Map<String, Object>>> queryAllCfg(SyncTypeEnum syncType) {

		Map<String, List<Map<String, Object>>> cacheResult = (Map<String, List<Map<String, Object>>>) ehCacheUtil
				.getValue(CacheConstant.CACHE_SYNC_CONFIG, syncType.name());
		if (cacheResult != null) {
			return cacheResult;
		}

		String sql = "{call c_assets_finance_001_db.GetHierarchyData(?)}";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(syncType.getName());

		List<Map<String, Object>> result = billRepository.queryForList(sql, parameter, new MapListProcessor());
		if (CollectionUtils.isEmpty(result)) {
			cacheResult = Collections.emptyMap();
		} else {
			cacheResult = result.stream().collect(Collectors.groupingBy(v -> {
				return v.getOrDefault("type_name", "").toString();
			}));
		}
		ehCacheUtil.putValue(CacheConstant.CACHE_SYNC_CONFIG, syncType.name(), cacheResult);
		return cacheResult;
	}

	@Override
	public List<Map<String, Object>> queryDefualtValueCfg(SyncTypeEnum bankBranch) {
		Map<String, List<Map<String, Object>>> allConfig = queryAllCfg(bankBranch);
		return allConfig.getOrDefault(bankBranch.getDefaultValueName(), Collections.emptyList());
	}

}
