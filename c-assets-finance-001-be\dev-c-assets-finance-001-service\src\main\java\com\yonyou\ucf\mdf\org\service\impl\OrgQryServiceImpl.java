package com.yonyou.ucf.mdf.org.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.org.service.OrgQryService;
import com.yonyou.ucf.mdf.org.vo.OrgVO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

/**
 * 业务单元查询接口实现
 * 
 * <AUTHOR>
 *
 *         2025年5月30日
 */
@Service
public class OrgQryServiceImpl implements OrgQryService {

	@Autowired
	private IBillRepository billRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public OrgVO queryByCode(String code) {
		String sql = "select id,code,name from iuap_apdoc_basedoc.org_orgs where description = ? and ytenant_id = ?";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(code);
		parameter.addParam(tenantId);
		return billRepository.queryForDTO(sql, parameter, OrgVO.class);
	}

}
