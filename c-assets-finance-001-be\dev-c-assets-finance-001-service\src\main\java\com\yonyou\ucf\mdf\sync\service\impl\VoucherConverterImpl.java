package com.yonyou.ucf.mdf.sync.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.model.OrgBookMapping;
import com.yonyou.ucf.mdf.sync.service.KingdeeSyncConfigService;
import com.yonyou.ucf.mdf.sync.service.OrgBookMappingService;
import com.yonyou.ucf.mdf.sync.service.VoucherConverter;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;
import com.yonyou.ucf.mdf.voucher.model.AuxiliaryAccountingItem;
import com.yonyou.ucf.mdf.voucher.model.CashFlowItem;
import com.yonyou.ucf.mdf.voucher.model.Voucher;
import com.yonyou.ucf.mdf.voucher.model.VoucherDetail;

import lombok.extern.slf4j.Slf4j;

/**
 * 金蝶凭证数据转换实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class VoucherConverterImpl implements VoucherConverter {

	@Autowired
	private OrgBookMappingService orgBookMappingService;
	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private KingdeeSyncConfigService kingdeeSyncConfigService;

	/**
	 * 用于提取金蝶凭证号和转换
	 */
	private Pattern pattern = Pattern.compile("^([A-Z]{2}).*-([0-9]+)$");

	/**
	 * 使用ThreadLocal确保每个线程使用独立的SimpleDateFormat实例
	 */
	private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = ThreadLocal
			.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

	@Override
	public Voucher convert(List<KingdeeVoucher> kingdeeVouchers) {
		if (kingdeeVouchers == null || kingdeeVouchers.isEmpty()) {
			return null;
		}

		// 获取第一条明细数据用于凭证头信息
		KingdeeVoucher firstDetail = kingdeeVouchers.get(0);

		Voucher voucher = new Voucher();
		// 先设置默认值
		defaultValueUtil.setDefaultValue(voucher, SyncTypeEnum.VOUCHER);

		// 设置凭证号
		String kingdeeVoucherNumber = firstDetail.getVoucherNumber();
		if (StringUtils.isNotBlank(kingdeeVoucherNumber)) {
			Matcher matcher = pattern.matcher(kingdeeVoucherNumber);
			if (matcher.find()) {
				String prefix = matcher.group(1); // 第一个捕获组是字母前缀
				String number = matcher.group(2); // 第二个捕获组是数字后缀
				String prefixNum = convertNum(prefix); // 根据前缀字符取映射数字
				String voucherNo = number;
				if (StringUtils.isNotBlank(prefixNum)) {
					voucherNo = prefixNum + number;
				}
				try {
					Integer voucherNumber = Integer.parseInt(voucherNo);
					voucher.setBillCode(voucherNumber);
					// 同时把金蝶的凭证号设置到自定义项
					voucher.setDefInfo1(kingdeeVoucherNumber);
				} catch (NumberFormatException e) {
					throw new RuntimeException("转换金蝶凭证号失败，原始凭证号：" + kingdeeVoucherNumber);
				}
			} else {
				throw new RuntimeException("转换金蝶凭证号失败，原始凭证号：" + kingdeeVoucherNumber);
			}
//			try {
//				// 从金蝶凭证号中提取数字部分（例如：从"EA01.01-00001"中提取"00001"）
//				String numberPart = kingdeeVoucherNumber.substring(kingdeeVoucherNumber.lastIndexOf("-") + 1);
//				// 转换为整数（去除前导零）
//				Integer voucherNumber = Integer.parseInt(numberPart);
//				voucher.setBillCode(voucherNumber);
//				// 同时把金蝶的凭证号设置到自定义项
//				voucher.setDefInfo1(kingdeeVoucherNumber);
//			} catch (Exception e) {
//				log.error("转换金蝶凭证号失败，原始凭证号：{}", kingdeeVoucherNumber, e);
//				// 如果转换失败，不设置凭证号，让系统自动生成
//			}
		}

		// 设置账簿编码
		String kingdeeOrgCode = firstDetail.getCompanyNumber();
		if (StringUtils.isNotBlank(kingdeeOrgCode)) {
			OrgBookMapping mapping = orgBookMappingService.getByKingdeeOrgCode(kingdeeOrgCode);
			if (mapping != null) {
				voucher.setAccbookCode(mapping.getBookCode());
			} else {
				log.warn("未找到金蝶会计主体编码对应的账簿映射关系，金蝶会计主体编码：{}", kingdeeOrgCode);
			}
		}
		if (StringUtils.isBlank(voucher.getAccbookCode())) {
			throw new RuntimeException("未找到金蝶公司编码对应的账簿映射关系，金蝶公司编码：" + kingdeeOrgCode);
		}

		// 设置来源系统编码（默认figl）
		voucher.setSrcSystemCode("figl");

		// 设置凭证类型编码（默认记）
//		voucher.setVoucherTypeCode("jz");

		// 设置制单人信息
//		voucher.setMakerMobile(firstDetail.getCreator());

		// 设置制单日期
		if (firstDetail.getBookedDate() != null) {
			try {
				voucher.setMakeTime(DATE_FORMAT.get().format(firstDetail.getBookedDate()));
			} catch (Exception e) {
				log.error("格式化制单日期失败，日期：{}", firstDetail.getBookedDate(), e);
				voucher.setMakeTime(firstDetail.getBookedDate().toString());
			}
		}

		// 设置凭证头摘要
		voucher.setDescription(firstDetail.getVoucherAbstract());

		// 按分录号分组(过滤掉entrySeq为null的记录)
		Map<String, List<KingdeeVoucher>> groupedDetails = kingdeeVouchers.stream()
				.filter(v -> StringUtils.isNotBlank(v.getEntrySeq()))
				.collect(Collectors.groupingBy(KingdeeVoucher::getEntrySeq));

		// 按对方科目号分组(过滤掉oppAccountSeq为null的记录以及primaryItem为null的数据)
		Map<String, List<KingdeeVoucher>> oppAccountGroups = kingdeeVouchers.stream()
				.filter(v -> StringUtils.isNotBlank(v.getOppAccountSeq()) && StringUtils.isNotBlank(v.getPrimaryItem()))
				.collect(Collectors.groupingBy(KingdeeVoucher::getOppAccountSeq));

		// 转换并合并凭证明细
		List<VoucherDetail> details = new ArrayList<>();
		for (List<KingdeeVoucher> group : groupedDetails.values()) {
			KingdeeVoucher groupFirstDetail = group.get(0);
			VoucherDetail voucherDetail = new VoucherDetail();

			// 设置摘要
			voucherDetail.setDescription(
					StringUtils.isNotBlank(groupFirstDetail.getAssistAbstract()) ? groupFirstDetail.getAssistAbstract()
							: groupFirstDetail.getVoucherAbstract());

			// 设置科目编码
			String accountNumber = groupFirstDetail.getAccountNumber();
			if (StringUtils.isNotBlank(accountNumber)) {
				// 去除科目编码中的点号
				accountNumber = accountNumber.replace(".", "");
			}
			voucherDetail.setAccsubjectCode(accountNumber);

			// 设置币种和汇率信息
//			voucherDetail.setCurrencyCode(detail.getCurrencyNumber());
			voucherDetail.setCurrencyCode("CNY");
			voucherDetail.setRateOrg(groupFirstDetail.getLocalRate());

			// 设置业务日期
			if (groupFirstDetail.getBizDate() != null) {
				try {
					voucherDetail.setBusidate(DATE_FORMAT.get().format(groupFirstDetail.getBizDate()));
				} catch (Exception e) {
					log.error("格式化业务日期失败，日期：{}", groupFirstDetail.getBizDate(), e);
				}
			}

			// 合并借贷金额
			BigDecimal totalDebit = group.stream().map(KingdeeVoucher::getDebitAmount).filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);

			BigDecimal totalCredit = group.stream().map(KingdeeVoucher::getCreditAmount).filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);

			if (totalDebit.compareTo(BigDecimal.ZERO) != 0) {
				voucherDetail.setDebitOriginal(totalDebit);
				voucherDetail.setDebitOrg(totalDebit);
			}
			if (totalCredit.compareTo(BigDecimal.ZERO) != 0) {
				voucherDetail.setCreditOriginal(totalCredit);
				voucherDetail.setCreditOrg(totalCredit);
			}

			// 合并数量信息
			voucherDetail.setQuantity(group.stream().map(KingdeeVoucher::getQty).filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add));

			// 价格取第一条明细的值
			voucherDetail.setPrice(groupFirstDetail.getPrice());

			// 收集所有辅助核算项(去重)
			Set<AuxiliaryAccountingItem> uniqueAssists = new HashSet<>();
			for (KingdeeVoucher detail : group) {
				// 辅助核算项1-8
				addAssistItem(uniqueAssists, detail.getAsstActType1(), detail.getAsstActNumber1());
				addAssistItem(uniqueAssists, detail.getAsstActType2(), detail.getAsstActNumber2());
				addAssistItem(uniqueAssists, detail.getAsstActType3(), detail.getAsstActNumber3());
				addAssistItem(uniqueAssists, detail.getAsstActType4(), detail.getAsstActNumber4());
				addAssistItem(uniqueAssists, detail.getAsstActType5(), detail.getAsstActNumber5());
				addAssistItem(uniqueAssists, detail.getAsstActType6(), detail.getAsstActNumber6());
				addAssistItem(uniqueAssists, detail.getAsstActType7(), detail.getAsstActNumber7());
				addAssistItem(uniqueAssists, detail.getAsstActType8(), detail.getAsstActNumber8());
			}

			// 转换为列表
			List<AuxiliaryAccountingItem> assists = new ArrayList<>(uniqueAssists);

			// 设置辅助核算列表
			if (!assists.isEmpty()) {
				voucherDetail.setClientAuxiliaryList(assists);
			}

			details.add(voucherDetail);

			// 生成现金流量子集
			if (oppAccountGroups.containsKey(groupFirstDetail.getEntrySeq())) {
				List<KingdeeVoucher> cashflowItems = oppAccountGroups.get(groupFirstDetail.getEntrySeq());
				// 这里可以添加现金流量子集的生成逻辑
				voucherDetail.setCashflowList(convertCashflowItems(cashflowItems));
			}
		}

		// 设置凭证明细
		voucher.setBodies(details);

		return voucher;
	}

	/**
	 * 根据金蝶凭证号前缀字符，取BIP映射数字
	 * 
	 * @param prefix
	 * @return
	 */
	private String convertNum(String prefix) {
		Map<String, List<Map<String, Object>>> configMap = kingdeeSyncConfigService.queryAllCfg(SyncTypeEnum.VOUCHER);
		if (configMap == null || configMap.size() == 0) {
			return null;
		}
		List<Map<String, Object>> cfg = configMap.get("凭证号映射");
		if (cfg == null || cfg.size() == 0) {
			return null;
		}
		for (Map<String, Object> map : cfg) {
			if (map.getOrDefault("config_key", "").toString().equals(prefix)) {
				return map.getOrDefault("config_value", "").toString();
			}
		}
		return null;
	}

	/**
	 * 转换现金流量子集
	 * 
	 * @param cashflowItems 金蝶现金流量项列表
	 * @return 现金流量子集列表
	 */
	private List<CashFlowItem> convertCashflowItems(List<KingdeeVoucher> cashflowItems) {
		List<CashFlowItem> result = new ArrayList<>();
		if (cashflowItems == null || cashflowItems.isEmpty()) {
			return result;
		}

		for (KingdeeVoucher item : cashflowItems) {
			CashFlowItem cashflow = new CashFlowItem();

			// 设置主表项目code
			if (StringUtils.isNotBlank(item.getPrimaryItem())) {
				cashflow.setMainItemCode(item.getPrimaryItem());
			}

			// 设置附表项目code
			if (StringUtils.isNotBlank(item.getSupplyItem())) {
				cashflow.setSupItemCode(item.getSupplyItem());
			}

			BigDecimal primaryCoef = BigDecimal.ONE;
			if (StringUtils.isNotBlank(item.getPrimaryCoef()) && !"0".equals(item.getPrimaryCoef())) {
				primaryCoef = new BigDecimal(item.getPrimaryCoef());
			}

			// 设置现金流量金额
			BigDecimal amount = item.getCashflowAmountOriginal() != null ? item.getCashflowAmountOriginal()
					: BigDecimal.ZERO;
			amount = amount.multiply(primaryCoef); // 乘以主表系数
			cashflow.setAmountOriginal(amount);
			cashflow.setAmountOrg(amount);

			result.add(cashflow);
		}

		return result;
	}

	private void addAssistItem(Set<AuxiliaryAccountingItem> assists, String type, String number) {
		if (StringUtils.isNotBlank(type) && StringUtils.isNotBlank(number)) {
			AuxiliaryAccountingItem assist = new AuxiliaryAccountingItem();
			assist.setFiledCode(type);
			assist.setValueCode(number);
			assists.add(assist);
		}
	}
}
