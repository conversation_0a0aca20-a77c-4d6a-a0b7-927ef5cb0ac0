<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
            
    
    <wb-module deploy-name="c-assets-finance-001">
                        
        
        <wb-resource deploy-path="/" source-path="/target/m2e-wtp/web-resources"/>
                        
        
        <wb-resource deploy-path="/" source-path="/src/main/webapp" tag="defaultRootSource"/>
                        
        
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/java"/>
                        
        
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/resources"/>
                
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/target/generated-sources/annotations"/>
                
        <dependent-module archiveName="c-assets-finance-001-api-ddm-3.0-RELEASE.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/dev-c-assets-finance-001-api/dev-c-assets-finance-001-api">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="c-assets-finance-001-app-ddm-3.0-RELEASE.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/dev-c-assets-finance-001-app/dev-c-assets-finance-001-app">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="c-assets-finance-001-infrastructure-ddm-3.0-RELEASE.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/dev-c-assets-finance-001-infrastructure/dev-c-assets-finance-001-infrastructure">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                
        <dependent-module archiveName="c-assets-finance-001-service-ddm-3.0-RELEASE.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/dev-c-assets-finance-001-service/dev-c-assets-finance-001-service">
                        
            <dependency-type>uses</dependency-type>
                    
        </dependent-module>
                        
        
        <property name="context-root" value="c-assets-finance-001"/>
                        
        
        <property name="java-output-path" value="/c-assets-finance-001-bootstrap/target/classes"/>
                    
    
    </wb-module>
        

</project-modules>
