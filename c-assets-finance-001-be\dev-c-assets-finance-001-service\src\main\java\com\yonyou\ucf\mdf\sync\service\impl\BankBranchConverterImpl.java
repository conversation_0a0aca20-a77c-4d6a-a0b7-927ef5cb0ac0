package com.yonyou.ucf.mdf.sync.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.bank.service.BankBranchService;
import com.yonyou.ucf.mdf.bank.vo.BankBranchVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankBranch;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.BankBranchConverter;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

/**
 * 银行网点数据转换器实现类
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Service
public class BankBranchConverterImpl implements BankBranchConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private EhCacheUtil ehCacheUtil;
	@Autowired
	private BankBranchService bankBranchService;

	@Override
	public BankBranchVO convert(KingdeeBankBranch kingdeeBankBranch) {
		if (kingdeeBankBranch == null) {
			return null;
		}

		BankBranchVO bankBranchVO = new BankBranchVO();
		// 设置操作标识为新增
		bankBranchVO.set_status(SyncActionEnum.INSERT.getValue());

		// 根据编码获取银行网点id缓存（如果存在，则是更新）
		String bankBranchId = (String) ehCacheUtil.getFail(CacheConstant.CACHE_BANK_BRANCH_ID,
				kingdeeBankBranch.getNumber());
		if (bankBranchId == null) {
			// 从数据库获取
			bankBranchId = bankBranchService.findIdByCode(kingdeeBankBranch.getNumber());
		}
		if (bankBranchId != null) {
			bankBranchVO.setId(bankBranchId);
			// 更新
			bankBranchVO.set_status(SyncActionEnum.UPDATE.getValue());
			ehCacheUtil.putValue(CacheConstant.CACHE_BANK_BRANCH_ID, kingdeeBankBranch.getNumber(), bankBranchId);
		}

		// 先设置默认值
		defaultValueUtil.setDefaultValue(bankBranchVO, SyncTypeEnum.BANK_BRANCH);

		// 设置基本信息
		bankBranchVO.setCode(kingdeeBankBranch.getNumber());
		bankBranchVO.setName(kingdeeBankBranch.getName());

		// 设置来源标识
		bankBranchVO.setSourceUnique(kingdeeBankBranch.getNumber());

		return bankBranchVO;
	}
}