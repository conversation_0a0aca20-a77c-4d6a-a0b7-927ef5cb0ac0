package com.yonyou.ucf.mdf.sync.publisher;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankAccount;
import com.yonyou.ucf.mdf.sync.event.BankAccountSyncEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 企业银行账户同步事件发布者
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class BankAccountSyncEventPublisher {

	@Autowired
	private RingBuffer<BankAccountSyncEvent> ringBuffer;

	/**
	 * 发布同步事件
	 * 
	 * @param kingdeeBankAccount 金蝶企业银行账户
	 */
	public void publish(KingdeeBankAccount kingdeeBankAccount) {

		try {
			long sequence = ringBuffer.next();
			try {
				BankAccountSyncEvent event = ringBuffer.get(sequence);
				event.setKingdeeBankAccount(kingdeeBankAccount);
			} finally {
				ringBuffer.publish(sequence);
			}
		} catch (Exception e) {
			log.error("发布企业银行账户同步事件成功，企业银行账户编码：{}", kingdeeBankAccount.getNumber(), e);
		}

	}
}