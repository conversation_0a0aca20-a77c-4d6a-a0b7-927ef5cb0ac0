package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.Arrays;
import java.util.List;

import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.imeta.orm.schema.SimpleCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.sync.model.BankBranchSyncFailRecord;
import com.yonyou.ucf.mdf.sync.service.BankBranchSyncFailRecordService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 银行网点同步失败记录服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Service
public class BankBranchSyncFailRecordServiceImpl implements BankBranchSyncFailRecordService {

	@Autowired
	private IBillCommonRepository billCommonRepository;
	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IBillQueryRepository billQryRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SuppressWarnings("unchecked")
	@Override
	public void saveFailRecord(BankBranchSyncFailRecord failRecord) {
		RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect(
					"id,code,name,kingdeeData,retryCount,errMsg,errStack,requestData,responeData,businessDate");
			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(failRecord.getCode())));
			List<BankBranchSyncFailRecord> result = (List<BankBranchSyncFailRecord>) billQryRepository
					.queryBySchema("cxkingdee-sync.cxkingdee-sync.BankBranchSyncFailRecord", schema);
			if (CollectionUtil.isNotEmpty(result)) {
				BankBranchSyncFailRecord oldRecord = result.get(0);
				failRecord.setId(oldRecord.getId());
				if (oldRecord.getRetryCount() != null) {
					failRecord.setRetryCount(oldRecord.getRetryCount() + 1);
				}
				failRecord.set_status(ActionEnum.UPDATE.getValueInt());
			}
			try {
				List<IBillDO> billDOs = Lists.newArrayList(failRecord);
				billCommonRepository.commonSaveBill(billDOs, "BankBranchSyncFailRecord");
			} catch (Exception e) {
				log.error("保存银行网点同步失败记录报错！", e);
			}
		});
	}

	@Override
	public void deleteByCode(String code) {
		RobotExecutors.runAs(tenantId, () -> {
			try {
				billRepository.batchRemove("cxkingdee-sync.cxkingdee-sync.BankBranchSyncFailRecord",
						Arrays.asList(new SimpleCondition("code", ConditionOperator.eq, code)));
			} catch (Exception e) {
				log.error("删除银行网点同步失败记录报错！" + e.getMessage(), e);
			}
		});
	}

}