package com.yonyou.ucf.mdf.sync.model;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 凭证同步日志
 * @Date 2025-06-04 16:56:01
 * @since 2023/11/28
 **/
@YMSEntity(name = "cxkingdee-sync.cxkingdee-sync.VoucherSyncLog", domain = "c-assets-finance-001")
public class VoucherSyncLog extends SuperDO {
	public static final String ENTITY_NAME = "cxkingdee-sync.cxkingdee-sync.VoucherSyncLog";
	public static final String VOUCHERNUMBER = "voucherNumber";
	public static final String VOUCHERID = "voucherId";
	public static final String COMPANYNUMBER = "companyNumber";
	public static final String COMPANYNAME = "companyName";
	public static final String PERIOD = "period";
	public static final String SUCCESS = "success";
	public static final String ERRMSG = "errMsg";
	public static final String COSTTIME = "costTime";
	public static final String KINGDEEDATA = "kingdeeData";
	public static final String REQUESTDATA = "requestData";
	public static final String RESPONEDATA = "responeData";
	public static final String ERRSTACK = "errStack";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 凭证号 */
	private String voucherNumber;
	/* 凭证id */
	private String voucherId;
	/* 公司编号 */
	private String companyNumber;
	/* 公司名称 */
	private String companyName;
	/* 会计期间 */
	private String period;
	/* 成功标识 */
	private String success;
	/* 错误信息 */
	private String errMsg;
	/* 耗时 */
	private Integer costTime;
	/* 金蝶凭证数据 */
	private String kingdeeData;
	/* 请求凭证保存数据 */
	private String requestData;
	/* 凭证接口保存返回数据 */
	private String responeData;
	/* 错误堆栈 */
	private String errStack;
	/* 单据日期 */
	private String businessDate;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setVoucherNumber(String voucherNumber) {
		this.voucherNumber = voucherNumber;
	}

	public void setVoucherId(String voucherId) {
		this.voucherId = voucherId;
	}

	public void setCompanyNumber(String companyNumber) {
		this.companyNumber = companyNumber;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public void setCostTime(Integer costTime) {
		this.costTime = costTime;
	}

	public void setKingdeeData(String kingdeeData) {
		this.kingdeeData = kingdeeData;
	}

	public void setRequestData(String requestData) {
		this.requestData = requestData;
	}

	public void setResponeData(String responeData) {
		this.responeData = responeData;
	}

	public void setErrStack(String errStack) {
		this.errStack = errStack;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getVoucherNumber() {
		return voucherNumber;
	}

	public String getVoucherId() {
		return voucherId;
	}

	public String getCompanyNumber() {
		return companyNumber;
	}

	public String getCompanyName() {
		return companyName;
	}

	public String getPeriod() {
		return period;
	}

	public String getSuccess() {
		return success;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public Integer getCostTime() {
		return costTime;
	}

	public String getKingdeeData() {
		return kingdeeData;
	}

	public String getRequestData() {
		return requestData;
	}

	public String getResponeData() {
		return responeData;
	}

	public String getErrStack() {
		return errStack;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
