package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案联系人信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerContactVO {
    /**
     * 联系人id；新增时不传，更新、删除时必传
     * 示例：123456
     */
    private Long id;

    /**
     * 联系人姓名；必填字段
     */
    private MultiLanguageVO fullName;

    /**
     * 性别；0：未知，1：男，2：女
     * 示例：0
     */
    private Short gender;

    /**
     * 职务
     * 示例：职务
     */
    private String positionName;

    /**
     * 手机号；本字段必须填写区号和手机号，格式为"+区号-手机号" 示例+86-13812121212
     * 示例：本字段必须填写区号和手机号，格式为"+区号-手机号" 示例+86-13812121212
     */
    private String areaCodeMobile;

    /**
     * 备用手机号；本字段是由原手机号字段更名得来，支持输入文本内容
     * 示例：本字段是由原手机号字段更名得来，支持输入文本内容
     */
    private String mobile;

    /**
     * 固定电话
     * 示例：固定电话
     */
    private String telePhone;

    /**
     * 邮箱
     * 示例：邮箱
     */
    private String email;

    /**
     * QQ
     * 示例：QQ
     */
    private String qq;

    /**
     * 微信
     * 示例：微信
     */
    private String weChat;

    /**
     * 备注
     * 示例：备注
     */
    private String remarks;

    /**
     * 默认联系人；必填；联系人信息的默认值只能设置一个；true：是；false：否
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案联系人特征
     */
    private Object contacterCharacter;

    /**
     * 客户档案联系人信息实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;

    /**
     * 来源数据唯一标识
     * 示例：123456
     */
    private String sourceUnique;
} 