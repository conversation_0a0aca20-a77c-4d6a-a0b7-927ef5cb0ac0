package com.yonyou.ucf.mdf.kingdee.eas.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.eas.util.KingdeeEasUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 金蝶EAS接口服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KingdeeEasApiServiceImpl implements KingdeeEasApiService {

	@Autowired
	private KingdeeEasUtil kingdeeEasUtil;

	@Override
	public String callEasBaseApi(KingdeeEasServiceEnum serviceEnum, String methodName, Object params) {
		log.info("调用金蝶EAS接口开始，服务名称：{}，方法名称：{}，参数：{}", serviceEnum.getDescription(), methodName, JSON.toJSONString(params));
		try {
			String jsonStr = JSON.toJSONString(params);
			String result = kingdeeEasUtil.doOperation(jsonStr, methodName, serviceEnum.getWsdlUrl());
			log.info("调用金蝶EAS接口成功，返回结果：{}", result);
			return result;
		} catch (Exception e) {
			log.error("调用金蝶EAS接口失败，服务名称：{}，方法名称：{}，参数：{}", serviceEnum.getDescription(), methodName, JSON.toJSONString(params), e);
			throw new RuntimeException("调用金蝶EAS接口失败", e);
		}
	}

	@Override
	public <T> T callEasBaseApi(KingdeeEasServiceEnum serviceEnum, String methodName, Object params, Class<T> responseType) {
		String result = callEasBaseApi(serviceEnum, methodName, params);
		try {
			return JSON.parseObject(result, responseType);
		} catch (Exception e) {
			log.error("解析金蝶EAS接口返回结果失败，返回结果：{}", result, e);
			throw new RuntimeException("解析金蝶EAS接口返回结果失败", e);
		}
	}
}