package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案开票客户
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerInvoicingVO {
    /**
     * 客户档案开票客户子表id，更新和删除时必填
     * 示例：123456
     */
    private Long id;

    /**
     * 客户档案开票客户子表开票客户；填写开票客户编码；必填
     * 示例：开票客户
     */
    private String invoicingCustomersIdCode;

    /**
     * 是否默认开票客户；必填；true：是，false：否；默认开票客户只能并且必须设置一个
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案开票客户实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;
} 