package com.yonyou.ucf.mdf.sync.util;

import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.KingdeeSyncConfigService;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据同步设置同步数据默认值工具类
 * 
 * <AUTHOR>
 *
 *         2025年5月29日
 */
@Slf4j
@Component
public class DefaultValueUtil {

	@Autowired
	private KingdeeSyncConfigService configService;

	/**
	 * 设置字段默认值
	 * 
	 * @param obj
	 * @param syncType
	 */
	public void setDefaultValue(Object obj, SyncTypeEnum syncType) {
		List<Map<String, Object>> defualtValueCfg = configService.queryDefualtValueCfg(syncType);
		if (CollectionUtils.isNotEmpty(defualtValueCfg)) {
			for (Map<String, Object> config : defualtValueCfg) {
				if (StringUtils.isNotBlank(config.getOrDefault("config_key", "").toString())) {
					try {
						BeanUtils.setProperty(obj, config.getOrDefault("config_key", "").toString(),
								config.getOrDefault("config_value", "").toString());
					} catch (Throwable e) {
						log.error("根据数据同步系统配置，设置{}默认字段值报错！", syncType.getName(), e);
					}
				}
			}
		}
	}

	/**
	 * 资产同步设置字段默认值
	 * 
	 * @param obj
	 * @param syncType
	 */
	public void setAssetSyncDefaultValue(Object obj, FixedAssetSyncTypeEnum fixedAssetSyncType) {
		Map<String, List<Map<String, Object>>> allCfg = configService.queryAllCfg(SyncTypeEnum.FIX_ASSET);
		if (allCfg != null && !allCfg.isEmpty()) {
			String defaultKey = "";
			switch (fixedAssetSyncType) {
			case ADD:
				defaultKey = "资产新增默认字段";
				break;
			case CHANGE:
				defaultKey = "资产变更默认字段";
				break;
			case CLEAR:
				defaultKey = "资产清理默认字段";
				break;
			case DISPATCH:
				defaultKey = "资产调拨默认字段";
				break;
			default:
				throw new IllegalArgumentException("Unexpected value: " + fixedAssetSyncType);
			}
			List<Map<String, Object>> defualtValueCfg = allCfg.get(defaultKey);
			if (CollectionUtils.isNotEmpty(defualtValueCfg)) {
				for (Map<String, Object> config : defualtValueCfg) {
					if (StringUtils.isNotBlank(config.getOrDefault("config_key", "").toString())) {
						try {
							BeanUtils.setProperty(obj, config.getOrDefault("config_key", "").toString(),
									config.getOrDefault("config_value", "").toString());
						} catch (Throwable e) {
							log.error("根据数据同步系统配置，设置资产{}默认字段值报错！", fixedAssetSyncType.getDescription(), e);
						}
					}
				}
			}
		}
	}

}
