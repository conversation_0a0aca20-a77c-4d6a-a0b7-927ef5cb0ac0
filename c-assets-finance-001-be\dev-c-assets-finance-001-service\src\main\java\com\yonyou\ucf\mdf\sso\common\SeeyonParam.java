package com.yonyou.ucf.mdf.sso.common;

import java.io.Serializable;

/**
 * @className: SeeyonParam
 * @author: wjc
 * @date: 2025/5/20 10:33
 * @Version: 1.0
 * @description:
 */
public class SeeyonParam implements Serializable {
    /***用户名 致远颁发给第三方的用于查询接口的用户名*/
    private String userName;
    /***密码 致远颁发给第三方的用于查询接口的密码*/
    private String password;
    /***系统注册编码，致远提供*/
    private String seeyonregisterCode;
    /***致远调用接口域名*/
    private String seeyonUrl;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSeeyonregisterCode() {
        return seeyonregisterCode;
    }

    public void setSeeyonregisterCode(String seeyonregisterCode) {
        this.seeyonregisterCode = seeyonregisterCode;
    }

    public String getSeeyonUrl() {
        return seeyonUrl;
    }

    public void setSeeyonUrl(String seeyonUrl) {
        this.seeyonUrl = seeyonUrl;
    }
}
