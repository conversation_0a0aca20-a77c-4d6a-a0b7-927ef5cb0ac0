#spring基本配置--start
spring.application.name=c-assets-finance-001
spring.domain.name=DEV
#spring基本配置--end

#应用配置信息--start
spring.profiles.active=test
access.key=fuaJUwsE69mccrCl
access.secret=evQeAcSH8jm2UysqVNbsNvDEG4a0C8
#应用配置信息--end

#yms配置--start
#需要配置yms控制台注册的编码
yms.application.name=c-assets-finance-001
yms.redis.enable=true
yms.redis.code=c-assets-finance-001_redis
yms.redis.code.pub=c-assets-finance-001_redis_pub
yms.datasource.enable=true
yms.datasource.code=mainDataSource
yms.lock.redisClientCode=c-assets-finance-001_redis
iuap.boot.redis.refs.defaultDef=c-assets-finance-001_redis
#yms配置--end

#环境变量配置--start
#是否启用友互通
openYht=true
#用户id，租户id等是否都仅使用友互通id
onlyUseYhtTenant=true
#环境变量配置--start

#UI元数据配置--start
mdd.uimeta.prop.isMetaServer=true
#UI元数据统一存储
mdd.ui.tpl.domain=@uimetadata.domain@/mdf
#UI元数据配置--end

#iris配置--start
MWCLIENT_ENABLE=true
mw_profiles_active=test
#app.version=dev_wuzq
#iris配置--end

#meta元数据--start
metadata.api.url=@metadata.api.domain@
#缓存通知配置
pipelinecode=@metadata.pipelinecode@
# 重新开启实体元数据本地缓存 600s
metadata.cache.timeout=72000
metadata.cache.maximumsize=500000
metadata.ui.url=@uimetadata.domain@
#meta元数据--end

#打印服务配置--start
#打印服务鉴权文件: linux和mac直接创建指定文件; windows 在idea或eclipse安装目录所在磁盘分区的根路径创建app目录
u8cprint.client.credential.path=printauth.properties
print.client.credential.path=printauth.properties
print.entrance.name=@print.entrance.name@
printBaseUrl=@printBaseUrl@
UAP.AUTH.ALG=HMAC
UAP.KDF.PRF=HmacSHA1
#打印服务配置--end

#审批流配置  --start
bpmrest.appsource=c-assets-finance-001
bpmrest.server=${domain.url}/iuap-apcom-workflow
#审批流配置  --end

#ES配置--begin
ucf.intellis.endpoint=@ucf.search.endpoint@
ucf.intellis.tenantId.default=@ucf.search.tenantId.default@
ucf.search.token.default=@ucf.search.token.default@
#ES配置--end

#yonscript--start
ublinker.env=pre
#外部调试器服务地址
J2V8_DEBUGER_HOST= @J2V8_DEBUGER_HOST@
#j2v8 debug server 超时时间ms 10min
J2V8_DEBUGER_TIMEOUT = 600000
hpapaas-passport-be.host=@passport.domain@
#调试句柄过期时间（秒）每个句柄一小时的占用时间
debug_handler_expire=3600
#调试句柄标记使用的redis
#redis_url=direct://@spring.redis.host@:@spring.redis.port@?poolSize=50&poolName=mypool&password=@spring.redis.password@
#yonscript--end


#分布式锁--start
zklock.url=@zk.host@:@zk.port@
zookeeper-address=@zk.host@:@zk.port@
#分布式锁--end

#业务流--start
bizFlow.url=@bizFlow.url@
bizFlow.contextUrl=@bizFlow.contextUrl@
#业务流--end

#单据编号--start
#设置是否启用流水号缓存，默认启用
billcode.cacheenable=true
#表示缓存的最大数据量，默认10
billcode.cachemax=10
#表示缓存的最小数据量，默认1
billcode.cachemin=1
#设置租户模式，1：友互通 2：营销云(默认)
billcode.tenantmode=1
#找不到编码规则时默认自动编号
billcode.nobillnumber.showdefault=true
# 编码规则如果没有预制候选属性，可以直接显示UI元数据里的候选属性
billcode.noprefabricate.showUImeta=true
#单据编号--end

#spring其他配置--start
# 文件上传大小限制
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB
#国际化多语配置
spring.messages.basename=i18n/messages
spring.messages.cache-duration=3600
spring.messages.encoding=UTF-8
#编码
spring.http.encoding.force=true
spring.http.encoding.charset=utf-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8
spring.main.allow-bean-definition-overriding=true
# actuator的访问路径
management.endpoints.web.base-path=/manage
# 上下文路径 配置的情况访问需要用 ip:port/mdf/manage/health
management.server.servlet.context-path=/mdf
#server 地址 设置127.0.0.1后 不允许远程管理连接:
#management.server.address=127.0.0.1
# 管理的端口调整成1234  如果不希望通过HTTP公开端点，则可以将管理端口设置为-1
management.server.port=-1
#关闭默认启用模式
management.endpoints.enabled-by-default = false
management.endpoint.info.enabled = true
management.endpoint.health.enabled = true
#http://ip:port/demo-prometheus/actuator/prometheus
management.endpoint.prometheus.enabled=true
management.endpoint.health.show-details=always
#暴露 端点配置 info,health,prometheus,custom
management.endpoints.web.exposure.include=info,health,prometheus,custom
#关闭默认配置的db检查
management.health.db.enabled=false
#关闭默认配置的redis检查
management.health.redis.enabled=false
# shutdown 可以关闭制定的端点
management.endpoint.shutdown.enabled=false
#groupby 适配多数据库的强约束配置
mdd.groupby.compatible=true
#导入service
import.invalid.handle.data.save=importSaveServiceImpl
reportGroup=c-assets-finance-001
#多语数据返回对象开关
mdd.i18n.enable=true
runtime.server.url=${domain.url}/c-assets-finance-001
A47.redis.ref=c-assets-finance-001_redis
redis.mainIndex=1
domain.iuap-apcom-i18n=${domain.url}/iuap-apcom-i18n
# mdf在协同对应的appcode
mdf.appcode=c-assets-finance-001
#参照收藏功能开关
ypd.open.refHot=true
mdd.cooperation.business.line=c-assets-finance-001

#按照正则排除不需要恢复上下文路径 新的正则需要带上原来默认正则
yms.session.ymsSessionWhite="^.*(/ping/?)$"
# 按照后缀排除不需要恢复上下文路径 以;分割
yms.session.whiteListSuffixs=".css;.js;.jpg;.png;.ttf;.ico;.html;.jsx;.gif"
# 包含方式：以;分割
yms.session.whiteListContain=/ping/;health;/extend/healthycheck
#按照正则排除
yms.session.greyListRegex="^.*(/ping/?)$"
#按照后缀排除 以;分割
yms.session.greyListSuffixs=".css;.js;.jpg;.png;.ttf;.ico;.html;.jsx;.gif"
#包含方式：以;分割
yms.session.greyListContain="/ping/;health;/rest/"
#越权找不到action时使用commoncommand
ypd.custom.defaultCheckCommonCommandAction=true
mdd.import.event.v2.switch=true

#oepnAPI信息配置
app.gateway-address-url=${domain.url}/iuap-api-auth/open-auth/dataCenter/getGatewayAddress?tenantId=${app.tenantId}
app.open-api.config={"default":{"appKey":"${bip.open-api.appKey}","appSecret":"${bip.open-api.appSecret}"}}