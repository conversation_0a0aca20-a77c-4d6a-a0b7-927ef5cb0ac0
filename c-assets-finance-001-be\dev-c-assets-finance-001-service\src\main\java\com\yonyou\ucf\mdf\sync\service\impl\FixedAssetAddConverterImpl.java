package com.yonyou.ucf.mdf.sync.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetAddParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetAddParam.FixedAssetEntry;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.FixedAssetAddConverter;
import com.yonyou.ucf.mdf.sync.service.KingdeeSyncConfigService;
import com.yonyou.ucf.mdf.sync.util.CodeTranslator;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;

/**
 * <AUTHOR>
 *
 *         2025年6月11日
 */
@Service
public class FixedAssetAddConverterImpl implements FixedAssetAddConverter {

	@Autowired
	private CodeTranslator codeTranslator;
	@Autowired
	private KingdeeSyncConfigService kingdeeSyncConfigService;
	@Autowired
	private DefaultValueUtil defaultValueUtil;

	@Override
	public FixedAssetAddParam convert(BizObject param) {
		if (param == null) {
			return null;
		}

		FixedAssetAddParam fixedAssetAddParam = new FixedAssetAddParam();
		fixedAssetAddParam.setNumber(param.getString("equip_code")); // 单据号（取BIP资产编码）
		Date audittime = param.getDate("audittime"); // 审批通过时间
		if (audittime != null) {
			fixedAssetAddParam.setPeriodId(new SimpleDateFormat("yyyyMM").format(audittime));
		}
		String pk_org = param.getString("pk_org"); // 资产组织
		if (StringUtils.isNotBlank(pk_org)) {
			fixedAssetAddParam.setCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
			fixedAssetAddParam.setControlUnitID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
		}

		FixedAssetEntry entry = new FixedAssetEntry();
		defaultValueUtil.setAssetSyncDefaultValue(entry, FixedAssetSyncTypeEnum.ADD); // 先设置默认值
		BizObject equip_name = param.getBizObject("equip_name", BizObject.class);
		if (equip_name != null) {
			entry.setZcmc(equip_name.getString("zh_CN"));
		}
		Object equip_num = param.get("equip_num"); // 数量
		if (equip_num != null) {
			entry.setZcsl(new BigDecimal(equip_num.toString()));
		}
		BizObject userDefines = param.getBizObject("userDefines", BizObject.class);
		if (userDefines != null) {
			Date ZCKP_03 = userDefines.getDate("ZCKP_03");
			if (ZCKP_03 != null) {
				entry.setYsrq(new SimpleDateFormat("yyyy-MM-dd").format(ZCKP_03)); // 验收日期
			}
			String ZCKP_07 = userDefines.getString("ZCKP_07");
			if (StringUtils.isNotBlank(ZCKP_07)) {
				entry.setJldwId(ZCKP_07); // 计量单位ID
			}
			Date ZCKP_08 = userDefines.getDate("ZCKP_08");
			if (ZCKP_08 != null) {
				entry.setSwrzrq(new SimpleDateFormat("yyyy-MM-dd").format(ZCKP_08)); // 实物入账日期
			}
		}
		String pk_capital_source = param.getString("pk_capital_source"); // 资金来源方式
		if (StringUtils.isNotBlank(pk_capital_source)) {
			entry.setZjlyfsId(codeTranslator.translate(MappingTypeEnum.CAPITAL_SOURCE_MAPPING, pk_capital_source)); // 资金来源方式ID
		}
		Date creationtime = param.getDate("creationtime");
		if (creationtime != null) {
			entry.setCwrzrq(new SimpleDateFormat("yyyy-MM-dd").format(creationtime)); // 财务入账日期
		}
		String pk_mandept = param.getString("pk_mandept");
		if (StringUtils.isNotBlank(pk_mandept)) {
			// 转换成编码
			entry.setGlbmId(codeTranslator.translate(MappingTypeEnum.DEPT_MAPPING, pk_mandept)); // 管理部门ID
		}
		String spec = param.getString("spec");
		String model = param.getString("model");
		entry.setGgxh(concatStrings(spec, model)); // 规格型号
		String pk_supplier = param.getString("pk_supplier");
		if (StringUtils.isNotBlank(pk_supplier)) {
			entry.setSupplierId(codeTranslator.translate(MappingTypeEnum.VENDOR_MAPPING, pk_supplier)); // 供应商ID
		}
		String pk_user = param.getString("pk_user");
		if (StringUtils.isNotBlank(pk_user)) {
			entry.setUserId(codeTranslator.translate(MappingTypeEnum.STAFF_MAPPING, pk_user)); // 使用人ID
		}
		String pk_usedept = param.getString("pk_usedept");
		if (StringUtils.isNotBlank(pk_usedept)) {
			entry.setSybmId(codeTranslator.translate(MappingTypeEnum.DEPT_MAPPING, pk_usedept)); // 使用部门ID
		}

		String pk_currency_fi = param.getString("pk_currency_fi");
		if (StringUtils.isNotBlank(pk_currency_fi)) {
			entry.setCurrencyId(getCurrency(pk_currency_fi)); // 币别ID
		}
//		entry.setHl(null); // 汇率
		Object origin_value = param.get("origin_value");
		if (origin_value != null) {
			entry.setYbje(new BigDecimal(origin_value.toString())); // 资产价值(含税)
		}
		if (userDefines != null) {
			Object ZCKP_09 = userDefines.get("ZCKP_09");
			if (ZCKP_09 != null) {
				entry.setZcyz(new BigDecimal(ZCKP_09.toString())); // 资产原值
			}
		}
		String pk_category = param.getString("pk_category");
		if (StringUtils.isNotBlank(pk_category)) {
			entry.setZclbId(codeTranslator.translate(MappingTypeEnum.ASSET_CATEGORY_MAPPING, pk_category)); // 资产类别ID
		}
		// TODO 还没有找到对应字段
//		entry.setLyfsId(null); // 来源方式ID（使用默认配置）
		// TODO 存放地点无法对照，暂时不传
//		String pk_location = param.getString("pk_location");
//		if (StringUtils.isNotBlank(pk_location)) {
//			entry.setCfddId(codeTranslator.translate(MappingTypeEnum.LOCATION_MAPPING, pk_location)); // 存放地点ID
//		}
//		entry.setSyztId(null); // 使用状态ID（使用默认配置）
//		entry.setJjytId(null); // 经济用途ID（使用默认配置）

		fixedAssetAddParam.setEntry(Collections.singletonList(entry));

		return fixedAssetAddParam;
	}

	private String getCurrency(String pk_currency_fi) {
		String currencyCode = codeTranslator.translate(MappingTypeEnum.CURRENCY_MAPPING, pk_currency_fi);
		if (StringUtils.isBlank(currencyCode)) {
			return pk_currency_fi;
		}
		Map<String, List<Map<String, Object>>> allConfig = kingdeeSyncConfigService.queryAllCfg(SyncTypeEnum.FIX_ASSET);
		if (allConfig == null || allConfig.isEmpty()) {
			return currencyCode;
		}
		List<Map<String, Object>> config = allConfig.get("币种编码映射");
		if (config == null || config.isEmpty()) {
			return currencyCode;
		}
		for (Map<String, Object> map : config) {
			if (currencyCode.equals(map.getOrDefault("config_key", "").toString())) {
				return map.getOrDefault("config_value", "").toString();
			}
		}
		return currencyCode;
	}

	public String concatStrings(String a, String b) {
		if (isValid(a) && isValid(b)) {
			return new StringJoiner("&").add(a).add(b).toString();
		}
		return isValid(a) ? a : isValid(b) ? b : null;
	}

	private boolean isValid(String s) {
		return s != null && !s.isEmpty();
	}

}
