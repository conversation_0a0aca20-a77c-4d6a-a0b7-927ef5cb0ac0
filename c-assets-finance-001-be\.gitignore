.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.DS_Store
# Avoid ignoring Maven wrapper jar file (.jar files are usually ignored)
!/.mvn/wrapper/maven-wrapper.jar
# Compiled class file
*.class
.classpath
.project
.settings
# Log file
*.log
*.0.gz
*.1.gz
*.2.gz
*.3.gz
# BlueJ files
*.ctxt
log.txt
# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.ear
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio and Webstorm
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# User-specific stuff:
.idea/**/workspace.xml
.idea/**/tasks.xml
.idea/dictionaries

# Sensitive or high-churn files:
.idea/**/dataSources/
.idea/**/dataSources.ids
.idea/**/dataSources.xml
.idea/**/dataSources.local.xml
.idea/**/sqlDataSources.xml
.idea/**/dynamic.xml
.idea/**/uiDesigner.xml

# Gradle:
.idea/**/gradle.xml
.idea/**/libraries

# CMake
cmake-build-debug/

# Mongo Explorer plugin:
.idea/**/mongoSettings.xml

*.iws
.idea/*

*.ipr
*/*.iml

# IntelliJ
/out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties
.idea/checkstyle-idea.xml
.idea/checkstyleidea-libs/
.idea/compiler.xml
.idea/encodings.xml
.idea/inspectionProfiles/
.idea/misc.xml
.idea/modules.xml
.idea/vcs.xml
*.iml
.idea
/hpapaas-passport-bootstrap/span/
/hpapaas-passport-bootstrap/log/
/hpapaas-passport-bootstrap/monitor/
/hpapaas-passport-bootstrap/disconf/
disconf/*
/hpapaas-passport-bootstrap/disconf/download/mwclient/hpapaas-passport-be/combine/1.0.0/authority.json
/hpapaas-passport-bootstrap/disconf/download/mwclient/hpapaas-passport-be/combine/1.0.0/authority.json.lock
/hpapaas-passport-bootstrap/disconf/download/mwclient/hpapaas-passport-be/combine/1.0.0/cbdegrade.json
/hpapaas-passport-bootstrap/disconf/download/mwclient/hpapaas-passport-be/combine/1.0.0/cbdegrade.json.lock
/hpapaas-passport-bootstrap/disconf/download/mwclient/hpapaas-passport-be/combine/1.0.0/strategy.json
/hpapaas-passport-bootstrap/disconf/download/mwclient/hpapaas-passport-be/combine/1.0.0/strategy.json.lock
/.metadata/
