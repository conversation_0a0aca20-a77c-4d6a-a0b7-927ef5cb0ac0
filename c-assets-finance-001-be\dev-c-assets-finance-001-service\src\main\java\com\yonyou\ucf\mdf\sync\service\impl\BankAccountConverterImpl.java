package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.bank.vo.BankAccountCurrencyVO;
import com.yonyou.ucf.mdf.bank.vo.BankAccountVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankAccount;
import com.yonyou.ucf.mdf.org.service.OrgQryService;
import com.yonyou.ucf.mdf.org.vo.OrgVO;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.BankAccountConverter;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

/**
 * <AUTHOR>
 *
 *         2025年6月3日
 */
@Component
public class BankAccountConverterImpl implements BankAccountConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private EhCacheUtil ehCacheUtil;
	@Autowired
	private OrgQryService orgQryService;

	@Override
	public BankAccountVO convert(KingdeeBankAccount kingdeeBankAccount) {
		if (kingdeeBankAccount == null) {
			return null;
		}

		BankAccountVO bankAccountVO = new BankAccountVO();
		// 设置操作标识为新增
		bankAccountVO.set_status(SyncActionEnum.INSERT.getValue());

		// 先设置默认值
		defaultValueUtil.setDefaultValue(bankAccountVO, SyncTypeEnum.BANK_ACCOUNT);

		// 设置基本信息
		bankAccountVO.setCode(kingdeeBankAccount.getNumber());
		bankAccountVO.setName(kingdeeBankAccount.getName());

		OrgVO orgVO = queryOrgByKingdeeCode(kingdeeBankAccount.getCompanyNum());
		if (orgVO != null) {
			bankAccountVO.setOrgid(orgVO.getId());
		}

		bankAccountVO.setAccount(kingdeeBankAccount.getBankAccountNumber());
		bankAccountVO.setBankNumber(kingdeeBankAccount.getBankNum());
		if (StringUtils.isNotBlank(kingdeeBankAccount.getAcctname())) {
			bankAccountVO.setAcctName(kingdeeBankAccount.getAcctname());
		} else {
			bankAccountVO.setAcctName(kingdeeBankAccount.getCompanyName());
		}

		// bankAccountVO.setDescription(""); // 这个是必填的，但是金蝶没返回

		// 设置来源标识
		bankAccountVO.setSourceUnique(kingdeeBankAccount.getNumber());

		BankAccountCurrencyVO currency = new BankAccountCurrencyVO();
		currency.set_status(SyncActionEnum.INSERT.getValue());
		currency.setCurrency("CNY");

		bankAccountVO.setCurrencyList(Collections.singletonList(currency));

		return bankAccountVO;
	}

	/**
	 * 根据金蝶组织编码，查询本地系统组织
	 * 
	 * @param code
	 * @return
	 */
	private OrgVO queryOrgByKingdeeCode(String code) {

		if (StringUtils.isBlank(code)) {
			return null;
		}

		OrgVO orgVO = (OrgVO) ehCacheUtil.getValue(CacheConstant.CACHE_ORG, code);

		if (orgVO != null) {
			return orgVO;
		}
		orgVO = orgQryService.queryByCode(code);
		if (orgVO != null) {
			ehCacheUtil.putValue(CacheConstant.CACHE_ORG, code, orgVO);
		} else {
			throw new RuntimeException(String.format("根据金蝶组织编码【%s】没有查询到对应组织！", code));
		}
		return orgVO;
	}

}
