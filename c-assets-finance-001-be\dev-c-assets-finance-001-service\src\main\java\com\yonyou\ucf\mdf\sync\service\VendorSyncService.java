package com.yonyou.ucf.mdf.sync.service;

import java.time.LocalDateTime;
import java.util.List;

import com.yonyou.ucf.mdf.kingdee.vo.KingdeeVendor;

/**
 * 供应商同步服务
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
public interface VendorSyncService {
    
    /**
     * 同步供应商数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncVendor(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 发布供应商同步事件
     * 
     * @param kingdeeVendors 金蝶供应商数据列表
     */
    void publishEvent(List<KingdeeVendor> kingdeeVendors);
} 