package com.yonyou.ucf.mdf.voucher.model;

import java.util.List;

/**
 * 凭证保持接口凭证实体类
 * <AUTHOR>
 *
 * 2025年5月14日
 */
import lombok.Data;

@Data
public class Voucher {

	/**
	 * 来源系统code(总账管理：figl，产品成本：fipcm，固定资产：assets，
	 * 汇兑损益：Revaluation，商业汇票：drft，现金管理：cmp，
	 * 应收管理：fiar，财务折算：figl_convert，期末结转：endcarry， 存货核算：fiia，产成品制造：fipcm，应付管理：fiap，
	 * 人力资源：hr_cloud，费控服务：znbz，成本中心：ficc，
	 * 损益结转：figl_profitaloss，产品成本（废弃）：ca，费用管理：fier) 默认值：figl
	 */
	private String srcSystemCode = "figl";

	/**
	 * 账簿code
	 */
	private String accbookCode;

	/**
	 * 凭证类型code
	 */
	private String voucherTypeCode;

	/**
	 * 凭证号（传入空时，系统自增）
	 */
	private Integer billCode;

	/**
	 * 附单据数 默认值：0
	 */
	private Integer attachedBill = 0;

	/**
	 * 制单人手机号（手机号和邮箱不能同时为空）
	 */
	private String makerMobile;

	/**
	 * 制单人邮箱（手机号和邮箱不能同时为空）
	 */
	private String makerEmail;

	/**
	 * 制单日期(默认当前系统时间) 格式为yyyy-MM-dd
	 */
	private String makeTime;

	/**
	 * 凭证头摘要
	 */
	private String description;

	/**
	 * 自定义扩展信息1
	 */
	private String defInfo1;

	/**
	 * 自定义扩展信息2
	 */
	private String defInfo2;

	/**
	 * 自定义扩展信息3
	 */
	private String defInfo3;

	/**
	 * 自定义扩展信息4
	 */
	private String defInfo4;

	/**
	 * 自定义扩展信息5
	 */
	private String defInfo5;

	/**
	 * 凭证分录
	 */
	private List<VoucherDetail> bodies;
}
