package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.Arrays;
import java.util.List;

import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.imeta.orm.schema.SimpleCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.sync.model.CustomerSyncFailRecord;
import com.yonyou.ucf.mdf.sync.service.CustomerSyncFailRecordService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户同步失败记录服务实现
 * 
 * <AUTHOR>
 *
 *         2025年5月27日
 */
@Slf4j
@Service
public class CustomerSyncFailRecordServiceImpl implements CustomerSyncFailRecordService {

	@Autowired
	private IBillCommonRepository billCommonRepository;
	@Autowired
	private IBillQueryRepository billQryRepository;

	@Autowired
	private IBillRepository billRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SuppressWarnings("unchecked")
	@Override
	public void saveFailRecord(CustomerSyncFailRecord failRecord) {
		RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect(
					"id,code,name,orgCode,orgName,kingdeeData,retryCount,errMsg,errStack,requestData,responeData,businessDate");
			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(failRecord.getCode())));
			List<CustomerSyncFailRecord> result = (List<CustomerSyncFailRecord>) billQryRepository
					.queryBySchema("cxkingdee-sync.cxkingdee-sync.CustomerSyncFailRecord", schema);
			if (CollectionUtil.isNotEmpty(result)) {
				CustomerSyncFailRecord oldRecord = result.get(0);
				failRecord.setId(oldRecord.getId());
				if (oldRecord.getRetryCount() != null) {
					failRecord.setRetryCount(oldRecord.getRetryCount() + 1);
				}
				failRecord.set_status(ActionEnum.UPDATE.getValueInt());
			}
			try {
				List<IBillDO> billDOs = Lists.newArrayList(failRecord);
				billCommonRepository.commonSaveBill(billDOs, "CustomerSyncFailRecord");
			} catch (Exception e) {
				log.error("保存客户同步失败记录报错！", e);
			}
		});
	}

	@Override
	public void deleteByCode(String code) {
		RobotExecutors.runAs(tenantId, () -> {
			try {
				billRepository.batchRemove("cxkingdee-sync.cxkingdee-sync.CustomerSyncFailRecord",
						Arrays.asList(new SimpleCondition("code", ConditionOperator.eq, code)));
			} catch (Exception e) {
				log.error("删除客户同步失败记录报错！" + e.getMessage(), e);
			}
		});
	}

}
