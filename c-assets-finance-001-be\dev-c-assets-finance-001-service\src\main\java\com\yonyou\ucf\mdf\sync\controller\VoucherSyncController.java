package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.sync.model.OrgBookMapping;
import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;
import com.yonyou.ucf.mdf.sync.service.OrgBookMappingService;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncService;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证同步
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@RestController
@RequestMapping("/rest")
public class VoucherSyncController extends BaseSyncController {

	@Autowired
	private VoucherSyncService voucherSyncService;
	@Autowired
	private OrgBookMappingService orgBookMappingService;

	/**
	 * 期间格式：yyyyMM
	 */
	private static final DateTimeFormatter PERIOD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

	/**
	 * 同步凭证任务接口
	 * 
	 * @param syncDate 同步日期参数
	 * @return 任务执行结果
	 * @throws Exception
	 */
	@PostMapping("/voucher/sync")
	public TaskResult syncVoucher(@RequestBody(required = false) SyncDate syncDate) throws Exception {
		LocalDateTime[] timeRange = getSyncTimeRange(syncDate);
		try {
			String fromPeriodNumber = timeRange[0].format(PERIOD_FORMATTER);
			String toPeriodNumber = timeRange[1].format(PERIOD_FORMATTER);
			OrgBookMapping orgBookMapping = null;
			if (StringUtils.isNotBlank(syncDate.getAccountBook())) { // 前端传的是账簿id，这里要转换成金蝶的编码
				orgBookMapping = orgBookMappingService.getByAccountBookId(syncDate.getAccountBook());
				if (orgBookMapping == null || StringUtils.isBlank(orgBookMapping.getKingdeeOrgCode())) {
					throw new RuntimeException("根据所选账簿未查询到对应金蝶公司！");
				}
			}
			if (orgBookMapping != null) {
				voucherSyncService.syncVoucher(fromPeriodNumber, toPeriodNumber, orgBookMapping.getKingdeeOrgCode(),
						syncDate.getVoucherNumber());
			} else {
				List<String> allKingdeeOrgCodes = orgBookMappingService.getAllKingdeeOrgCode();
				if (CollectionUtils.isNotEmpty(allKingdeeOrgCodes)) {
					for (String code : allKingdeeOrgCodes) {
						try {
							voucherSyncService.syncVoucher(fromPeriodNumber, toPeriodNumber, code,
									syncDate.getVoucherNumber());
						} catch (Exception e) {
							log.error("同步凭证数据失败", e);
						}
					}
				}
			}
			return createTaskResult("凭证同步", null);
		} catch (Exception e) {
			throw new RuntimeException("同步凭证报错：" + e.getMessage(), e);
		}
	}

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param rows 同步失败记录列表
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/voucher/selectretry")
	public TaskResult syncVoucherRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				List<KingdeeVoucher> kingdeeVouchers = rows.stream().map(row -> {
					String kingdeeData = row.getString("kingdeeData");
					if (StringUtils.isBlank(kingdeeData)) {
						return null;
					}
					return JsonUtils.parseObject(kingdeeData, new TypeReference<List<KingdeeVoucher>>() {
					});
				}).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());

				if (!kingdeeVouchers.isEmpty()) {
					voucherSyncService.publishEvent(kingdeeVouchers);
				}
			}
			return createTaskResult("凭证同步", null);
		} catch (Exception e) {
			return createTaskResult("凭证同步", e);
		}
	}
}