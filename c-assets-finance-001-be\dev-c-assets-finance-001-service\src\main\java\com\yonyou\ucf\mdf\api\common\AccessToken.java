package com.yonyou.ucf.mdf.api.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/21 08:57
 * @DESCRIPTION Acctoken
 */
@Data
public class AccessToken implements Serializable {

    private static final long serialVersionUID = -7277598349871464738L;
    private GaUrl gaUrl;
    /**
     * 获取的访问令牌 access_token
     */
    @JsonProperty(value = "access_token")
    private String accessToken;

    /**
     * 访问令牌的过期时间，单位秒
     */
    private Integer expire;
}
