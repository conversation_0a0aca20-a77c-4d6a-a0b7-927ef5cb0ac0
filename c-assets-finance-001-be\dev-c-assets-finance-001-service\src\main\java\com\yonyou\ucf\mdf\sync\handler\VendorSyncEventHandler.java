package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeVendor;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.VendorSyncEvent;
import com.yonyou.ucf.mdf.sync.model.VendorSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.VendorSyncLog;
import com.yonyou.ucf.mdf.sync.service.VendorConverter;
import com.yonyou.ucf.mdf.sync.service.VendorSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.VendorSyncLogService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;
import com.yonyou.ucf.mdf.vendor.service.VendorService;
import com.yonyou.ucf.mdf.vendor.vo.VendorBatchResult;
import com.yonyou.ucf.mdf.vendor.vo.VendorVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 供应商同步事件处理器
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VendorSyncEventHandler implements EventHandler<VendorSyncEvent> {

	private final VendorConverter vendorConverter;
	private final VendorService vendorService;
	private final VendorSyncLogService vendorSyncLogService;
	private final VendorSyncFailRecordService vendorSyncFailRecordService;
	private final EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(VendorSyncEvent event, long sequence, boolean endOfBatch) throws Exception {
		KingdeeVendor kingdeeVendor = event.getKingdeeVendor();
		log.info("开始处理供应商同步事件，金蝶供应商：{}，序列号：{}", kingdeeVendor, sequence);
		VendorVO vendorVO = null;
		VendorSyncLog syncLog = null;
		long begin = System.currentTimeMillis();
		try {
			// 1. 转换数据
			vendorVO = vendorConverter.convert(kingdeeVendor);
			if (vendorVO == null) {
				return;
			}

			// 2. 保存数据
			ResponseResult<VendorBatchResult> responseResult = vendorService
					.batchSaveVendor(Collections.singletonList(vendorVO));

			// 3. 记录同步日志
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog = new VendorSyncLog();
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setCode(vendorVO.getCode());
			syncLog.setName(vendorVO.getName().toString());
			syncLog.setOrgCode(vendorVO.getOrg_code());
			syncLog.setOrgName(vendorVO.getOrg_name());
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeVendor));
			syncLog.setRequestData(JSONObject.toJSONString(vendorVO));
			syncLog.setResponeData(JSONObject.toJSONString(responseResult));
			syncLog.setCostTime(costTime);
			if (!"200".equals(responseResult.getCode())) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(responseResult.getMessage());
			} else {
				VendorBatchResult batchResult = responseResult.getData();
				if (batchResult != null) {
					if (batchResult.getFailCount() != null && batchResult.getFailCount() > 0) {
						syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
						if (batchResult.getMessages() != null && !batchResult.getMessages().isEmpty()) {
							syncLog.setErrMsg(batchResult.getMessages().get(0).getMessage());
						}
					} else {
						syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
					}
				} else {
					syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
					syncLog.setErrMsg("调用供应商保存接口，返回数据BatchResult为空，无法判断是否保存成功");
				}
			}

			vendorSyncLogService.logSuccess(syncLog);

			log.info("供应商同步事件处理完成，金蝶供应商：{}，序列号：{}", event.getKingdeeVendor(), sequence);
		} catch (Exception e) {
			log.error("供应商同步事件处理失败，金蝶供应商：{}，序列号：{}", event.getKingdeeVendor(), sequence, e);
			// 记录失败日志
			if (syncLog == null) {
				syncLog = new VendorSyncLog();
			}
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg("供应商同步事件处理失败" + e.getMessage());
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			if (vendorVO != null) {
				syncLog.setCode(vendorVO.getCode());
				syncLog.setName(vendorVO.getName().toString());
				syncLog.setOrgCode(vendorVO.getOrg_code());
				syncLog.setOrgName(vendorVO.getOrg());
				syncLog.setRequestData(JSONObject.toJSONString(vendorVO));
			}
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeVendor));
			syncLog.setCostTime(costTime);
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			vendorSyncLogService.logError(syncLog);
		}

		VendorSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			vendorSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_VENDOR, kingdeeVendor.getNumber(), kingdeeVendor);
			ehCacheUtil.removeFail(CacheConstant.CACHE_VENDOR, kingdeeVendor.getNumber());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_VENDOR, kingdeeVendor.getNumber(), kingdeeVendor);
		}
	}

	/**
	 * 删除同步失败记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(VendorSyncLog syncLog) {
		vendorSyncFailRecordService.deleteByCode(syncLog.getCode());
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private VendorSyncFailRecord convertFailRecord(VendorSyncLog syncLog) {
		if (syncLog == null || SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			return null;
		}
		VendorSyncFailRecord failRecord = new VendorSyncFailRecord();
		failRecord.setCode(syncLog.getCode());
		failRecord.setName(syncLog.getName());
		failRecord.setOrgCode(syncLog.getOrgCode());
		failRecord.setOrgName(syncLog.getOrgName());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setRetryCount(0);
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		return failRecord;
	}
}