package com.yonyou.ucf.mdf.sync.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.lmax.disruptor.ExceptionHandler;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.util.DaemonThreadFactory;
import com.yonyou.ucf.mdf.sync.event.BankAccountSyncEvent;
import com.yonyou.ucf.mdf.sync.event.BankAccountSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.BankBranchSyncEvent;
import com.yonyou.ucf.mdf.sync.event.BankBranchSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.CustomDocDefinitionSyncEvent;
import com.yonyou.ucf.mdf.sync.event.CustomDocDefinitionSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.CustomDocSyncEvent;
import com.yonyou.ucf.mdf.sync.event.CustomDocSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.CustomerSyncEvent;
import com.yonyou.ucf.mdf.sync.event.CustomerSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.FixedAssetSyncEvent;
import com.yonyou.ucf.mdf.sync.event.FixedAssetSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.VendorSyncEvent;
import com.yonyou.ucf.mdf.sync.event.VendorSyncEventFactory;
import com.yonyou.ucf.mdf.sync.event.VoucherSyncEvent;
import com.yonyou.ucf.mdf.sync.event.VoucherSyncEventFactory;
import com.yonyou.ucf.mdf.sync.handler.BankAccountSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.BankBranchSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.CustomDocDefinitionSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.CustomDocSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.CustomerSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.FixedAssetSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.VendorSyncEventHandler;
import com.yonyou.ucf.mdf.sync.handler.VoucherSyncEventHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Disruptor配置类
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class DisruptorConfig {

	/**
	 * RingBuffer大小，必须是2的幂
	 */
	private static final int BUFFER_SIZE = 2048;

	private final CustomerSyncEventHandler customerSyncEventHandler;
	private final VendorSyncEventHandler vendorSyncEventHandler;
	private final BankBranchSyncEventHandler bankBranchSyncEventHandler;
	private final BankAccountSyncEventHandler bankAccountSyncEventHandler;
	private final CustomDocDefinitionSyncEventHandler customDocDefinitionSyncEventHandler;
	private final CustomDocSyncEventHandler customDocSyncEventHandler;
	private final FixedAssetSyncEventHandler fixedAssetSyncEventHandler;
	private final VoucherSyncEventHandler voucherSyncEventHandler;
	private final FixedAssetSyncEventFactory fixedAssetSyncEventFactory;

	/**
	 * 创建异常处理器
	 */
	private <T> ExceptionHandler<T> createExceptionHandler() {
		return new ExceptionHandler<T>() {
			@Override
			public void handleEventException(Throwable ex, long sequence, T event) {
				log.error("处理事件异常，序列号：{}，事件：{}", sequence, event, ex);
			}

			@Override
			public void handleOnStartException(Throwable ex) {
				log.error("启动异常", ex);
			}

			@Override
			public void handleOnShutdownException(Throwable ex) {
				log.error("关闭异常", ex);
			}
		};
	}

	@Bean
	public Disruptor<CustomerSyncEvent> customerSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<CustomerSyncEvent> disruptor = new Disruptor<>(new CustomerSyncEventFactory(), BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(customerSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<CustomerSyncEvent> customerSyncRingBuffer(Disruptor<CustomerSyncEvent> customerSyncDisruptor) {
		return customerSyncDisruptor.getRingBuffer();
	}

	@Bean
	public Disruptor<VendorSyncEvent> vendorSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<VendorSyncEvent> disruptor = new Disruptor<>(new VendorSyncEventFactory(), BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(vendorSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<VendorSyncEvent> vendorSyncRingBuffer(Disruptor<VendorSyncEvent> vendorSyncDisruptor) {
		return vendorSyncDisruptor.getRingBuffer();
	}

	@Bean
	public Disruptor<BankBranchSyncEvent> bankBranchSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<BankBranchSyncEvent> disruptor = new Disruptor<>(new BankBranchSyncEventFactory(), BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(bankBranchSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<BankBranchSyncEvent> bankBranchSyncRingBuffer(
			Disruptor<BankBranchSyncEvent> bankBranchSyncDisruptor) {
		return bankBranchSyncDisruptor.getRingBuffer();
	}

	@Bean
	public Disruptor<BankAccountSyncEvent> bankAccountSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<BankAccountSyncEvent> disruptor = new Disruptor<>(new BankAccountSyncEventFactory(), BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(bankAccountSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<BankAccountSyncEvent> bankAccountSyncRingBuffer(
			Disruptor<BankAccountSyncEvent> bankAccountSyncDisruptor) {
		return bankAccountSyncDisruptor.getRingBuffer();
	}

	@Bean
	public Disruptor<CustomDocDefinitionSyncEvent> customDocDefinitionSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<CustomDocDefinitionSyncEvent> disruptor = new Disruptor<>(new CustomDocDefinitionSyncEventFactory(),
				BUFFER_SIZE, DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(customDocDefinitionSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<CustomDocDefinitionSyncEvent> customDocDefinitionSyncRingBuffer(
			Disruptor<CustomDocDefinitionSyncEvent> customDocDefinitionSyncDisruptor) {
		return customDocDefinitionSyncDisruptor.getRingBuffer();
	}

	@Bean
	public Disruptor<CustomDocSyncEvent> customDocSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<CustomDocSyncEvent> disruptor = new Disruptor<>(new CustomDocSyncEventFactory(), BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(customDocSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<CustomDocSyncEvent> customDocSyncRingBuffer(
			Disruptor<CustomDocSyncEvent> customDocSyncDisruptor) {
		return customDocSyncDisruptor.getRingBuffer();
	}

	@Bean
	public Disruptor<VoucherSyncEvent> voucherSyncDisruptor() {
		// 创建Disruptor，使用DaemonThreadFactory作为线程工厂
		Disruptor<VoucherSyncEvent> disruptor = new Disruptor<>(new VoucherSyncEventFactory(), BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(voucherSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	@Bean
	public RingBuffer<VoucherSyncEvent> voucherSyncRingBuffer(Disruptor<VoucherSyncEvent> voucherSyncDisruptor) {
		return voucherSyncDisruptor.getRingBuffer();
	}

	/**
	 * 创建固定资产同步事件Disruptor
	 */
	@Bean
	public Disruptor<FixedAssetSyncEvent> fixedAssetSyncDisruptor() {
		// 创建Disruptor
		Disruptor<FixedAssetSyncEvent> disruptor = new Disruptor<>(fixedAssetSyncEventFactory, BUFFER_SIZE,
				DaemonThreadFactory.INSTANCE);

		// 设置事件处理器
		disruptor.handleEventsWith(fixedAssetSyncEventHandler);

		// 设置异常处理器
		disruptor.setDefaultExceptionHandler(createExceptionHandler());

		// 启动Disruptor
		disruptor.start();

		return disruptor;
	}

	/**
	 * 创建固定资产同步事件RingBuffer
	 */
	@Bean
	public RingBuffer<FixedAssetSyncEvent> fixedAssetSyncRingBuffer(Disruptor<FixedAssetSyncEvent> disruptor) {
		return disruptor.getRingBuffer();
	}
}
