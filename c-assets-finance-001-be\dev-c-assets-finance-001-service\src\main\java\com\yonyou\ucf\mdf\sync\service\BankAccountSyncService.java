package com.yonyou.ucf.mdf.sync.service;

import java.time.LocalDateTime;
import java.util.List;

import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankAccount;

/**
 * 企业银行账户同步服务
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface BankAccountSyncService {

    /**
     * 同步企业银行账户数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncBankAccount(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 发布同步事件
     * 
     * @param kingdeeBankAccounts 金蝶企业银行账户列表
     */
    void publishEvent(List<KingdeeBankAccount> kingdeeBankAccounts);
} 