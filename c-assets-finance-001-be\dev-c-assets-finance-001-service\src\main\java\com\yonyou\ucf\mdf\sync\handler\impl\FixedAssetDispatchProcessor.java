package com.yonyou.ucf.mdf.sync.handler.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.kingdee.fixedasset.service.FixedAssetDispatchService;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.CommonResponse;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetDispatchParam;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.FixedAssetSyncEvent;
import com.yonyou.ucf.mdf.sync.handler.FixedAssetSyncProcessor;
import com.yonyou.ucf.mdf.sync.model.FixedAssetSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.FixedAssetSyncLog;
import com.yonyou.ucf.mdf.sync.service.FixedAssetDispatchConverter;
import com.yonyou.ucf.mdf.sync.service.FixedAssetSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.FixedAssetSyncLogService;

import lombok.extern.slf4j.Slf4j;

/**
 * 固定资产调拨处理器
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class FixedAssetDispatchProcessor implements FixedAssetSyncProcessor {

	@Autowired
	private FixedAssetDispatchService fixedAssetDispatchService;
	@Autowired
	private FixedAssetDispatchConverter fixedAssetDispatchConverter;
	@Autowired
	private FixedAssetSyncLogService fixedAssetSyncLogService;
	@Autowired
	private FixedAssetSyncFailRecordService fixedAssetSyncFailRecordService;

	@Override
	public void process(FixedAssetSyncEvent event) {
		FixedAssetSyncTypeEnum syncType = event.getSyncType();
		if (syncType == FixedAssetSyncTypeEnum.DISPATCH) {
			long begin = System.currentTimeMillis();
			BizObject param = event.getParam();
			log.info("开始处理固定资产调拨事件，参数：{}", JsonUtils.toJsonString(param));
			FixedAssetSyncLog syncLog = initialSyncLog(event);
			try {
				FixedAssetDispatchParam fixedAssetDispatchParam = fixedAssetDispatchConverter.convert(param,
						event.getAssetBillType());
				if (fixedAssetDispatchParam == null) {
					return;
				}
				syncLog.setRequestData(JsonUtils.toJsonString(fixedAssetDispatchParam));
				// 调用固定资产调拨服务
				CommonResponse respone = fixedAssetDispatchService.dispatchFixedAsset(fixedAssetDispatchParam);
				syncLog.setResponeData(JsonUtils.toJsonString(respone));
				if (respone.isSuccess()) {
					syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
				} else {
					syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
					syncLog.setErrMsg(respone.getMsg());
				}
				long end = System.currentTimeMillis();
				int costTime = (int) (end - begin);
				syncLog.setCostTime(costTime);

				fixedAssetSyncLogService.save(syncLog);

			} catch (Exception e) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(e.getMessage());
				syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
				long end = System.currentTimeMillis();
				int costTime = (int) (end - begin);
				syncLog.setCostTime(costTime);
				fixedAssetSyncLogService.save(syncLog);
			}

			FixedAssetSyncFailRecord failRecord = convertFailRecord(syncLog);
			if (failRecord != null) {
				fixedAssetSyncFailRecordService.saveFailRecord(failRecord);
			}

			log.info("固定资产调拨事件处理完成");
		}
	}

	/**
	 * 生成失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private FixedAssetSyncFailRecord convertFailRecord(FixedAssetSyncLog syncLog) {
		if (syncLog == null || SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			return null;
		}
		FixedAssetSyncFailRecord failRecord = new FixedAssetSyncFailRecord();
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		failRecord.setBusinessNo(syncLog.getBusinessNo());
		failRecord.setSyncType(syncLog.getSyncType());
		failRecord.setAssetCode(syncLog.getAssetCode());
		failRecord.setAssetName(syncLog.getAssetName());
		failRecord.setAssetBillId(syncLog.getAssetBillId());
		failRecord.setAssetBillType(syncLog.getAssetBillType());
		failRecord.setSuccess(syncLog.getSuccess());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setRawData(syncLog.getRawData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setCostTime(syncLog.getCostTime());
		failRecord.setRetryCount(0);
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		return failRecord;
	}

	/**
	 * 初始化同步日志
	 * 
	 * @param param
	 * @return
	 */
	private FixedAssetSyncLog initialSyncLog(FixedAssetSyncEvent event) {
		BizObject param = event.getParam();
		AssetBillTypeEnum billType = event.getAssetBillType();
		FixedAssetSyncLog syncLog = new FixedAssetSyncLog();
		syncLog.set_status(ActionEnum.INSERT.getValueInt());
		syncLog.setAssetBillType(billType.getCode());
		syncLog.setBusinessNo(param.getString("bill_code"));
		syncLog.setSyncType(event.getSyncType().getEasMethodName());
		syncLog.setAssetCode(syncLog.getBusinessNo());
		syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
		syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		syncLog.setRawData(JsonUtils.toJsonString(param));
		syncLog.setAssetBillId(param.getString("id"));
		String equip_name = param.getString("pk_transitype__name");
		if (equip_name != null) {
			syncLog.setAssetName(equip_name);
		}
		return syncLog;
	}
}