package com.yonyou.ucf.mdf.sync.publisher;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankBranch;
import com.yonyou.ucf.mdf.sync.event.BankBranchSyncEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 银行网点同步事件发布器
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Component
public class BankBranchSyncEventPublisher {

    @Autowired
    private RingBuffer<BankBranchSyncEvent> ringBuffer;

    /**
     * 发布银行网点同步事件
     * 
     * @param kingdeeBankBranch 金蝶银行网点数据
     */
    public void publish(KingdeeBankBranch kingdeeBankBranch) {
        try {
            long sequence = ringBuffer.next();
            try {
                BankBranchSyncEvent event = ringBuffer.get(sequence);
                event.setKingdeeBankBranch(kingdeeBankBranch);
            } finally {
                ringBuffer.publish(sequence);
            }
        } catch (Exception e) {
            log.error("发布银行网点同步事件失败，银行网点编码：{}", kingdeeBankBranch.getNumber(), e);
        }
    }
} 