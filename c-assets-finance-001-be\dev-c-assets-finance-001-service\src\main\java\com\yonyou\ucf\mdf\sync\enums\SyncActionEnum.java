package com.yonyou.ucf.mdf.sync.enums;

import lombok.Getter;

/**
 * 同步操作枚举
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Getter
public enum SyncActionEnum {

	/**
	 * 新增
	 */
	INSERT("Insert", "新增"),

	/**
	 * 更新
	 */
	UPDATE("Update", "更新"),

	/**
	 * 插入更新（根据code判断，存在则更新，不存在则新增）
	 */
	INSERT_UPDATE("InsertUpdate", "插入更新");

	/**
	 * 操作标识
	 */
	private final String value;

	/**
	 * 操作描述
	 */
	private final String desc;

	SyncActionEnum(String value, String desc) {
		this.value = value;
		this.desc = desc;
	}

	/**
	 * 根据操作标识获取枚举
	 * 
	 * @param value 操作标识
	 * @return 同步操作枚举
	 */
	public static SyncActionEnum getByValue(String value) {
		for (SyncActionEnum actionEnum : values()) {
			if (actionEnum.getValue().equals(value)) {
				return actionEnum;
			}
		}
		return null;
	}
}