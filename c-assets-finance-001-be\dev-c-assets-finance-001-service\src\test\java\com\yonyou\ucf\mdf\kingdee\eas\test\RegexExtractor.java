package com.yonyou.ucf.mdf.kingdee.eas.test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexExtractor {
	public static void main(String[] args) {
		String str1 = "EA01.01.002-00001";
		String str2 = "HK01.10-00085";

		// 正则表达式模式
		Pattern pattern = Pattern.compile("^([A-Z]{2}).*-([0-9]+)$");

		// 提取示例
		extractWithRegex(str1, pattern);
		extractWithRegex(str2, pattern);
	}

	public static void extractWithRegex(String input, Pattern pattern) {
		Matcher matcher = pattern.matcher(input);

		if (matcher.find()) {
			String prefix = matcher.group(1); // 第一个捕获组是字母前缀
			String number = matcher.group(2); // 第二个捕获组是数字后缀

			System.out.println("原始字符串: " + input);
			System.out.println("字母前缀: " + prefix);
			System.out.println("数字后缀: " + number);
			System.out.println("----------");
		} else {
			System.out.println("字符串格式不匹配: " + input);
		}
	}
}
