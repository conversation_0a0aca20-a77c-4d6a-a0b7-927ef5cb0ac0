package com.yonyou.ucf.mdf.vendor.vo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;

import lombok.Data;

/**
 * 供应商实体类
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Data
public class VendorVO {
	/**
	 * 操作标识, Insert:新增、Update:更新、InsertUpdate:会根据code判断，存在则更新，不存在则新增
	 */
	private String _status;

	/**
	 * id新增时无需填写，修改时必填
	 */
	private String id;

	/**
	 * 供应商编码
	 */
	private String code;

	/**
	 * 管理组织主键id，和org_code不能同时为空
	 */
	private String org;

	/**
	 * 管理组织编码，和org不能同时为空
	 */
	private String org_code;

	/**
	 * 管理组织名称（日志记录使用，不传接口）
	 */
	@JsonIgnore
	private String org_name;

	/**
	 * 使用组织，不填写则修改管理组织信息，填写使用组织（与管理组织不同），则只修改业务信息
	 */
	private String vendorApplyRange_org;

	/**
	 * 供应商名称,支持多语
	 */
	private MultiLanguageVO name;

	/**
	 * 所属分类ID，为空时需要传所属分类编码，不能同时为空
	 */
	private String vendorclass;

	/**
	 * 所属分类编码，为空时需要填写所属分类ID，不能同时为空
	 */
	private String vendorclass_code;

	/**
	 * 纳税类别, 0:一般纳税人、1:小规模纳税人、2:海外纳税
	 */
	private String taxPayingCategories;

	/**
	 * 国家ID
	 */
	private String country;

	/**
	 * 国家编码
	 */
	private String country_code;

	/**
	 * 语言id
	 */
	private String language;

	/**
	 * 语言编码
	 */
	private String language_code;

	/**
	 * 时区
	 */
	private String timeZone;

	/**
	 * 对应组织ID，传值时说明为内部供应商
	 */
	private String correspondingorg;

	/**
	 * 对应组织编码
	 */
	private String correspondingorg_code;

	/**
	 * 对应客户ID
	 */
	private String correspondingcust;

	/**
	 * 对应客户编码
	 */
	private String correspondingcust_code;

	/**
	 * 上级供应商id
	 */
	private String parentVendor;

	/**
	 * 散户, true:是、false:否
	 */
	private Boolean retailInvestors;

	/**
	 * 统一社会信用代码
	 */
	private String creditcode;

	/**
	 * 固定电话
	 */
	private String vendorphone;

	/**
	 * 通信地址
	 */
	private String vendoraddress;

	/**
	 * 邮政编码
	 */
	private String vendorzipcode;

	/**
	 * 电子邮箱
	 */
	private String vendoremail;

	/**
	 * 联系电话
	 */
	private String contactphone;

	/**
	 * 微信公众号
	 */
	private String wechatpublicaccount;

	/**
	 * 注册地区编码
	 */
	private String regionCode;

	/**
	 * 注册地址
	 */
	private String address;

	/**
	 * 外部编码,该字段会翻译供应商id，翻译成功会变为修改态
	 */
	private String erpCode;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 档案来源
	 */
	private String extSystemCode;

	/**
	 * 是否被分配者使用, true:是、false:否，当有非创建组使用时传true否则传false
	 */
	private Boolean isApplied;

	/**
	 * 表头特征自定义项
	 */
	private Object vendorCharacterDefine;

	/**
	 * 供应商业务属性
	 */
	private VendorExtendsVO vendorextends;

	/**
	 * 供应商联系人信息
	 */
	private List<VendorContactsVO> vendorcontactss;

	/**
	 * 供应商银行信息
	 */
	private List<VendorBankVO> vendorbanks;

	/**
	 * 供应商适用范围
	 */
	private List<VendorOrgVO> vendorOrgs;

	/**
	 * 供应商资质信息
	 */
	private VendorQualifyVO vendorQualifies;

	/**
	 * 地址信息
	 */
	private VendorAddressVO vendorAddresses;
}