<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>c-assets-finance-001</artifactId>
        <groupId>com.yonyou.ucf</groupId>
        <version>ddm-3.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>c-assets-finance-001-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yonyou.iuap</groupId>
            <artifactId>iuap-ap-ypd-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>iuap-ap-bizflow_mdd-sdk</artifactId>
                    <groupId>com.yonyou.iuap</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yonyou.ucf</groupId>
            <artifactId>c-assets-finance-001-api</artifactId>
        </dependency>
        <!--统一平台-->
        <!--本地安装依赖：mvn install:install-file -Dfile=D:\lib\bamboocloud_Codec-0.0.3.jar -DgroupId=com.bamboocloud.Codec.bamboocloud_Codec -DartifactId=bamboocloud_Codec -Dversion=0.0.3 -Dpackaging=jar -->
        <dependency>
            <groupId>com.bamboocloud.Codec.bamboocloud_Codec</groupId>
            <artifactId>bamboocloud_Codec</artifactId>
            <version>0.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.8.1</version>
        </dependency>
        
        <!-- Apache Axis 依赖 -->
        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.5</version>
        </dependency>
        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.xml</groupId>
            <artifactId>jaxrpc-api</artifactId>
            <version>1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-jaxrpc</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-saaj</artifactId>
            <version>1.4</version>
        </dependency>
        <!-- Disruptor -->
        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>
        <!-- Spring Cache -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <!-- EhCache 3.x -->
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
        </dependency>
    </dependencies>

</project>