package com.yonyou.ucf.mdf.customdoc.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocDefinition;
import com.yonyou.ucf.mdf.customdoc.service.ICustomDocDefinitionService;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案定义服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@Service
public class CustomDocDefinitionServiceImpl implements ICustomDocDefinitionService {

	private static final String SAVE_API = "/yonbip/digitalModel/customerdocdef/saveAndUpdate";

	@Autowired
	private SyncApiRequest syncApiRequest;

	@Autowired
	private IBillRepository billRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public ResponseResult<CustomDocDefinition> saveAndUpdate(CustomDocDefinition customDocDefinition) {
		try {
			CustomDocDefinition oldData = findByCode(customDocDefinition.getCode());
			if (oldData != null) {
				customDocDefinition.setId(oldData.getId());
				customDocDefinition.set_status(SyncActionEnum.UPDATE.getValue());
			}
			String result = syncApiRequest.doPostData(SAVE_API, customDocDefinition);
			ResponseResult<CustomDocDefinition> responseResult = JsonUtils.parseObject(result,
					new TypeReference<ResponseResult<CustomDocDefinition>>() {
					});
			return responseResult;
		} catch (Exception e) {
			log.error("保存自定义档案定义失败", e);
			throw new RuntimeException("保存自定义档案定义失败", e);
		}
	}

	@Override
	public CustomDocDefinition findByCode(String code) {
		String sql = "select id,code,name,classify,mcflag,ispreset from iuap_apdoc_basedoc.bd_cust_doc_def where code = ? and ytenant_id = ? and enable = '1'";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(code);
		parameter.addParam(tenantId);
		return billRepository.queryForDTO(sql, parameter, CustomDocDefinition.class);
	}
}