package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.imeta.orm.schema.SimpleCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.sync.model.VoucherSyncFailRecord;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncFailRecordService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月4日
 */
@Slf4j
@Service
public class VoucherSyncFailRecordServiceImpl implements VoucherSyncFailRecordService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	@Autowired
	private IBillQueryRepository billQryRepository;

	@Autowired
	private IBillRepository billRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SuppressWarnings("unchecked")
	@Override
	public void saveFailRecord(VoucherSyncFailRecord failRecord) {
		RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("id,voucherNumber,voucherId,retryCount");
			schema.addCondition(
					QueryConditionGroup.and(QueryCondition.name("voucherId").eq(failRecord.getVoucherId())));
			List<VoucherSyncFailRecord> result = (List<VoucherSyncFailRecord>) billQryRepository
					.queryBySchema("cxkingdee-sync.cxkingdee-sync.VoucherSyncFailRecord", schema);
			if (CollectionUtil.isNotEmpty(result)) {
				VoucherSyncFailRecord oldRecord = result.get(0);
				failRecord.setId(oldRecord.getId());
				if (oldRecord.getRetryCount() != null) {
					failRecord.setRetryCount(oldRecord.getRetryCount() + 1);
				}
				failRecord.set_status(ActionEnum.UPDATE.getValueInt());
			}
			if (StringUtils.isNotBlank(failRecord.getErrMsg()) && failRecord.getErrMsg().length() >= 200) {
				failRecord.setErrMsg(failRecord.getErrMsg().substring(0, 180));
			}
			try {
				List<IBillDO> billDOs = Lists.newArrayList(failRecord);
				billCommonRepository.commonSaveBill(billDOs, "VoucherSyncFailRecord");
			} catch (Exception e) {
				log.error("保存凭证同步失败记录报错！", e);
			}
		});
	}

	@Override
	public void deleteByVoucherId(String voucherId) {
		RobotExecutors.runAs(tenantId, () -> {
			try {
				billRepository.batchRemove("cxkingdee-sync.cxkingdee-sync.VoucherSyncFailRecord",
						Arrays.asList(new SimpleCondition("voucherId", ConditionOperator.eq, voucherId)));
			} catch (Exception e) {
				log.error("删除凭证同步失败记录报错！" + e.getMessage(), e);
			}
		});
	}

}
