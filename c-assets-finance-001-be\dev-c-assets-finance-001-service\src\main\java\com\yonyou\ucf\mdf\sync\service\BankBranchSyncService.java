package com.yonyou.ucf.mdf.sync.service;

import java.time.LocalDateTime;
import java.util.List;

import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankBranch;

/**
 * 银行网点同步服务
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
public interface BankBranchSyncService {
    
    /**
     * 同步银行网点数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncBankBranch(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 发布银行网点同步事件
     * 
     * @param kingdeeBankBranches 金蝶银行网点数据列表
     */
    void publishEvent(List<KingdeeBankBranch> kingdeeBankBranches);
} 