package com.yonyou.ucf.mdf.sync.enums;

import lombok.Getter;

/**
 * 固定资产同步类型枚举
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Getter
public enum FixedAssetSyncTypeEnum {
    
    /**
     * 固定资产新增
     */
    ADD("新增", "ImpFixedAssetApply"),
    
    /**
     * 固定资产变更
     */
    CHANGE("变更", "ImpFaChangeBill"),
    
    /**
     * 固定资产清理
     */
    CLEAR("清理", "ImpClearBill"),
    
    /**
     * 固定资产调拨
     */
    DISPATCH("调拨", "ImpDisPatchBill");
    
    /**
     * 业务类型描述
     */
    private final String description;
    
    /**
     * 金蝶EAS接口方法名
     */
    private final String easMethodName;
    
    FixedAssetSyncTypeEnum(String description, String easMethodName) {
        this.description = description;
        this.easMethodName = easMethodName;
    }
} 