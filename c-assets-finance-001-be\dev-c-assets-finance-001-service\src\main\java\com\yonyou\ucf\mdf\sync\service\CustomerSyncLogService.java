package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.model.CustomerSyncLog;

/**
 * 客户同步日志服务
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
public interface CustomerSyncLogService {

	/**
	 * 记录同步成功日志
	 * 
	 * @param syncLog 日志信息
	 * @return 同步日志
	 */
	void logSuccess(CustomerSyncLog syncLog);

	/**
	 * 记录同步失败日志
	 * 
	 * @param syncLog 日志信息
	 * @return 同步日志
	 */
	void logError(CustomerSyncLog syncLog);
}