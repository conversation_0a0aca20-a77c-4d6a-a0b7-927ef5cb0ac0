package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.sync.model.FixedAssetSyncLog;
import com.yonyou.ucf.mdf.sync.service.FixedAssetSyncLogService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月13日
 */
@Slf4j
@Service
public class FixedAssetSyncLogServiceImpl implements FixedAssetSyncLogService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public void save(FixedAssetSyncLog syncLog) {
		log.info("开始保存资产同步日志，资产号：{}", syncLog.getAssetCode());
		try {
			if (StringUtils.isNotBlank(syncLog.getErrMsg()) && syncLog.getErrMsg().length() >= 200) {
				syncLog.setErrMsg(syncLog.getErrMsg().substring(0, 180));
			}
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			RobotExecutors.runAs(tenantId, () -> {
				try {
					return billCommonRepository.commonSaveBill(billDOs, "FixedAssetSyncLog");
				} catch (Exception e) {
					log.error("保存资产同步日志报错！" + e.getMessage(), e);
					throw new RuntimeException("保存资产同步日志报错！" + e.getMessage(), e);
				}
			});
			log.info("保存资产同步日志成功，资产号：{}", syncLog.getAssetCode());
		} catch (Exception e) {
			log.error("保存资产同步日志失败，资产号：{}", syncLog.getAssetCode(), e);
			throw new RuntimeException("保存资产同步日志失败:" + e.getMessage(), e);
		}
	}

}
