package com.yonyou.ucf.mdf.sync.publisher;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDoc;
import com.yonyou.ucf.mdf.sync.event.CustomDocSyncEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案同步事件发布者
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class CustomDocSyncEventPublisher {

	@Autowired
	private RingBuffer<CustomDocSyncEvent> ringBuffer;

	/**
	 * 发布自定义档案同步事件
	 * 
	 * @param kingdeeCustomDoc 金蝶自定义档案
	 */
	public void publish(KingdeeCustomDoc kingdeeCustomDoc) {
		try {
			long sequence = ringBuffer.next();
			try {
				CustomDocSyncEvent event = ringBuffer.get(sequence);
				event.setKingdeeCustomDoc(kingdeeCustomDoc);
			} finally {
				ringBuffer.publish(sequence);
			}
			log.info("发布自定义档案同步事件成功，自定义档案编码：{}", kingdeeCustomDoc.getNumber());
		} catch (Exception e) {
			log.error("发布自定义档案同步事件失败，自定义档案编码：{}", kingdeeCustomDoc.getNumber(), e);
		}
	}
}