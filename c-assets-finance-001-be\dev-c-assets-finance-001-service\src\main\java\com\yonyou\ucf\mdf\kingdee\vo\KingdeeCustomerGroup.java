package com.yonyou.ucf.mdf.kingdee.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金蝶客户组实体类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
@EqualsAndHashCode
@JsonInclude(Include.NON_NULL)
public class KingdeeCustomerGroup {

	/**
	 * 客户分组名称
	 */
	private String groupName;

	/**
	 * 客户分组编码
	 */
	private String groupNum;

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("{");
		sb.append("\"groupName\":\"").append(groupName).append("\",");
		sb.append("\"groupNum\":\"").append(groupNum).append("\"");
		sb.append("}");
		return sb.toString();
	}
}