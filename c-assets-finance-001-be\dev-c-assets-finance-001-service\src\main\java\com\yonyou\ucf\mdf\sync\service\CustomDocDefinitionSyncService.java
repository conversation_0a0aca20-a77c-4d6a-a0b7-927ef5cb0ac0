package com.yonyou.ucf.mdf.sync.service;

import java.time.LocalDateTime;
import java.util.List;

import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;

/**
 * 自定义档案定义同步服务
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface CustomDocDefinitionSyncService {
    
    /**
     * 同步自定义档案定义数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncCustomDocDefinition(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 发布自定义档案定义同步事件
     * 
     * @param kingdeeCustomDocDefinitions 金蝶自定义档案定义数据列表
     */
    void publishEvent(List<KingdeeCustomDocDefinition> kingdeeCustomDocDefinitions);
} 