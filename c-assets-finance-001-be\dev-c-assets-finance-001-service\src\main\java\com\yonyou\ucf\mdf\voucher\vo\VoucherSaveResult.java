package com.yonyou.ucf.mdf.voucher.vo;

import java.math.BigDecimal;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers.BigDecimalDeserializer;

import lombok.Data;

/**
 * 凭证保存返回结果
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Data
public class VoucherSaveResult {

	/**
	 * 凭证ID
	 */
	private String voucherId;

	/**
	 * 凭证状态（00暂存，01保存，02错误，03已审核，04已记账，05作废）
	 */
	private String voucherStatus;

	/**
	 * 会计期间，格式：yyyy-MM
	 */
	private String period;

	/**
	 * 借方原币金额合计
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal totalDebitOrg;

	/**
	 * 贷方原币金额合计
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal totalCreditOrg;

	/**
	 * 凭证号
	 */
	private String billCode;

	/**
	 * 账簿信息
	 */
	private AccbookInfo accbook;

	/**
	 * 凭证类型信息
	 */
	private VoucherTypeInfo voucherType;

	/**
	 * 账簿信息
	 */
	@Data
	public static class AccbookInfo {
		/**
		 * 账簿ID
		 */
		private String id;

		/**
		 * 账簿编码
		 */
		private String code;

		/**
		 * 账簿名称
		 */
		private String name;
	}

	/**
	 * 凭证类型信息
	 */
	@Data
	public static class VoucherTypeInfo {
		/**
		 * 凭证类型ID
		 */
		private String id;

		/**
		 * 凭证类型编码
		 */
		private String code;

		/**
		 * 凭证类型名称
		 */
		private String name;

		/**
		 * 凭证类型字符串
		 */
		private String voucherstr;
	}
}