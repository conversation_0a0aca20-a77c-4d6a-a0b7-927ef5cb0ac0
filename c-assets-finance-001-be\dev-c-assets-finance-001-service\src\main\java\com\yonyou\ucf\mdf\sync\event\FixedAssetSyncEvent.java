package com.yonyou.ucf.mdf.sync.event;

import org.imeta.orm.base.BizObject;

import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 固定资产同步事件基类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
@EqualsAndHashCode
public class FixedAssetSyncEvent {

	/**
	 * 同步类型
	 */
	private FixedAssetSyncTypeEnum syncType;

	/**
	 * 单据类型
	 */
	private AssetBillTypeEnum assetBillType;

	/**
	 * 参数
	 */
	private BizObject param;

}