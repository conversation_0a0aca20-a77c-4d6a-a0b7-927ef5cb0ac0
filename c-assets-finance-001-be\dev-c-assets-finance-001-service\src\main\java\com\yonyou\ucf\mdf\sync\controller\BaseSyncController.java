package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 同步控制器基类
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
public abstract class BaseSyncController {

	/**
	 * 获取同步时间范围
	 * 
	 * @param syncDate 同步日期参数
	 * @return 时间范围数组，[0]为开始时间，[1]为结束时间
	 */
	protected LocalDateTime[] getSyncTimeRange(@RequestBody(required = false) SyncDate syncDate) {
		LocalDateTime startTime;
		LocalDateTime endTime;

		if (syncDate != null && syncDate.getStartDate() != null && syncDate.getEndDate() != null) {
			// 使用传入的日期参数
			startTime = LocalDateTime.parse(syncDate.getStartDate() + " 00:00:00",
					DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			endTime = LocalDateTime.parse(syncDate.getEndDate() + " 23:59:59",
					DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
		} else {
			// 使用当前日期
			LocalDate now = LocalDate.now();
			startTime = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0, 0);
			endTime = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 23, 59, 59);
		}

		return new LocalDateTime[] { startTime, endTime };
	}

	/**
	 * 创建任务结果
	 * 
	 * @param title 任务标题
	 * @param e     异常信息
	 * @return 任务结果
	 */
	protected TaskResult createTaskResult(String title, Exception e) {
		TaskResult result = new TaskResult();
		result.setTitle(title);
		result.setAsynchronized(true);

		if (e != null) {
			log.error("执行{}定时任务失败！", title, e);
			result.setStatus(0);
			result.setContent(title + "定时任务执行失败！" + ExceptionUtils.getStackTrace(e));
			result.setMsg(title + "定时任务执行失败！" + e.getMessage());
		} else {
			result.setStatus(1);
			result.setContent(title + "定时任务执行成功！");
		}

		return result;
	}
}