package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomer;
import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;
import com.yonyou.ucf.mdf.sync.service.CustomerSyncService;

/**
 * 客户同步控制器
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@RestController
@RequestMapping("/rest")
public class CustomerSyncController extends BaseSyncController {

	@Autowired
	private CustomerSyncService customerSyncService;

	/**
	 * 同步客户数据
	 * 
	 * @param syncDate 同步日期参数
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/customer/sync")
	public TaskResult syncCustomer(@RequestBody(required = false) SyncDate syncDate) throws Exception {
		LocalDateTime[] timeRange = getSyncTimeRange(syncDate);
		try {
			customerSyncService.syncCustomer(timeRange[0], timeRange[1]);
			return createTaskResult("客户同步", null);
		} catch (Exception e) {
			return createTaskResult("客户同步", e);
		}
	}

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param rows 同步失败记录列表
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/customer/selectretry")
	public TaskResult syncCustomerRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				List<KingdeeCustomer> kingdeeCustomers = rows.stream().map(row -> {
					String kingdeeData = row.getString("kingdeeData");
					if (StringUtils.isBlank(kingdeeData)) {
						return null;
					}
					return JsonUtils.parseObject(kingdeeData, KingdeeCustomer.class);
				}).filter(Objects::nonNull).collect(Collectors.toList());

				if (!kingdeeCustomers.isEmpty()) {
					customerSyncService.publishEvent(kingdeeCustomers);
				}
			}
			return createTaskResult("客户同步", null);
		} catch (Exception e) {
			return createTaskResult("客户同步", e);
		}
	}
}
