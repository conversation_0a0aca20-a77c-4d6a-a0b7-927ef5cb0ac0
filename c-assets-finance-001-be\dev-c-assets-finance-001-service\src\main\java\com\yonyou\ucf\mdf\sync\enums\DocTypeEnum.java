package com.yonyou.ucf.mdf.sync.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 档案类型枚举
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Getter
@AllArgsConstructor
public enum DocTypeEnum {

	/**
	 * 自定义档案设置
	 */
	CUSTOM_DOC_SETTING("1", "自定义档案设置"),

	/**
	 * 自定义档案维护
	 */
	CUSTOM_DOC_MAINTENANCE("2", "自定义档案维护");

	/**
	 * 编码
	 */
	private final String code;

	/**
	 * 名称
	 */
	private final String name;

	/**
	 * 根据编码获取枚举
	 * 
	 * @param code 编码
	 * @return 枚举
	 */
	public static DocTypeEnum getByCode(String code) {
		for (DocTypeEnum docType : values()) {
			if (docType.getCode().equals(code)) {
				return docType;
			}
		}
		return null;
	}
}