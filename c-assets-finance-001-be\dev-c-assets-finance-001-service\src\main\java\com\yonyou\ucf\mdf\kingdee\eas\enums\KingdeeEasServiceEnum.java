package com.yonyou.ucf.mdf.kingdee.eas.enums;

import lombok.Getter;

/**
 * 金蝶EAS服务枚举
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Getter
public enum KingdeeEasServiceEnum {

	/**
	 * 基础数据服务
	 */
	BASE_DATA("基础数据服务", "/ormrpc/services/WSAirportYY?wsdl"),

	/**
	 * 固定资产服务
	 */
	FIXED_ASSET("固定资产服务", "/ormrpc/services/WSAirportYY?wsdl"),

	/**
	 * 凭证服务
	 */
	VOUCHER("凭证服务", "/ormrpc/services/WSWSVoucher?wsdl");

	/**
	 * 服务描述
	 */
	private final String description;

	/**
	 * WebService接口路径
	 */
	private final String wsdlUrl;

	KingdeeEasServiceEnum(String description, String wsdlUrl) {
		this.description = description;
		this.wsdlUrl = wsdlUrl;
	}
}