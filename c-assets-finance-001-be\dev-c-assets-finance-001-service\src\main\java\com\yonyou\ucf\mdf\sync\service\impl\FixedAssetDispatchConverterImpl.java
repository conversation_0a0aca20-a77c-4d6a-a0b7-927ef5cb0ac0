package com.yonyou.ucf.mdf.sync.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetDispatchParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetDispatchParam.FixedAssetDispatchEntry;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.model.AssetSimple;
import com.yonyou.ucf.mdf.sync.service.AssetQueryService;
import com.yonyou.ucf.mdf.sync.service.FixedAssetDispatchConverter;
import com.yonyou.ucf.mdf.sync.util.CodeTranslator;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;

/**
 * <AUTHOR>
 *
 *         2025年6月13日
 */
@Service
public class FixedAssetDispatchConverterImpl implements FixedAssetDispatchConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private CodeTranslator codeTranslator;
	@Autowired
	private AssetQueryService assetQueryService;

	@Override
	public FixedAssetDispatchParam convert(BizObject param, AssetBillTypeEnum assetBillType) {
		if (param == null) {
			return null;
		}

		FixedAssetDispatchParam fixedAssetDispatchParam = new FixedAssetDispatchParam();
		defaultValueUtil.setAssetSyncDefaultValue(fixedAssetDispatchParam, FixedAssetSyncTypeEnum.DISPATCH);
		Date audittime = param.getDate("audittime"); // 审批通过时间
		if (audittime != null) {
			fixedAssetDispatchParam.setDispatchDate(new SimpleDateFormat("yyyy-MM-dd").format(audittime)); // 调拨日期
			fixedAssetDispatchParam.setAffirmDate(new SimpleDateFormat("yyyy-MM-dd").format(audittime)); // 确认日期
			fixedAssetDispatchParam.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(audittime)); // 创建时间
		}

		fixedAssetDispatchParam.setDispatchCause(null); // TODO 调拨原因
//		fixedAssetDispatchParam.setHasAffirmed(1); // 确认状态

//		String pk_org = param.getString("pk_org"); // 资产组织
//		if (StringUtils.isNotBlank(pk_org)) {
//			fixedAssetDispatchParam.setCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
//			fixedAssetDispatchParam.setControlUnitID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
//		}
		String inCompanyID = param.getString("pk_ownerunit_in"); // 调入所有权
		String outCompanyID = param.getString("pk_ownerunit_out"); // 调出所有权
		if (assetBillType == AssetBillTypeEnum.DEPLOY_USEDOUT_LIST
				|| assetBillType == AssetBillTypeEnum.DEPLOY_USEDOUT_CARD
				|| assetBillType == AssetBillTypeEnum.DEPLOY_USEDIN_LIST
				|| assetBillType == AssetBillTypeEnum.DEPLOY_USEDIN_CARD) {
			inCompanyID = param.getString("pk_usedorg_in"); // 调入使用权
			outCompanyID = param.getString("pk_usedorg_out"); // 调出使用权
		}
		if (StringUtils.isNotBlank(inCompanyID)) {
			fixedAssetDispatchParam.setInCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, inCompanyID));
		}
		if (StringUtils.isNotBlank(outCompanyID)) {
			fixedAssetDispatchParam
					.setOutCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, outCompanyID));
			// 做单组织要取调出组织
			fixedAssetDispatchParam.setCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, outCompanyID));
			fixedAssetDispatchParam
					.setControlUnitID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, outCompanyID));
		}
		Date billmaketime = param.getDate("billmaketime");
		if (billmaketime != null) {
			fixedAssetDispatchParam.setBizDate(new SimpleDateFormat("yyyy-MM-dd").format(billmaketime)); // 业务日期
		}
		fixedAssetDispatchParam.setNumber(param.getString("bill_code")); // 单据编号

		BigDecimal total_amount = param.getBigDecimal("total_amount");
		if (total_amount != null) {
			fixedAssetDispatchParam.setDispatchAmount(total_amount); // 调拨费用
		}

		List<BizObject> bodyvos = param.getBizObjects("bodyvos", BizObject.class);
		if (CollectionUtils.isEmpty(bodyvos)) {
			return null;
		}
		List<FixedAssetDispatchEntry> entrys = Lists.newArrayList();
		for (BizObject bizObject : bodyvos) {
			FixedAssetDispatchEntry entry = new FixedAssetDispatchEntry();
			String pk_equip = bizObject.getString("pk_equip");
			if (StringUtils.isEmpty(pk_equip)) {
				continue;
			}
			AssetSimple asset = assetQueryService.queryAssetSimpleById(pk_equip);
			if (asset == null || !asset.isFa_flag()) {
				continue;
			}
			entry.setAssetName(asset.getEquip_name());
			entry.setNumber(asset.getEquip_code());
			entry.setQuantity(new BigDecimal("1"));
			entrys.add(entry);

			String pk_reason = bizObject.getString("pk_reason");
			if (StringUtils.isBlank(fixedAssetDispatchParam.getDispatchCause()) && StringUtils.isNotBlank(pk_reason)) {
				fixedAssetDispatchParam
						.setDispatchCause(codeTranslator.translate(MappingTypeEnum.REASON_MAPPING, pk_reason));
			}
			String pk_user_after = bizObject.getString("pk_user_after");
			if (StringUtils.isBlank(fixedAssetDispatchParam.getInPrincipalID())
					&& StringUtils.isNotBlank(pk_user_after)) {
				fixedAssetDispatchParam
						.setInPrincipalID(codeTranslator.translate(MappingTypeEnum.STAFF_MAPPING, pk_user_after));
			}
			String pk_user_before = bizObject.getString("pk_user_before");
			if (StringUtils.isBlank(fixedAssetDispatchParam.getOutPrincipalID())
					&& StringUtils.isNotBlank(pk_user_before)) {
				fixedAssetDispatchParam
						.setOutPrincipalID(codeTranslator.translate(MappingTypeEnum.STAFF_MAPPING, pk_user_before));
			}

		}
		if (entrys.isEmpty()) {
			return null;
		}

		fixedAssetDispatchParam.setEntry(entrys);

		return fixedAssetDispatchParam;
	}

}
