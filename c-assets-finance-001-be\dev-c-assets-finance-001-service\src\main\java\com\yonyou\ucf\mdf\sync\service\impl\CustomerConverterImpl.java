package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.customer.service.CustomerService;
import com.yonyou.ucf.mdf.customer.vo.CustomerVO;
import com.yonyou.ucf.mdf.customer.vo.MultiLanguageVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomer;
import com.yonyou.ucf.mdf.org.service.OrgQryService;
import com.yonyou.ucf.mdf.org.vo.OrgVO;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.CustomerConverter;
import com.yonyou.ucf.mdf.sync.service.KingdeeSyncConfigService;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

/**
 * 客户数据转换器实现
 * 
 * <AUTHOR>
 *
 *         2025年5月27日
 */
@Component
public class CustomerConverterImpl implements CustomerConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private OrgQryService orgQryService;
	@Autowired
	private EhCacheUtil ehCacheUtil;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private KingdeeSyncConfigService configService;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public CustomerVO convert(KingdeeCustomer kingdeeCustomer) {
		CustomerVO customer = new CustomerVO();
		customer.set_status(SyncActionEnum.INSERT.getValue());

		CustomerVO oldCustomer = queryCustomerByCode(kingdeeCustomer.getNumber());
		if (oldCustomer != null) {
			customer.setId(oldCustomer.getId());
			customer.set_status(SyncActionEnum.UPDATE.getValue());
		}

		// 先设置默认值
		defaultValueUtil.setDefaultValue(customer, SyncTypeEnum.CUSTOMER);
		customer.setSourceUnique(kingdeeCustomer.getNumber());
		customer.setCode(kingdeeCustomer.getNumber());
		customer.setName(MultiLanguageVO.builder().simplifiedName(kingdeeCustomer.getName()).build());

		OrgVO orgVO = queryOrgByKingdeeCode(kingdeeCustomer.getAdminCUNum());
		if (orgVO != null) {
			customer.setCreateOrgCode(orgVO.getCode());
			customer.setCreateOrgName(orgVO.getName());
		}

//		customer.setEnterpriseNature((short) 0);
		customer.setEnterpriseName(kingdeeCustomer.getName());

		if (CollectionUtils.isNotEmpty(kingdeeCustomer.getCustomerGroups())) {
			String customerCls = getCustomerCls(kingdeeCustomer.getCustomerGroups().get(0).getGroupNum());
			customer.setCustomerClassCode(customerCls);
		}

		return customer;
	}

	/**
	 * 获取客户分类映射（因为金蝶客户的分类可能和BIP不一致）
	 * 
	 * @param customerGroups
	 * @return
	 */
	private String getCustomerCls(String customerGroups) {
		Map<String, List<Map<String, Object>>> customerSyncConfig = configService.queryAllCfg(SyncTypeEnum.CUSTOMER);
		if (customerSyncConfig.isEmpty()) {
			return customerGroups;
		}
		List<Map<String, Object>> config = customerSyncConfig.get("客户分类映射");
		if (config == null || config.size() == 0) {
			return customerGroups;
		}
		for (Map<String, Object> map : config) {
			if (customerGroups.equals(map.getOrDefault("config_key", "").toString())) {
				return map.getOrDefault("config_value", "").toString();
			}
		}
		return customerGroups;
	}

	/**
	 * 根据编码查询已存在客户
	 * 
	 * @param code
	 * @return
	 */
	private CustomerVO queryCustomerByCode(String code) {
		CustomerVO customer = (CustomerVO) ehCacheUtil.getValue(CacheConstant.CACHE_BIP_CUSTOMER, code);
		if (customer != null) {
			return customer;
		}
		customer = customerService.queryByCode(code);
		if (customer != null) {
			ehCacheUtil.putValue(CacheConstant.CACHE_BIP_CUSTOMER, code, customer);
		}
		return customer;
	}

	/**
	 * 根据金蝶组织编码，查询本地系统组织
	 * 
	 * @param adminCUNum
	 * @return
	 */
	private OrgVO queryOrgByKingdeeCode(String adminCUNum) {

		OrgVO orgVO = (OrgVO) ehCacheUtil.getValue(CacheConstant.CACHE_ORG, adminCUNum);

		if (orgVO != null) {
			return orgVO;
		}
		orgVO = orgQryService.queryByCode(adminCUNum);
		if (orgVO != null) {
			ehCacheUtil.putValue(CacheConstant.CACHE_ORG, adminCUNum, orgVO);
		}
		return orgVO;
	}

}
