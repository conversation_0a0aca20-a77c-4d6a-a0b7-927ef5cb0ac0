package com.yonyou.ucf.mdf.sync.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDoc;
import com.yonyou.ucf.mdf.org.service.OrgQryService;
import com.yonyou.ucf.mdf.org.vo.OrgVO;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.CustomDocConverter;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

/**
 * 自定义档案转换接口实现类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Service
public class CustomDocConverterImpl implements CustomDocConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private OrgQryService orgQryService;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	@Override
	public CustomDocVO convert(KingdeeCustomDoc kingdeeCustomDoc) {

		if (kingdeeCustomDoc == null) {
			return null;
		}

		CustomDocVO customDocVO = new CustomDocVO();
		// 先设置默认值
		defaultValueUtil.setDefaultValue(customDocVO, SyncTypeEnum.CUSTOM_DOC);
		customDocVO.setCode(kingdeeCustomDoc.getNumber());
		customDocVO.setName(new MultiLanguageVO().setZh_CN(kingdeeCustomDoc.getName()));
		customDocVO.setShortname(kingdeeCustomDoc.getName());
		customDocVO.setCustdocdefid_code(kingdeeCustomDoc.getGroupNum());

		OrgVO orgVO = queryOrgByKingdeeCode(kingdeeCustomDoc.getCreatorCompanyNum());
		if (orgVO != null) {
			customDocVO.setOrgid(orgVO.getId());
			customDocVO.setOrgCode(orgVO.getCode());
		}

		customDocVO.setParentCode(kingdeeCustomDoc.getParentNum());

		customDocVO.setEnable(1);

		customDocVO.setSourceUnique(kingdeeCustomDoc.getNumber());

		return customDocVO;
	}

	/**
	 * 根据金蝶组织编码，查询本地系统组织
	 * 
	 * @param adminCUNum
	 * @return
	 */
	private OrgVO queryOrgByKingdeeCode(String adminCUNum) {

		if (StringUtils.isBlank(adminCUNum)) {
			return null;
		}

		OrgVO orgVO = (OrgVO) ehCacheUtil.getValue(CacheConstant.CACHE_ORG, adminCUNum);

		if (orgVO != null) {
			return orgVO;
		}
		orgVO = orgQryService.queryByCode(adminCUNum);
		if (orgVO != null) {
			ehCacheUtil.putValue(CacheConstant.CACHE_ORG, adminCUNum, orgVO);
		} else {
			throw new RuntimeException(String.format("根据金蝶组织编码【%s】没有查询到对应组织！", adminCUNum));
		}
		return orgVO;
	}

}