package com.yonyou.ucf.mdf.sync.publisher;

import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;
import com.yonyou.ucf.mdf.sync.event.CustomDocDefinitionSyncEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案定义同步事件发布者
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomDocDefinitionSyncEventPublisher {

    private final Disruptor<CustomDocDefinitionSyncEvent> customDocDefinitionSyncDisruptor;
    private final RingBuffer<CustomDocDefinitionSyncEvent> ringBuffer;

    /**
     * 发布自定义档案定义同步事件
     * 
     * @param kingdeeCustomDocDefinition 金蝶自定义档案定义数据
     */
    public void publish(KingdeeCustomDocDefinition kingdeeCustomDocDefinition) {
        try {
            // 获取下一个序列号
            long sequence = ringBuffer.next();
            try {
                // 获取该序列号对应的事件对象
                CustomDocDefinitionSyncEvent event = ringBuffer.get(sequence);
                // 填充事件数据
                event.setKingdeeCustomDocDefinition(kingdeeCustomDocDefinition);
                event.setSyncTime(String.valueOf(System.currentTimeMillis()));
            } finally {
                // 发布事件
                ringBuffer.publish(sequence);
            }
        } catch (Exception e) {
            log.error("发布自定义档案定义同步事件失败，金蝶自定义档案定义：{}", kingdeeCustomDocDefinition, e);
            throw e;
        }
    }

    /**
     * 关闭Disruptor
     */
    public void shutdown() {
        log.error("自定义档案定义同步事件发布关闭");
        customDocDefinitionSyncDisruptor.shutdown();
    }
} 