package com.yonyou.ucf.mdf.api.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.ucf.mdf.api.common.AccessToken;
import com.yonyou.ucf.mdf.api.common.ApiConfigInfo;
import com.yonyou.ucf.mdf.api.common.GaUrl;
import com.yonyou.ucf.mdf.api.common.ResponseResult;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.imeta.spring.support.cache.RedisManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025/3/21 08:57
 * @DESCRIPTION openApi 青桔工具类
 */
@Component
public class BipOpenApiRequest {
    private static final ObjectMapper mapper = new ObjectMapper();
    @Value("${app.open-api.config}")
    private String openApiConfig;

    //使用的key,默认取 default
    private String useKey = "default";
    @Autowired
    private GatewayService gatewayService;

    @Autowired
    private RedisManager redisManager;

    @SneakyThrows
    public <T> T doPost(String requestUrl, Object param, Class<T> type) {
        return mapper.readValue(doPost(requestUrl, param), type);
    }

    @SneakyThrows
    public <T> T doPost(String requestUrl, Object param, TypeReference<T> typeReference) {
        return mapper.readValue(doPost(requestUrl, param), typeReference);
    }

    @SneakyThrows
    public <T> T doPostByObj(String requestUrl, Object param, TypeReference<T> typeReference) {
        return mapper.readValue(doPostByObj(requestUrl, param), typeReference);
    }

    public String doPostByObj(String requestUrl, Object param) {
        String url = buildUrl(requestUrl);
        HttpRequest post = HttpUtil.createPost(url);
        if (ObjectUtil.isNotEmpty(param)) {
            JSONObject data = new JSONObject();
            JSONObject obj = (JSONObject) JSONObject.toJSON(param);
            data.put("data",obj);
            post.body(data.toJSONString());
        }
        return post.execute().body();
    }

    public String doPost(String requestUrl, Object param) {
        String url = buildUrl(requestUrl);
        HttpRequest post = HttpUtil.createPost(url);
        if (ObjectUtil.isNotEmpty(param)) {
            JSONObject data = new JSONObject();
            JSONObject obj = (JSONObject) JSONObject.toJSON(param);
            JSONArray arr = new JSONArray();
            arr.add(obj);
            data.put("data",arr);
            post.body(data.toJSONString());
        }
        return post.execute().body();
    }

    @SneakyThrows
    public <T> T doGet(String requestUrl, Object param, Class<T> type) {
        return mapper.readValue(doGet(requestUrl, param), type);
    }

    @SneakyThrows
    public <T> T doGet(String requestUrl, Object param, TypeReference<T> typeReference) {
        return mapper.readValue(doGet(requestUrl, param), typeReference);
    }

    @SneakyThrows
    public String doGet(String requestUrl, Object param) {
        String url = buildUrl(requestUrl);
        if (ObjectUtil.isNotEmpty(param)) {
            Map<String, Object> paramMap = mapper.readValue(JSONObject.toJSONString(param), new TypeReference<Map<String, Object>>() {
            });
            url = UrlUtil.addUrlParams(url, paramMap);
        }
        HttpRequest httpRequest = HttpUtil.createGet(url);
        return httpRequest.execute().body();
    }

    protected String buildUrl(String requestUrl) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder
                .getRequestAttributes())).getRequest();
        String ip = IPUtil.getIp(request);
        String cacheKey = ip.split(",")[0] + requestUrl;
        AccessToken accessToken = redisManager.getObject(cacheKey);
        if (accessToken == null || ObjectUtil.isEmpty(accessToken) || StringUtils.isBlank(accessToken.getAccessToken())) {
            GaUrl gateway = gatewayService.getGateway();
            String tokenUrl = gateway.getTokenUrl() + "/open-auth/selfAppAuth/getAccessToken";
            AccessToken token = getAccessToken(tokenUrl);
            token.setGaUrl(gateway);
            accessToken = token;
            // -3 为避免token在BIP中已失效，但在redis中还未失效的时间差
            redisManager.setObject(cacheKey, token, token.getExpire() - 3);
        }
        String url = accessToken.getGaUrl().getGatewayUrl() + requestUrl;
        String token = accessToken.getAccessToken();
        return UrlUtil.addUrlParams(url, "access_token", token);
    }

    @SneakyThrows
    protected AccessToken getAccessToken(String tokenUrl) {
        JSONObject openApiConfigObj = JSONObject.parseObject(openApiConfig);
        ApiConfigInfo apiConfigInfo = openApiConfigObj.getObject(useKey, ApiConfigInfo.class);
        if (apiConfigInfo == null) {
            throw new RuntimeException("密钥信息空");
        }
        Map<String, Object> params = new TreeMap<>();
        params.put("appKey", apiConfigInfo.getAppKey());
        params.put("timestamp", System.currentTimeMillis());
        params.put("signature", SignUtil.sign(params, apiConfigInfo.getAppSecret()));
        String url = UrlUtil.addUrlParams(tokenUrl, params);
        String body = HttpUtil.createGet(url).execute().body();
        if (StringUtils.isBlank(body)) {
            throw new RuntimeException("accessToken 获取失败，返回信息为空");
        }
        ResponseResult<AccessToken> result = mapper.readValue(body, new TypeReference<ResponseResult<AccessToken>>() {
        });
        if (!result.isSuccess()) {
            throw new RuntimeException("accessToken 获取失败，code:" + result.getCode() + "message:" + result.getMessage());
        }
        return result.getData();
    }

    public String getUseKey() {
        return useKey;
    }

    public void setUseKey(String useKey) {
        this.useKey = useKey;
    }
}
