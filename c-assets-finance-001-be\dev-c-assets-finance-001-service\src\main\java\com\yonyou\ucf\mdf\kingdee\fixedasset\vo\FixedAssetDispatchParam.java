package com.yonyou.ucf.mdf.kingdee.fixedasset.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 固定资产调拨申请实体
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class FixedAssetDispatchParam {
	/**
	 * 调拨日期
	 */
	private String dispatchDate;

	/**
	 * 调拨原因
	 */
	private String dispatchCause;

	/**
	 * 调拨费用
	 */
	private BigDecimal dispatchAmount;

	/**
	 * 确认日期
	 */
	private String affirmDate;

	/**
	 * 确认状态
	 */
	private Integer hasAffirmed;

	/**
	 * 调入方负责人
	 */
	private String inPrincipalID;

	/**
	 * 调入组织
	 */
	private String inCompanyID;

	/**
	 * 调出组织
	 */
	private String outCompanyID;

	/**
	 * 调出方负责人
	 */
	private String outPrincipalID;

	/**
	 * 调拨方式
	 */
	private String dispatchMethodID;

	/**
	 * 公司
	 */
	private String companyID;

	/**
	 * 单据编号
	 */
	private String number;

	/**
	 * 业务日期
	 */
	private String bizDate;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 控制单元
	 */
	private String controlUnitID;

	/**
	 * 分录
	 */
	private List<FixedAssetDispatchEntry> entry;

	/**
	 * 固定资产调拨分录实体
	 */
	@Data
	public static class FixedAssetDispatchEntry {
		/**
		 * 调拨数量
		 */
		private BigDecimal quantity;

		/**
		 * 资产名称
		 */
		private String assetName;

		/**
		 * 单据编号（资产编码）
		 */
		private String number;
	}
}