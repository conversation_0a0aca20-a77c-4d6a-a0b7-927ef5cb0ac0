package com.yonyou.ucf.mdf.base.vo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 批量操作结果
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BatchResult {
	/**
	 * 总数量
	 */
	private Integer count;

	/**
	 * 成功数量
	 */
	private Integer successCount;

	/**
	 * 失败数量
	 */
	private Integer failCount;

	/**
	 * 错误消息列表
	 */
	private List<ErrorMessage> messages;

	/**
	 * 成功信息列表
	 */
	private List<SuccessInfo> infos;

	/**
	 * 级别
	 */
	private String level;

	/**
	 * 错误消息
	 */
	@JsonIgnoreProperties(ignoreUnknown = true)
	@Data
	public static class ErrorMessage {
		/**
		 * 数据唯一标识（来源数据唯一标识）
		 */
		private String sourceUnique;

		/**
		 * 错误数据描述
		 */
		private String message;
	}

	/**
	 * 成功信息
	 */
	@JsonIgnoreProperties(ignoreUnknown = true)
	@Data
	public static class SuccessInfo {
		/**
		 * 数据信息
		 */
		private DataInfo data;

		@JsonIgnoreProperties(ignoreUnknown = true)
		@Data
		public static class DataInfo {
			/**
			 * 数据唯一标识（来源数据唯一标识）
			 */
			private String sourceUnique;

			/**
			 * 数据唯一标识（目标数据唯一标识）
			 */
			private String targetUnique;
		}
	}
}