package com.yonyou.ucf.mdf.sync.util;

import javax.annotation.Resource;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.sync.constant.CacheConstant;

import lombok.extern.slf4j.Slf4j;

/**
 * 金蝶数据缓存工具类
 */
@Slf4j
@Component
public class EhCacheUtil {

	@Resource
	private CacheManager cacheManager;

	/**
	 * 获取成功缓存
	 */
	private Cache getSuccessCache(String cacheName) {
		String fullCacheName = CacheConstant.CACHE_SUCCESS_PREFIX + cacheName;
		Cache cache = cacheManager.getCache(fullCacheName);
		if (cache == null) {
			log.error("缓存不存在：{}", fullCacheName);
			throw new RuntimeException("缓存不存在：" + fullCacheName);
		}
		return cache;
	}

	/**
	 * 获取失败缓存
	 */
	private Cache getFailCache(String cacheName) {
		String fullCacheName = CacheConstant.CACHE_FAIL_PREFIX + cacheName;
		Cache cache = cacheManager.getCache(fullCacheName);
		if (cache == null) {
			log.error("缓存不存在：{}", fullCacheName);
			throw new RuntimeException("缓存不存在：" + fullCacheName);
		}
		return cache;
	}

	/**
	 * 添加成功数据到缓存
	 */
	public void putSuccess(String cacheName, String key, Object value) {
		try {
			getSuccessCache(cacheName).put(key, value);
		} catch (Exception e) {
			log.error("添加成功数据到缓存失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 添加失败数据到缓存
	 */
	public void putFail(String cacheName, String key, Object value) {
		try {
			getFailCache(cacheName).put(key, value);
		} catch (Exception e) {
			log.error("添加失败数据到缓存失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 从成功缓存获取数据
	 */
	public Object getSuccess(String cacheName, String key) {
		try {
			Cache.ValueWrapper valueWrapper = getSuccessCache(cacheName).get(key);
			return valueWrapper != null ? valueWrapper.get() : null;
		} catch (Exception e) {
			log.error("从成功缓存获取数据失败：{}", e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 从失败缓存获取数据
	 */
	public Object getFail(String cacheName, String key) {
		try {
			Cache.ValueWrapper valueWrapper = getFailCache(cacheName).get(key);
			return valueWrapper != null ? valueWrapper.get() : null;
		} catch (Exception e) {
			log.error("从失败缓存获取数据失败：{}", e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 从成功缓存删除数据
	 */
	public void removeSuccess(String cacheName, String key) {
		try {
			getSuccessCache(cacheName).evict(key);
		} catch (Exception e) {
			log.error("从成功缓存删除数据失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 从失败缓存删除数据
	 */
	public void removeFail(String cacheName, String key) {
		try {
			getFailCache(cacheName).evict(key);
		} catch (Exception e) {
			log.error("从失败缓存删除数据失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 清空成功缓存
	 */
	public void clearSuccess(String cacheName) {
		try {
			getSuccessCache(cacheName).clear();
		} catch (Exception e) {
			log.error("清空成功缓存失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 清空失败缓存
	 */
	public void clearFail(String cacheName) {
		try {
			getFailCache(cacheName).clear();
		} catch (Exception e) {
			log.error("清空失败缓存失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 获取缓存数据
	 * 
	 * @param cacheName 缓存名称
	 * @param key       缓存key
	 * @return
	 */
	public Object getValue(String cacheName, String key) {
		try {
			Cache.ValueWrapper valueWrapper = getCache(cacheName).get(key);
			return valueWrapper != null ? valueWrapper.get() : null;
		} catch (Exception e) {
			log.error("从成功缓存获取数据失败：{}", e.getMessage(), e);
			return null;
		}
	}

	/**
	 * 清空失败缓存
	 */
	public void clearValue(String cacheName) {
		try {
			getCache(cacheName).clear();
		} catch (Exception e) {
			log.error("清空失败缓存失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 缓存数据
	 * 
	 * @param cacheName
	 * @param key
	 * @param value
	 */
	public void putValue(String cacheName, String key, Object value) {
		try {
			getCache(cacheName).put(key, value);
		} catch (Exception e) {
			log.error("添加数据到缓存失败：{}", e.getMessage(), e);
		}
	}

	/**
	 * 获取缓存
	 * 
	 * @param cacheName
	 * @return
	 */
	private Cache getCache(String cacheName) {
		Cache cache = cacheManager.getCache(cacheName);
		if (cache == null) {
			log.error("缓存不存在：{}", cacheName);
			throw new RuntimeException("缓存不存在：" + cacheName);
		}
		return cache;
	}

}