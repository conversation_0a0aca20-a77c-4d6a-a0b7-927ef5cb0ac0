package com.yonyou.ucf.mdf.sso.common;

/**
 * @className: UrlConstant
 * @author: wjc
 * @date: 2025/5/20 10:42
 * @Version: 1.0
 * @description:
 */
public interface UrlConstant {

    /**
     * HTTP URL》》》用友获取token接口信息
     */
    String OPEN_API_URL =  "/iuap-api-auth/open-auth/selfAppAuth/getAccessToken";
    /**
     * HTTP URL》》》用友获取单点登录code接口
     */
    String THIRD_LOGIN_URL =  "/iuap-api-gateway/yonbip/yht/getThirdLoginCode";

    String THIRD_BIND_USER_ID = "/seeyon/thirdpartyController.do";

    String CAS_URL = "/cas/thirdOauth2CodeLogin?thirdUCId=%s&code=%s&service=%s";

    String LOGIN_URL = "/login?service=";

}
