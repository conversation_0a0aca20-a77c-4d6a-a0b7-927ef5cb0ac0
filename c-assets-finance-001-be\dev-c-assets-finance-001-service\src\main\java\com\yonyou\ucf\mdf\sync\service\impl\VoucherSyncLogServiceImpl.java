package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.sync.model.VoucherSyncLog;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncLogService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证同步日志服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class VoucherSyncLogServiceImpl implements VoucherSyncLogService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public void save(VoucherSyncLog syncLog) {
		log.info("开始保存凭证同步日志，凭证号：{}", syncLog.getVoucherNumber());
		try {
			if (StringUtils.isNotBlank(syncLog.getErrMsg()) && syncLog.getErrMsg().length() >= 200) {
				syncLog.setErrMsg(syncLog.getErrMsg().substring(0, 180));
			}
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			RobotExecutors.runAs(tenantId, () -> {
				try {
					return billCommonRepository.commonSaveBill(billDOs, "VoucherSyncLog");
				} catch (Exception e) {
					log.error("保存凭证同步日志报错！" + e.getMessage(), e);
				}
				return null;
			});
			log.info("保存凭证同步日志成功，凭证号：{}", syncLog.getVoucherNumber());
		} catch (Exception e) {
			log.error("保存凭证同步日志失败，凭证号：{}", syncLog.getVoucherNumber(), e);
			throw new RuntimeException("保存凭证同步日志失败", e);
		}
	}
}