package com.yonyou.ucf.mdf.voucher.service;

import java.util.List;

import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.voucher.model.Voucher;
import com.yonyou.ucf.mdf.voucher.vo.VoucherDeleteParams;
import com.yonyou.ucf.mdf.voucher.vo.VoucherQueryParams;
import com.yonyou.ucf.mdf.voucher.vo.VoucherSaveResult;
import com.yonyou.ucf.mdf.voucher.vo.VoucherVO;

/**
 * 凭证服务
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface VoucherService {

	/**
	 * 保存凭证
	 * 
	 * @param voucher 凭证数据
	 */
	ResponseResult<VoucherSaveResult> save(Voucher voucher);

	/**
	 * 查询凭证列表
	 * 
	 * @param queryParams 查询参数
	 * @return 凭证查询结果
	 */
	List<VoucherVO> query(VoucherQueryParams queryParams);

	/**
	 * 删除凭证
	 * 
	 * @param deleteParams 删除参数
	 */
	void delete(VoucherDeleteParams deleteParams);
}