package com.yonyou.ucf.mdf.sso.common;

import org.apache.commons.lang3.StringUtils;

/**
 * @className: ParseStringUtil
 * @author: wjc
 * @date: 2025/5/20 10:34
 * @Version: 1.0
 * @description:
 */
public class ParseStringUtil {
    /**
     * URL中参数解析
     */
    public static String getParseResult(String url, String key) {
        if (StringUtils.contains(url, key)) {
            int index = url.lastIndexOf(key) + key.length() + 1;
            int length = url.length();
            return url.substring(index, length);
        }
        return "";
    }
    /**
     * 去除指定元素
     */
    public static String removeElement(String url, String key) {
        if (StringUtils.contains(url, key)) {
            int index = url.lastIndexOf(key) - 1;
            return url.substring(0, index);
        }
        return url;
    }
}
