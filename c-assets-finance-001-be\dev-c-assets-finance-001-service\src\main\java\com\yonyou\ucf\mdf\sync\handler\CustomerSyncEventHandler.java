package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customer.service.CustomerService;
import com.yonyou.ucf.mdf.customer.vo.CustomerVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomer;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.CustomerSyncEvent;
import com.yonyou.ucf.mdf.sync.model.CustomerSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.CustomerSyncLog;
import com.yonyou.ucf.mdf.sync.service.CustomerConverter;
import com.yonyou.ucf.mdf.sync.service.CustomerSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.CustomerSyncLogService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户同步事件处理器
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerSyncEventHandler implements EventHandler<CustomerSyncEvent> {

	private final CustomerConverter customerConverter;
	private final CustomerService customerService;
	private final CustomerSyncLogService customerSyncLogService;
	private final CustomerSyncFailRecordService customerSyncFailRecordService;
	private final EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(CustomerSyncEvent event, long sequence, boolean endOfBatch) throws Exception {
		KingdeeCustomer kingdeeCustomer = event.getKingdeeCustomer();
		log.info("开始处理客户同步事件，金蝶客户：{}，序列号：{}", kingdeeCustomer, sequence);
		CustomerVO customerVO = null;
		CustomerSyncLog syncLog = null;
		long begin = System.currentTimeMillis();
		try {
			// 1. 转换数据
			customerVO = customerConverter.convert(kingdeeCustomer);
			if (customerVO == null) {
				return;
			}

			// 2. 保存数据
			ResponseResult<List<BatchResult>> responseResult = customerService
					.batchSaveCustomer(Collections.singletonList(customerVO));

			// 3. 记录同步日志
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog = new CustomerSyncLog();
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setCode(customerVO.getCode());
			syncLog.setName(customerVO.getName().getSimplifiedName());
			syncLog.setOrgCode(customerVO.getCreateOrgCode());
			syncLog.setOrgName(customerVO.getCreateOrgName());
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeCustomer));
			syncLog.setRequestData(JSONObject.toJSONString(customerVO));
			syncLog.setResponeData(JSONObject.toJSONString(responseResult));
			syncLog.setCostTime(costTime);
			if (!"200".equals(responseResult.getCode())) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(responseResult.getMessage());
			} else {
				List<BatchResult> batchResults = responseResult.getData();
				if (CollectionUtil.isNotEmpty(batchResults)) {
					if (batchResults.get(0).getFailCount() != null && batchResults.get(0).getFailCount() > 0) {
						syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
						if (CollectionUtil.isNotEmpty(batchResults.get(0).getMessages())) {
							syncLog.setErrMsg(batchResults.get(0).getMessages().get(0).getMessage());
						}
					} else {
						syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
					}
				} else {
					syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
					syncLog.setErrMsg("调用客户保存接口，返回数据BatchResult为空，无法判断是否保存成功");
				}
			}

			customerSyncLogService.logSuccess(syncLog);

			log.info("客户同步事件处理完成，金蝶客户：{}，序列号：{}", event.getKingdeeCustomer(), sequence);
		} catch (Exception e) {
			log.error("客户同步事件处理失败，金蝶客户：{}，序列号：{}", event.getKingdeeCustomer(), sequence, e);
			// 记录失败日志
			if (syncLog == null) {
				syncLog = new CustomerSyncLog();
			}
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg("客户同步事件处理失败" + e.getMessage());
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			if (customerVO != null) {
				syncLog.setCode(customerVO.getCode());
				syncLog.setName(customerVO.getName().getSimplifiedName());
				syncLog.setOrgCode(customerVO.getCreateOrgCode());
				syncLog.setOrgName(customerVO.getCreateOrgName());
				syncLog.setRequestData(JSONObject.toJSONString(customerVO));
			}
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeCustomer));
			syncLog.setCostTime(costTime);
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			customerSyncLogService.logError(syncLog);
		}

		CustomerSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			customerSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_KINGDEE_CUSTOMER, kingdeeCustomer.getNumber(), kingdeeCustomer);
			ehCacheUtil.removeFail(CacheConstant.CACHE_KINGDEE_CUSTOMER, kingdeeCustomer.getNumber());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_KINGDEE_CUSTOMER, kingdeeCustomer.getNumber(), kingdeeCustomer);
		}

	}

	/**
	 * 删除同步失败记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(CustomerSyncLog syncLog) {
		customerSyncFailRecordService.deleteByCode(syncLog.getCode());
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private CustomerSyncFailRecord convertFailRecord(CustomerSyncLog syncLog) {
		if (syncLog == null || SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			return null;
		}
		CustomerSyncFailRecord failRecord = new CustomerSyncFailRecord();
		failRecord.setCode(syncLog.getCode());
		failRecord.setName(syncLog.getName());
		failRecord.setOrgCode(syncLog.getOrgCode());
		failRecord.setOrgName(syncLog.getOrgName());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setRetryCount(0);
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		return failRecord;
	}

}