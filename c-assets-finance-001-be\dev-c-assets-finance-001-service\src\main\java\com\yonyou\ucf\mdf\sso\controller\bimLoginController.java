package com.yonyou.ucf.mdf.sso.controller;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.sso.common.SSOConstant;
import com.yonyou.ucf.mdf.sso.service.IBimLoginService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @className: bimLoginController
 * @author: wjc
 * @date: 2025/5/12 10:36
 * @Version: 1.0
 * @description:
 */
@RestController
@RequestMapping("/oauth2/bim")
public class bimLoginController {

    private static final Logger logger = LoggerFactory.getLogger(bimLoginController.class);

    @Value("${third.bim.domain}")
    private String bimDomain;
    @Value("${third.bim.clientId}")
    private String bimClientId;
    @Value("${domain.url:}")
    private String domainUrl;

    @Autowired
    private IBimLoginService loginService;

    @GetMapping(value = "/loginToYonBIP")
    public void loginToYonBIP(HttpServletRequest request, HttpServletResponse response) {
        try {
            String code = request.getParameter("code");
            if (StringUtils.isEmpty(code)) {
                //没有参数需要重定向登录页面，登陆后跳转会带过来code
                StringBuilder builder = new StringBuilder();
                builder.append(bimDomain + SSOConstant.SSO_THIRD_AUTHORIZE);
                builder.append("?client_id=");
                builder.append(bimClientId);
                builder.append("&redirect_uri=").append(domainUrl+"/c-assets-finance-001/oauth2/bim/loginToYonBIP");
                builder.append("&response_type=code");
                logger.error("未获取到code重新获取,[sendRedirect]:" + builder.toString());
                response.sendRedirect(builder.toString());
                return;
            }
            response.setContentType("text/html;charset=utf-8");
            String redirectUrl = loginService.getRedirectUrl(request, response);
            if (StringUtils.isEmpty(redirectUrl)) {
                throw new RuntimeException("获取跳转地址失败!");
            }
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            logger.error("跳转到BIP失败" + e.getMessage());
            try {
                JSONObject result = new JSONObject();
                result.put("result", "fail");
                result.put("msg", e.getMessage());
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write(result.toJSONString());
            } catch (Exception e1) {
                logger.error("跳转到BIP失败" + e1.getMessage());
            }
        }
    }

}
