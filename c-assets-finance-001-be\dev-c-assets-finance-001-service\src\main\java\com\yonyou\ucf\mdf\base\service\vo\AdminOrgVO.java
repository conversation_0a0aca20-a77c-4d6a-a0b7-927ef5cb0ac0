package com.yonyou.ucf.mdf.base.service.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/22 14:33
 * @DESCRIPTION 部门实体类
 */
@Data
public class AdminOrgVO {
    /*** 编码 必填 示例：code001 */
    private String code;

    /*** 名称 必填 */
    private MultiLang name;

    /*** 所属组织id（与所属组织编码不能同时为空，编码优先级高于id） */
    private String parentorgid;

    /*** 所属组织编码 示例：dept001 */
    private String parentorgid_code;

    /*** 所属上级id（组织或部门，为空时默认取所属组织id） */
    private String parent;

    /*** 所属上级编码（组织或部门，为空时默认取所属组织id） */
    private String parent_code;

    /*** 主键（新增时不填，修改时必填） */
    private String id;

    /*** 外部系统主键 */
    private String objid;

    /*** 部门分管领导主键（编码优先级高于id） */
    private String branchleader;

    /*** 部门分管领导编码 */
    private String branchleader_code;

    /*** 操作标识 必填 示例：Insert */
    private String _status;

    /*** 启用状态 示例：1 */
    private Integer enable;

    /*** 部门性质主键（编码优先级高于id） */
    private String depttype;

    /*** 部门性质编码 */
    private String depttype_code;

    /*** 部门负责人主键（编码优先级高于id） */
    private String principal;

    /*** 部门负责人编码 */
    private String principal_code;

    /*** 部门自定义特征 */
    private Object deptdefinefeature;

    /*** 特征id（新增时不填，修改时必填） */
    private String featureId;
}