package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案负责人
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerPrincipalVO {
    /**
     * 客户档案负责人子表id，更新和删除时必填
     * 示例：123456
     */
    private Long id;

    /**
     * 客户档案负责人子表专管部门；填写部门编码；专管部门和专管业务员至少有一项必填
     * 示例：专管部门
     */
    private String specialManagementDepCode;

    /**
     * 客户档案负责人子表专管业务员；填写业务员编码；专管部门和专管业务员至少有一项必填
     * 示例：专管业务员
     */
    private String professSalesmanCode;

    /**
     * 是否默认负责人；必填；true：是，false：否；默认负责人只能并且必须设置一个
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案负责人实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;
} 