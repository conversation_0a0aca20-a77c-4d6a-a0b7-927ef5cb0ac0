package com.yonyou.ucf.mdf.sync.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeVendor;
import com.yonyou.ucf.mdf.org.service.OrgQryService;
import com.yonyou.ucf.mdf.org.vo.OrgVO;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.VendorConverter;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;
import com.yonyou.ucf.mdf.vendor.vo.VendorVO;

/**
 * 供应商数据转换器实现
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Service
public class VendorConverterImpl implements VendorConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;

	@Autowired
	private OrgQryService orgQryService;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public VendorVO convert(KingdeeVendor kingdeeVendor) {
		if (kingdeeVendor == null) {
			return null;
		}

		VendorVO vendorVO = new VendorVO();
		// 先设置默认值
		defaultValueUtil.setDefaultValue(vendorVO, SyncTypeEnum.VENDOR);
		// 设置操作标识为新增
		vendorVO.set_status(SyncActionEnum.INSERT_UPDATE.getValue());

		// 设置基本信息
		vendorVO.setCode(kingdeeVendor.getNumber());

		// 设置供应商名称（支持多语）
		MultiLanguageVO name = new MultiLanguageVO();
		name.setZh_CN(kingdeeVendor.getName());
		vendorVO.setName(name);

		// 设置管理组织信息
		OrgVO orgVO = queryOrgByKingdeeCode(kingdeeVendor.getAdminCUNum());
		if (orgVO != null) {
			vendorVO.setOrg_code(orgVO.getCode());
			vendorVO.setOrg_name(orgVO.getName());
		}

		// 设置供应商分类信息
		vendorVO.setVendorclass_code(kingdeeVendor.getSupplierGroupNum());

		// 设置是否集团内公司
		if ("true".equalsIgnoreCase(kingdeeVendor.getIsInternalCompany())) {
			orgVO = queryOrgByKingdeeCode(kingdeeVendor.getInternalCompanyNum());
			if (orgVO != null) {
				vendorVO.setCorrespondingorg(orgVO.getCode());
				vendorVO.setCorrespondingorg_code(orgVO.getName());
			}
		}

		return vendorVO;
	}

	/**
	 * 根据组织编码获取组织信息
	 * 
	 * @param adminCUNum
	 * @return
	 */
	private OrgVO queryOrgByKingdeeCode(String adminCUNum) {
		OrgVO orgVO = (OrgVO) ehCacheUtil.getValue(CacheConstant.CACHE_ORG, adminCUNum);

		if (orgVO != null) {
			return orgVO;
		}
		orgVO = orgQryService.queryByCode(adminCUNum);
		if (orgVO != null) {
			ehCacheUtil.putValue(CacheConstant.CACHE_ORG, adminCUNum, orgVO);
		}
		return orgVO;
	}
}