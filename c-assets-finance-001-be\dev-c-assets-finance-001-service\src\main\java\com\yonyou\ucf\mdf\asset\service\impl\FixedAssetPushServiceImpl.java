package com.yonyou.ucf.mdf.asset.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.asset.service.FixedAssetPushService;
import com.yonyou.ucf.mdf.sync.publisher.FixedAssetSyncEventPublisher;

/**
 * 固定资产推送服务实现
 */
@Service
public class FixedAssetPushServiceImpl implements FixedAssetPushService {

	@Autowired
	private FixedAssetSyncEventPublisher publisher;

	@Override
	public void pushFixedAsset(AssetBillTypeEnum assetBillType, BizObject bizObject) {
		if (assetBillType == null || bizObject == null) {
			return;
		}
		switch (assetBillType) {
		case USEDEPT_ALTER_LIST:
		case USEDEPT_ALTER_CARD:
		case MANDEPT_ALTER_LIST:
		case MANDEPT_ALTER_CARD:
			publisher.publishChangeEvent(assetBillType, bizObject);
			break;
		case ALTER_LIST:
		case ALTER_CARD:
			String alterinfo = bizObject.getString("alterinfo");
			if (StringUtils.isNotBlank(alterinfo)) {
				String[] alterinfos = alterinfo.split(",");
				// 判断alterinfos数组是否包含pk_usedept、pk_mandept
				boolean containsUseDept = false;
				boolean containsManDept = false;
				for (String info : alterinfos) {
					if ("pk_usedept".equals(info)) {
						containsUseDept = true;
					}
					if ("pk_mandept".equals(info)) {
						containsManDept = true;
					}
				}
				if (containsUseDept || containsManDept) {
					publisher.publishChangeEvent(assetBillType, bizObject);
				}
			}
			break;
		case EQUIP_LIST:
		case EQUIP_CARD:
			boolean fa_flag = bizObject.getBoolean("fa_flag");
			if (fa_flag) {
				publisher.publishAddEvent(assetBillType, bizObject);
			}
			break;
		case DEPLOY_OUT_LIST:
		case DEPLOY_OUT_CARD:
		case DEPLOY_IN_LIST:
		case DEPLOY_IN_CARD:
//		case DEPLOY_USEDOUT_LIST:
//		case DEPLOY_USEDOUT_CARD:
//		case DEPLOY_USEDIN_LIST:
//		case DEPLOY_USEDIN_CARD:
			publisher.publishDispatchEvent(assetBillType, bizObject);
			break;
		case Sale_LIST:
		case Sale_CARD:
			publisher.publishClearEvent(assetBillType, bizObject);
			break;
		default:
			break;
		}
	}

}
