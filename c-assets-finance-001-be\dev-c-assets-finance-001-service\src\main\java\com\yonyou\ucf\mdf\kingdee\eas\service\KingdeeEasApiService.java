package com.yonyou.ucf.mdf.kingdee.eas.service;

import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;

/**
 * 金蝶EAS接口服务
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
public interface KingdeeEasApiService {

	/**
	 * 调用金蝶EAS基础数据接口
	 * 
	 * @param serviceEnum 服务枚举
	 * @param methodName  方法名称
	 * @param params      参数
	 * @return 接口返回结果
	 */
	String callEasBaseApi(KingdeeEasServiceEnum serviceEnum, String methodName, Object params);

	/**
	 * 调用金蝶EAS接口（带返回类型）
	 * 
	 * @param <T>          返回类型
	 * @param serviceEnum  服务枚举
	 * @param methodName   方法名称
	 * @param params       参数
	 * @param responseType 返回类型
	 * @return 接口返回结果
	 */
	<T> T callEasBaseApi(KingdeeEasServiceEnum serviceEnum, String methodName, Object params, Class<T> responseType);
}