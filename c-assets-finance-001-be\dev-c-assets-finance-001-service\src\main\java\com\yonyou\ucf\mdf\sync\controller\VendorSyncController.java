package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeVendor;
import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;
import com.yonyou.ucf.mdf.sync.service.VendorSyncService;

/**
 * 供应商同步
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@RestController
@RequestMapping("/rest")
public class VendorSyncController extends BaseSyncController {

	@Autowired
	private VendorSyncService vendorSyncService;

	/**
	 * 同步供应商任务接口
	 * 
	 * @param syncDate 同步日期参数
	 * @return 任务执行结果
	 * @throws Exception
	 */
	@PostMapping("/vendor/sync")
	public TaskResult syncVendor(@RequestBody(required = false) SyncDate syncDate) throws Exception {
		LocalDateTime[] timeRange = getSyncTimeRange(syncDate);
		try {
			vendorSyncService.syncVendor(timeRange[0], timeRange[1]);
			return createTaskResult("供应商同步", null);
		} catch (Exception e) {
			return createTaskResult("供应商同步", e);
		}
	}

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param rows 同步失败记录列表
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/vendor/selectretry")
	public TaskResult syncVendorRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				List<KingdeeVendor> kingdeeVendors = rows.stream().map(row -> {
					String kingdeeData = row.getString("kingdeeData");
					if (StringUtils.isBlank(kingdeeData)) {
						return null;
					}
					return JsonUtils.parseObject(kingdeeData, KingdeeVendor.class);
				}).filter(Objects::nonNull).collect(Collectors.toList());

				if (!kingdeeVendors.isEmpty()) {
					vendorSyncService.publishEvent(kingdeeVendors);
				}
			}
			return createTaskResult("供应商同步", null);
		} catch (Exception e) {
			return createTaskResult("供应商同步", e);
		}
	}
}