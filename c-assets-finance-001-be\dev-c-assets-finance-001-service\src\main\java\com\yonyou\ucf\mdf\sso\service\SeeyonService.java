package com.yonyou.ucf.mdf.sso.service;

import cn.hutool.json.JSONUtil;
import com.yonyou.ucf.mdf.sso.common.AESUtil;
import com.yonyou.ucf.mdf.sso.common.ParseStringUtil;
import com.yonyou.ucf.mdf.sso.common.StateInfoPO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @className: SeeyonService
 * @author: wjc
 * @date: 2025/5/20 10:32
 * @Version: 1.0
 * @description:
 */
@Service
public class SeeyonService {
    @Value("${YonBIP.thirdUcId}")
    private String thirdUcId;
    @Value("${YonBIP.tenantId}")
    private String tenantId;
    private String type = "seeyon";
    @Value("${third.seeyonUrl}")
    private String seeyonUrl;

    private static Logger log = LoggerFactory.getLogger(SeeyonService.class);

    /**
     * 对state解析
     */
    public StateInfoPO getStateInfo(String state) {
        if (StringUtils.isEmpty(state)) {
            throw new RuntimeException("State Is Empty");
        }
        //authMsg信息
        StateInfoPO stateInfoPo;
        try {
            //从State获取authMsg
            String authMsg = ParseStringUtil.getParseResult(state, "authMsg");
            if (StringUtils.isEmpty(authMsg)) {
                if (StringUtils.isAllEmpty(thirdUcId,tenantId,type)){
                    throw new RuntimeException("thirdUcId tenantId type has not config");
                }
                //纯单点方案，需要走配置文件
                stateInfoPo = new StateInfoPO();
                stateInfoPo.setBizUrl(state);
                stateInfoPo.setThirdUcId(thirdUcId);
                stateInfoPo.setTenantId(tenantId);
                stateInfoPo.setType(type);
                stateInfoPo.setSeeyonUrl(seeyonUrl);
                return stateInfoPo;
            }
            //对authMsg信息进行解码
            String decodeAuthMsg = AESUtil.decrypt(authMsg);
            //authMsg转成stateInfoPo取值
            stateInfoPo = JSONUtil.toBean(decodeAuthMsg, StateInfoPO.class);
            log.error("StateInfo: " + stateInfoPo.toString());
            //对必填项进行校验
            if (ObjectUtils.anyNull(stateInfoPo, stateInfoPo.getTenantId(), stateInfoPo.getThirdUcId(), stateInfoPo.getType())) {
                //空判断加信息
                throw new RuntimeException("TenantId or ThirdUcId or Type Is Null，DetailInfo:" + stateInfoPo.getTenantId() + ":" + stateInfoPo.getThirdUcId() + ":" + stateInfoPo.getType());
            }
            //去除authMsg的key值
            String bizUrl = ParseStringUtil.removeElement(state, "authMsg");
            if (StringUtils.isEmpty(bizUrl)) {
                throw new RuntimeException("BizUrl Is Empty");
            }
            //设置业务URL
            stateInfoPo.setBizUrl(bizUrl);
        } catch (Exception e) {
            log.error("Get StateInfo Error, exception={}", e.getMessage());
            throw new RuntimeException("Get StateInfo Error,msg:" + e.getMessage());
        }
        return stateInfoPo;
    }


}
