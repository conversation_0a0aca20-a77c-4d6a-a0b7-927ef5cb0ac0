package com.yonyou.ucf.mdf.vendor.vo;

import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;
import lombok.Data;

/**
 * 供应商银行实体类
 * 
 * <AUTHOR>
 * @date 2024年3月26日
 */
@Data
public class VendorBankVO {
    /**
     * 供应商银行id，新增时无需填写，修改时必填
     */
    private String id;

    /**
     * 外部编码，会翻译成供应商银行的id，翻译成功为更新态
     */
    private String erpCode;

    /**
     * 国家id
     */
    private String country;

    /**
     * 国家code，与country选填一个，两个都填以country为主
     */
    private String country_code;

    /**
     * 省份(中文名)
     */
    private String province;

    /**
     * 币种id
     */
    private String currency;

    /**
     * 币种code，与currency选填一个，两个都填以currency为准
     */
    private String currency_code;

    /**
     * 银行类别id
     */
    private String bank;

    /**
     * 银行类别code，与bank选填一个，两个都填以bank为准
     */
    private String bank_code;

    /**
     * 银行网点id
     */
    private String openaccountbank;

    /**
     * 银行网点code，与openaccountbank选填一个，两个都填以openaccountbank为准
     */
    private String openaccountbank_code;

    /**
     * 银行账号
     */
    private String account;

    /**
     * 账号名称
     */
    private String accountname;

    /**
     * 开户地区
     */
    private String region;

    /**
     * 开户地区
     */
    private String regionCode;

    /**
     * 联行号
     */
    private String correspondentcode;

    /**
     * 账号类型, 0:对公账号、1:对私账号
     */
    private String accountType;

    /**
     * 默认银行, true:是、false:否
     */
    private Boolean defaultbank;

    /**
     * 状态, false:启用、true:停用
     */
    private Boolean stopstatus;

    /**
     * 备注,支持多语
     */
    private MultiLanguageVO memo;

    /**
     * 银行特征自定义项
     */
    private Object vendorBankCharacterDefine;

    /**
     * 操作标识, Insert:新增、Update:更新
     */
    private String _status;

    /**
     * 代理电票银行网点
     */
    private String elecBillBankNode;

    /**
     * 代理电票银行网点编码，用来翻译代理电票银行网点
     */
    private String elecBillBankNode_code;
} 