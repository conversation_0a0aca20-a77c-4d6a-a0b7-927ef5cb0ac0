package com.yonyou.ucf.mdf.sync.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest.QueryParams;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDoc;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeResponse;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.publisher.CustomDocSyncEventPublisher;
import com.yonyou.ucf.mdf.sync.service.CustomDocSyncService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Service
public class CustomDocSyncServiceImpl implements CustomDocSyncService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Autowired
	private CustomDocSyncEventPublisher customDocSyncEventPublisher;

	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 同步起始页
	 */
	private Integer dataStart = 0;

	/**
	 * 同步每页获取数量
	 */
	@Value("${kingdee.sync.pageSize}")
	private Integer pageSize = 100;

	@Override
	public void syncCustomDoc(LocalDateTime startTime, LocalDateTime endTime) {
		log.error("开始同步自定义档案数据，时间范围：{} - {}", startTime, endTime);
		try {
			// 1. 构建金蝶接口请求参数
			KingdeeEasRequest.QueryParams queryParams = new KingdeeEasRequest.QueryParams();
			queryParams.setModifytimeStart(startTime);
			queryParams.setModifytimeEnd(endTime);

			KingdeeEasRequest<KingdeeEasRequest.QueryParams> request = new KingdeeEasRequest<>();
			request.setData(queryParams);
			request.setDataStart(dataStart);
			request.setPageSize(pageSize);

			// 2. 调用金蝶接口获取数据
			String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA,
					"ExpGeneralAsstActType", request);
			if (StringUtils.isBlank(result)) {
				log.error("未获取到自定义档案数据");
				return;
			}
			KingdeeResponse<KingdeeCustomDoc> response = JSON.parseObject(result,
					new TypeReference<KingdeeResponse<KingdeeCustomDoc>>() {
					});

			if (response == null || response.getRows() == null) {
				log.error("未获取到自定义档案数据");
				return;
			}

			// 3. 发布同步事件
			publishEvent(response.getRows());

			// 4. 如果还有更多数据，继续获取
			if (response.getRows().size() == pageSize) {
				request.setDataStart(request.getDataStart() + pageSize);
				syncCustomDoc(request);
			}
			log.error("同步自定义档案数据结束------");
		} catch (Exception e) {
			log.error("同步自定义档案数据失败", e);
			throw new RuntimeException("同步自定义档案数据失败", e);
		}
	}

	/**
	 * 递归调用
	 * 
	 * @param request
	 */
	private void syncCustomDoc(KingdeeEasRequest<QueryParams> request) {
		// 2. 调用金蝶接口获取数据
		String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpGeneralAsstActType",
				request);
		if (StringUtils.isBlank(result)) {
			log.error("未获取到自定义档案数据");
			return;
		}
		KingdeeResponse<KingdeeCustomDoc> response = JSON.parseObject(result,
				new TypeReference<KingdeeResponse<KingdeeCustomDoc>>() {
				});

		if (response == null || response.getRows() == null) {
			log.error("未获取到自定义档案数据");
			return;
		}

		// 3. 发布同步事件
		publishEvent(response.getRows());

		// 4. 如果还有更多数据，继续获取
		if (response.getRows().size() == pageSize) {
			request.setDataStart(request.getDataStart() + pageSize);
			syncCustomDoc(request);
		}
	}

	/**
	 * 发布事件
	 * 
	 * @param kingdeeCustomDocs
	 */
	@Override
	public void publishEvent(List<KingdeeCustomDoc> kingdeeCustomDocs) {
		for (KingdeeCustomDoc kingdeeCustomDoc : kingdeeCustomDocs) {
			try {
				KingdeeCustomDoc failCustomDoc = (KingdeeCustomDoc) ehCacheUtil.getFail(CacheConstant.CACHE_CUSTOM_DOC,
						kingdeeCustomDoc.getNumber());
				if (failCustomDoc != null) {
					customDocSyncEventPublisher.publish(kingdeeCustomDoc);
				} else {
					KingdeeCustomDoc successCustomDoc = (KingdeeCustomDoc) ehCacheUtil
							.getSuccess(CacheConstant.CACHE_CUSTOM_DOC, kingdeeCustomDoc.getNumber());
					if (successCustomDoc == null) {
						customDocSyncEventPublisher.publish(kingdeeCustomDoc);
					} else if (!successCustomDoc.equals(kingdeeCustomDoc)) {
						customDocSyncEventPublisher.publish(kingdeeCustomDoc);
					}
				}
			} catch (Exception e) {
				log.error("发布自定义档案同步事件失败，自定义档案编码：{}", kingdeeCustomDoc.getNumber(), e);
			}
		}
	}
}