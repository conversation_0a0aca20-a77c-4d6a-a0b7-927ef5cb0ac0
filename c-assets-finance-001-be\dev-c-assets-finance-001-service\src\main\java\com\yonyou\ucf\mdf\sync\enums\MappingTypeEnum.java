package com.yonyou.ucf.mdf.sync.enums;

/**
 * 映射类型枚举
 * 
 * <AUTHOR>
 * @date 2025/6/12
 */
public enum MappingTypeEnum {
	/**
	 * 供应商主键/编码映射
	 */
	VENDOR_MAPPING("iuap_apdoc_coredoc.aa_vendor", "id", "code", "dr = 0 and ytenant_id = ?"),

	/**
	 * 部门主键/金蝶编码映射
	 */
	DEPT_MAPPING("iuap_apdoc_basedoc.org_admin", "id", "description", "dr = 0 and enable = 1 and ytenant_id = ?"),

	/**
	 * 资金来源主键/编码映射
	 */
	CAPITAL_SOURCE_MAPPING("amc_aim.pam_capital_source", "id", "code", "dr = 0 and ytenant_id = ?"),

	/**
	 * 员工主键/金蝶编码映射
	 */
	STAFF_MAPPING("iuap_apdoc_basedoc.bd_staff", "id", "remark", "dr = 0 and enable = 1 and ytenant_id = ?"),

	/**
	 * 用户主键/员工主键
	 */
	USER_MAPPING("iuap_apdoc_basedoc.bd_staff", "user_id", "id", "dr = 0 and enable = 1 and ytenant_id = ?"),

	/**
	 * 币种主键/编码映射
	 */
	CURRENCY_MAPPING("iuap_apdoc_basedoc.bd_currency_tenant", "id", "code", "dr = 0 and enable = 1 and ytenant_id = ?"),

	/**
	 * 资产类别主键/金蝶编码映射
	 */
	ASSET_CATEGORY_MAPPING("amc_ambd.pam_category", "id", "memo", "dr = 0 and enablestate = 2 and ytenant_id = ?"),

	/**
	 * 资产位置主键/位置名称映射
	 */
	LOCATION_MAPPING("amc_ambd.pam_location", "id", "location_name", "dr = 0 and ytenant_id = ?"),

	/**
	 * 处置方式主键/编码映射
	 */
	REDUCE_WAY_MAPPING("iuap_apdoc_finbd.bd_reduceway", "id", "code", "dr = 0 and enable = 1 and ytenant_id = ?"),

	/**
	 * 调拨原因主键/名称映射
	 */
	REASON_MAPPING("amc_aim.pam_reason", "id", "name", "dr = 0 and enablestate = 2 and ytenant_id = ?"),

	/**
	 * 组织主键/金蝶编码映射
	 */
	ORG_MAPPING("iuap_apdoc_basedoc.org_orgs", "id", "description", "ytenant_id = ?");

	private final String tableName;
	private final String keyField;
	private final String valueField;
	private final String condition;

	MappingTypeEnum(String tableName, String keyField, String valueField, String condition) {
		this.tableName = tableName;
		this.keyField = keyField;
		this.valueField = valueField;
		this.condition = condition;
	}

	public String getTableName() {
		return tableName;
	}

	public String getKeyField() {
		return keyField;
	}

	public String getValueField() {
		return valueField;
	}

	public String getCondition() {
		return condition;
	}

}
