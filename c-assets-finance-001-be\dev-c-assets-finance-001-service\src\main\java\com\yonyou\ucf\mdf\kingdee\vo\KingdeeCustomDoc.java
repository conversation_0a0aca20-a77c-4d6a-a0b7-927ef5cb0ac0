package com.yonyou.ucf.mdf.kingdee.vo;

import com.alibaba.fastjson.JSON;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金蝶自定义档案VO
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@EqualsAndHashCode
@Data
public class KingdeeCustomDoc {

	/**
	 * 编码
	 */
	private String number;

	/**
	 * 创建公司编码
	 */
	private String creatorCompanyNum;

	/**
	 * 创建公司名称
	 */
	private String creatorCompanyName;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 创建者编码
	 */
	private String creatorNum;

	/**
	 * 创建者名称
	 */
	private String creatorName;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 自定义档案类别名称
	 */
	private String groupName;

	/**
	 * 自定档案类别编码
	 */
	private String groupNum;

	/**
	 * 上级核算项目名称
	 */
	private String parentName;

	/**
	 * 上级核算项目编码
	 */
	private String parentNum;

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}