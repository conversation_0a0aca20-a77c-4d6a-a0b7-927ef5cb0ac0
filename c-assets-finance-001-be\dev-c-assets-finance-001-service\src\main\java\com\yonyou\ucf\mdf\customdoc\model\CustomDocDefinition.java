package com.yonyou.ucf.mdf.customdoc.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 自定义档案定义实体类
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomDocDefinition {

	/**
	 * 档案编码，更新时编码不可修改
	 */
	private String code;

	/**
	 * 档案名称
	 */
	private String name;

	/**
	 * 是否是树形 false：非树形档案 true：树形档案
	 */
	private Boolean classify;

	/**
	 * 是否分级管控 false: 不是分级管控档案 true：分级管控档案
	 */
	private Boolean mcflag;

	/**
	 * 是否是系统预制档案 false: 不是系统预置档案 true：系统预置档案
	 */
	private Boolean ispreset;

	/**
	 * 操作状态 Insert 新增 Update 更新
	 */
	private String _status;

	/**
	 * 主键id 修改时必填，新增时为空
	 */
	private String id;
}