package com.yonyou.ucf.mdf.sso.common;

import cn.hutool.json.JSONUtil;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @className: HttpClientUtil
 * @author: wjc
 * @date: 2025/5/20 10:37
 * @Version: 1.0
 * @description:
 */
public class HttpClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    public static final String POST = "post";

    public static final String GET = "get";

    private static long CONNECT_TIMEOUT = 60;
    private static long READ_TIMEOUT = 60;
    private static long WRITE_TIMEOUT = 60;

    public static void setConnectTimeOut(long connectTimeout) {
        CONNECT_TIMEOUT = connectTimeout;
        READ_TIMEOUT = connectTimeout;
    }

    public static String doGet(String url, String path, Map<String, Object> headerParams, Map<String, Object> mapParams) {
        String resultString = "";
        Response response = null;
        try {
            //******** 一、 创建 httpClient 对象***********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            String params = setUrlParams(mapParams);
            StringBuffer strinb = new StringBuffer();
            strinb.append(url);
            strinb.append(path);
            strinb.append(params);

            // ******** 二、创建request 对象*************
            Request.Builder builder = new Request.Builder()
                    .url(strinb.toString())
                    .get();
            if (null != headerParams && headerParams.size() > 0) {
                for (Map.Entry<String, Object> entry : headerParams.entrySet()) {
                    String key = entry.getKey();
                    Object val = entry.getValue();
                    builder.addHeader(key, val.toString());
                }
            }
            Request request = builder.build();
            //********* 三、发送请求 ********************

            response = httpClient.newCall(request).execute();

            //********* 四、对响应进行处理 ***************
            //1、如果 http 状态码不在 【200 ，300】区间内，抛出异常
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            // 因为服务器返回的数据可能非常大，所以必须通过数据流的方式来进行访问
            // 提供了诸如 string() 和 bytes() 这样的方法将流内的数据一次性读取完毕
            resultString = response.body().string();

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException(e);
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException(e);
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                response.body().close();
            }
        }
        return resultString;
    }

    public static String doPost(String url, String path, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {

        Response response = null;
        String resultString = "";
        try {
            //******* 一、创建 httpClient 对象 **********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            StringBuffer stringB = new StringBuffer();
            stringB.append(url);
            stringB.append(path);
            if (null != queryParams && queryParams.size() > 0) {
                String params = setUrlParams(queryParams);
                stringB.append(params);
            }
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(JSONUtil.toJsonStr(obj), mediaType);

            //******** 二、创建 request 对象 ************

            Request.Builder builder = new Request.Builder()
                    .url(stringB.toString())
                    .post(body);
            if (null != headerParams && headerParams.size() > 0) {
                for (Map.Entry<String, Object> entry : headerParams.entrySet()) {
                    String key = entry.getKey();
                    Object val = entry.getValue();
                    builder.addHeader(key, val.toString());
                }
            }
            Request request = builder.build();
            //******** 三、执行请求 ********************
            response = httpClient.newCall(request).execute();

            //******** 四、处理响应 ********************
            int code = response.code();
            String message = response.message();
            if (!response.isSuccessful()) {
                throw new IOException(" Unexpect code " + response);
            }

            resultString = response.body().string();

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException(e);
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException(e);
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                response.body().close();
            }
        }
        return resultString;
    }

    public static String doDelete(String url, String path, Map<String, Object> headerParams, Map<String, Object> queryParams) {

        Response response = null;
        String resultString = "";
        try {
            //******* 一、创建 httpClient 对象 **********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            StringBuffer stringB = new StringBuffer();
            stringB.append(url);
            stringB.append(path);
            if (null != queryParams && queryParams.size() > 0) {
                String params = setUrlParams(queryParams);
                stringB.append(params);
            }
            MediaType mediaType = MediaType.parse("application/json");

            //******** 二、创建 request 对象 ************

            Request.Builder builder = new Request.Builder()
                    .url(stringB.toString())
                    .delete();
            if (null != headerParams && headerParams.size() > 0) {
                for (Map.Entry<String, Object> entry : headerParams.entrySet()) {
                    String key = entry.getKey();
                    Object val = entry.getValue();
                    builder.addHeader(key, val.toString());
                }
            }
            Request request = builder.build();
            //******** 三、执行请求 ********************
            response = httpClient.newCall(request).execute();

            //******** 四、处理响应 ********************
            if (!response.isSuccessful()) {
                throw new IOException(" Unexpect code " + response);
            }
            resultString = response.body().string();

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException(e);
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException(e);
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                response.body().close();
            }
        }
        return resultString;
    }

    public static String doPut(String url, String path, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {

        Response response = null;
        String resultString = "";
        try {
            //******* 一、创建 httpClient 对象 **********
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
                    .build();

            StringBuffer stringB = new StringBuffer();
            stringB.append(url);
            stringB.append(path);
            if (null != queryParams && queryParams.size() > 0) {
                String params = setUrlParams(queryParams);
                stringB.append(params);
            }
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(JSONUtil.toJsonStr(obj), mediaType);

            //******** 二、创建 request 对象 ************

            Request.Builder builder = new Request.Builder()
                    .url(stringB.toString())
                    .put(body);
            if (null != headerParams && headerParams.size() > 0) {
                for (Map.Entry<String, Object> entry : headerParams.entrySet()) {
                    String key = entry.getKey();
                    Object val = entry.getValue();
                    builder.addHeader(key, val.toString());
                }
            }
            Request request = builder.build();
            //******** 三、执行请求 ********************
            response = httpClient.newCall(request).execute();

            //******** 四、处理响应 ********************
            if (!response.isSuccessful()) {
                throw new IOException(" Unexpect code " + response);
            }

            resultString = response.body().string();

        } catch (SocketTimeoutException e) {
            // 超时
            throw new RuntimeException(e);
        } catch (IOException e) {
            // 网络IO异常
            throw new RuntimeException(e);
        } catch (Exception e) {
            // 其他异常
            throw new RuntimeException(e);
        } finally {
            if (response != null) {
                // body 必须被关闭，否则会发生资源泄漏；
                response.body().close();
            }
        }
        return resultString;
    }

    public static String setUrlParams(Map<String, Object> mapParams) {

        // 是否已经设置了问号
        boolean hasSetQuestionMark = false;
        StringBuffer strParams = new StringBuffer("");
        if (mapParams != null) {
            Iterator<String> iterator = mapParams.keySet().iterator();
            String key = "";
            while (iterator.hasNext()) {
                try {
                    key = iterator.next().toString();
                    if (!hasSetQuestionMark) {
                        strParams.append("?").append(key).append("=")
                                .append(mapParams.get(key).toString());
                        // .append(URLEncoder.encode(mapParams.get(key).toString(), "utf-8"));
                        hasSetQuestionMark = true;
                        continue;
                    }
                    strParams.append("&").append(key).append("=")
                            .append(mapParams.get(key).toString());
                    //.append(URLEncoder.encode(mapParams.get(key).toString(), "utf-8"));
                } catch (Exception e) {
                    logger.error("参数设置异常：", e);
                }
            }
        }
        return strParams.toString();
    }

    public static String doGet(String url, Map<String, Object> headerParams, Map<String, Object> mapParams) {
        logger.error("请求地址：{},params：{}，headerParams:{}", url, JSONUtil.toJsonStr(mapParams), JSONUtil.toJsonStr(headerParams));
        String result = doGet(url, "", headerParams, mapParams);
        logger.error("请求地址：{},result:{}", url,result);
        return result;
    }

    public static String doPost(String url, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {
        return doPost(url, "", headerParams, queryParams, obj);
    }

    public static String doDelete(String url, Map<String, Object> headerParams, Map<String, Object> queryParams) {
        return doDelete(url, "", headerParams, queryParams);
    }

    public static String doPut(String url, Map<String, Object> headerParams, Map<String, Object> queryParams, Object obj) {
        return doPut(url, "", headerParams, queryParams, obj);
    }

}
