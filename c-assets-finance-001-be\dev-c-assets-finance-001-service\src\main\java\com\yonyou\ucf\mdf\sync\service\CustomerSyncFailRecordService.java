package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.model.CustomerSyncFailRecord;

/**
 * 客户同步失败记录服务
 * 
 * <AUTHOR>
 *
 *         2025年5月27日
 */
public interface CustomerSyncFailRecordService {

	/**
	 * 保存客户同步失败记录（用于后续定时同步）
	 * 
	 * @param failRecord
	 */
	void saveFailRecord(CustomerSyncFailRecord failRecord);

	/**
	 * 根据编码删除
	 * 
	 * @param code
	 */
	void deleteByCode(String code);

}
