# redis缓存index
mdd.uimeta.prop.uimetaRedisIndex=6
# viewmodel是否应用redis 外部缓存
mdd.uimeta.prop.isRedisCache=true
# viewmodel是否应用 上下文缓存
mdd.uimeta.prop.isContextCache=false
# uimeta 规则级别定义 ruleLvs; 多个级别逗号分隔
mdd.uimeta.prop.uimetaRuleLvs=common,uimeta
# 是否走metaService服务
mdd.uimeta.prop.isMetaServer=true
# 统一存储服务rest地址
mdd.uimeta.prop.metaServerUrl=@uimetadata.domain@/mdf
# 统一存储SDK内部走mongoDB
mdd.uimeta.prop.isMetaServerFromDao=true