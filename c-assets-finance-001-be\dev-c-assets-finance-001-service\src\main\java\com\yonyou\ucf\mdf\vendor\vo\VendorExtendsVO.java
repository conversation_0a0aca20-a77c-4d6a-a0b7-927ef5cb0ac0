package com.yonyou.ucf.mdf.vendor.vo;

import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;
import lombok.Data;

/**
 * 供应商业务属性实体类
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Data
public class VendorExtendsVO {
    /**
     * 进项税率id
     */
    private String taxitems;

    /**
     * 进项税率编码
     */
    private String taxitems_code;

    /**
     * 开票供应商ID
     */
    private String invoicevendor;

    /**
     * 价格标识：0含税优先，1无税优先
     */
    private String priceTag;

    /**
     * 结算方式ID
     */
    private String settlemethod;

    /**
     * 结算方式code
     */
    private String settlemethod_code;

    /**
     * 交易币种ID
     */
    private String currency;

    /**
     * 交易币种code
     */
    private String currency_code;

    /**
     * 汇率类型ID
     */
    private String exchangeratetype;

    /**
     * 汇率类型code
     */
    private String exchangeratetype_code;

    /**
     * 专管部门id
     */
    private String department;

    /**
     * 专管部门编码
     */
    private String department_code;

    /**
     * 专管业务员ID
     */
    private String person;

    /**
     * 供应商简称,支持多语
     */
    private MultiLanguageVO simplename;

    /**
     * 助记码
     */
    private String helpcode;

    /**
     * 付款协议ID
     */
    private String paymentagreement;

    /**
     * 付款协议编码
     */
    private String paymentagreement_code;

    /**
     * 信用期限
     */
    private Long creditServiceDay;

    /**
     * 发货方式ID
     */
    private String shipvia;

    /**
     * 发货方式code
     */
    private String shipvia_code;

    /**
     * 备注,支持多语
     */
    private MultiLanguageVO remark;

    /**
     * 运输区域ID
     */
    private Long shipregion;

    /**
     * 运输区域编码
     */
    private String shipregion_code;

    /**
     * 开票方式，1：先货后票，2：货票同行 3:先票后货
     */
    private String invoiceBizType;

    /**
     * 风险状态（枚举）0:正常1:冻结2:黑名单
     */
    private String freezestatus;

    /**
     * 生命周期状态id
     */
    private String lifecycleStatus;

    /**
     * 生命周期状态编码
     */
    private String lifecycleStatus_code;

    /**
     * 状态信息-状态：true：停用；false：启用
     */
    private Boolean stopstatus;

    /**
     * 财务协同, true:是、false:否
     */
    private Boolean financialSynergy;
} 