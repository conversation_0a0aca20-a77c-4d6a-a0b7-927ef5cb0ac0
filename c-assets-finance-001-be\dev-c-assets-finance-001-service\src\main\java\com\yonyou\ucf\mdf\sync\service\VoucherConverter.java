package com.yonyou.ucf.mdf.sync.service;

import java.util.List;

import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.voucher.model.Voucher;

/**
 * 金蝶凭证数据转换接口
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface VoucherConverter {

    /**
     * 将金蝶凭证明细列表转换为本系统凭证
     * 注：传入的明细列表应该属于同一张凭证
     *
     * @param kingdeeVouchers 金蝶凭证明细数据列表
     * @return 本系统凭证数据
     */
    Voucher convert(List<KingdeeVoucher> kingdeeVouchers);
} 