package com.yonyou.ucf.mdf.kingdee.voucher.vo;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import lombok.Data;

/**
 * 金蝶凭证查询返回实体
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Data
public class KingdeeVoucherResponse {

	/**
	 * 状态码 000:成功 001:失败（经过实际验证，金蝶返回数据，这个字段是空字符串，没有任何值）
	 */
	private String err;

	/**
	 * 错误信息 错误信息，它是过滤条件的校验信息，如果校验失败（也就是说errs有数据），则不会进行导出处理。 它的格式为：key[value]:错误信息。
	 */
	private List<String> errs;

	/**
	 * 凭证数据
	 */
	private List<List<String>> rows;

	/**
	 * 后端处理总用时，单位毫秒
	 */
	private Integer totalTime;

	/**
	 * 引出的凭证数量
	 */
	private String voucherCount;

	/**
	 * 判断是否成功
	 * 
	 * @return
	 */
	public boolean isSuccess() {
		if (errs != null && errs.size() > 0) {
			return false;
		}
		return true;
	}

	/**
	 * 获取错误信息
	 * 
	 * @return
	 */
	public String errMsg() {
		if (!isSuccess()) {
			return errs.stream().collect(Collectors.joining(","));
		}
		return "";
	}

	/**
	 * 将rows数据转换为KingdeeVoucher列表
	 * 
	 * @return KingdeeVoucher列表
	 */
	public List<KingdeeVoucher> convertToVouchers() {
		if (CollectionUtils.isEmpty(rows) || rows.size() < 2) {
			return new ArrayList<>();
		}

		List<String> headers = rows.get(0);
		List<KingdeeVoucher> vouchers = new ArrayList<>();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

		for (int i = 1; i < rows.size(); i++) {
			List<String> row = rows.get(i);
			if (CollectionUtils.isEmpty(row)) {
				continue;
			}

			KingdeeVoucher voucher = new KingdeeVoucher();
			for (int j = 0; j < headers.size() && j < row.size(); j++) {
				String header = headers.get(j);
				String value = row.get(j);
				if (StringUtils.isBlank(value)) {
					continue;
				}

				switch (header) {
				case "companyNumber":
					voucher.setCompanyNumber(value);
					break;
				case "companyName":
					voucher.setCompanyName(value);
					break;
				case "voucherNumber":
					voucher.setVoucherNumber(value);
					break;
				case "voucherId":
					voucher.setVoucherId(value);
					break;
				case "importKey":
					voucher.setImportKey(value);
					break;
				case "periodYear":
					voucher.setPeriodYear(value);
					break;
				case "periodNumber":
					voucher.setPeriodNumber(value);
					break;
				case "bookedDate":
					try {
						voucher.setBookedDate(dateFormat.parse(value));
					} catch (ParseException e) {
						// 日期解析失败，跳过
					}
					break;
				case "bizDate":
					try {
						voucher.setBizDate(dateFormat.parse(value));
					} catch (ParseException e) {
						// 日期解析失败，跳过
					}
					break;
				case "voucherType":
					voucher.setVoucherType(value);
					break;
				case "bizStatus":
					voucher.setBizStatus(value);
					break;
				case "description":
					voucher.setDescription(value);
					break;
				case "voucherAbstract":
					voucher.setVoucherAbstract(value);
					break;
				case "attaches":
					voucher.setAttaches(Integer.parseInt(value));
					break;
				case "isCheck":
					voucher.setIsCheck(value);
					break;
				case "creator":
					voucher.setCreator(value);
					break;
				case "auditor":
					voucher.setAuditor(value);
					break;
				case "poster":
					voucher.setPoster(value);
					break;
				case "cashier":
					voucher.setCashier(value);
					break;
				case "sourceSys":
					voucher.setSourceSys(value);
					break;
				case "sourceType":
					voucher.setSourceType(value);
					break;
				case "sourceBillId":
					voucher.setSourceBillId(value);
					break;
				case "entrySeq":
					voucher.setEntrySeq(value);
					break;
				case "profitCenterNumber":
					voucher.setProfitCenterNumber(value);
					break;
				case "accountName":
					voucher.setAccountName(value);
					break;
				case "accountNumber":
					voucher.setAccountNumber(value);
					break;
				case "currencyNumber":
					voucher.setCurrencyNumber(value);
					break;
				case "entryDC":
					voucher.setEntryDC(value);
					break;
				case "measurement":
					voucher.setMeasurement(value);
					break;
				case "cussent":
					voucher.setCussent(value);
					break;
				case "customerNumber":
					voucher.setCustomerNumber(value);
					break;
				case "localRate":
					voucher.setLocalRate(new BigDecimal(value));
					break;
				case "originalAmount":
					voucher.setOriginalAmount(new BigDecimal(value));
					break;
				case "debitAmount":
					voucher.setDebitAmount(new BigDecimal(value));
					break;
				case "creditAmount":
					voucher.setCreditAmount(new BigDecimal(value));
					break;
				case "qty":
					voucher.setQty(new BigDecimal(value));
					break;
				case "price":
					voucher.setPrice(new BigDecimal(value));
					break;
				case "asstSeq":
					voucher.setAsstSeq(value);
					break;
				case "assistAbstract":
					voucher.setAssistAbstract(value);
					break;
				case "assistBizDate":
					try {
						voucher.setAssistBizDate(dateFormat.parse(value));
					} catch (ParseException e) {
						// 日期解析失败，跳过
					}
					break;
				case "assistEndDate":
					try {
						voucher.setAssistEndDate(dateFormat.parse(value));
					} catch (ParseException e) {
						// 日期解析失败，跳过
					}
					break;
				case "cpCustomerNumber":
					voucher.setCpCustomerNumber(value);
					break;
				case "cpSupplierNumber":
					voucher.setCpSupplierNumber(value);
					break;
				case "cpOrgUnitNumber":
					voucher.setCpOrgUnitNumber(value);
					break;
				case "cpMaterialNumber":
					voucher.setCpMaterialNumber(value);
					break;
				case "settlementType":
					voucher.setSettlementType(value);
					break;
				case "settlementNumber":
					voucher.setSettlementNumber(value);
					break;
				case "bizNumber":
					voucher.setBizNumber(value);
					break;
				case "ticketNumber":
					voucher.setTicketNumber(value);
					break;
				case "invoiceNumber":
					voucher.setInvoiceNumber(value);
					break;
				case "asstActType1":
					voucher.setAsstActType1(value);
					break;
				case "asstActNumber1":
					voucher.setAsstActNumber1(value);
					break;
				case "asstActName1":
					voucher.setAsstActName1(value);
					break;
				case "asstActType2":
					voucher.setAsstActType2(value);
					break;
				case "asstActNumber2":
					voucher.setAsstActNumber2(value);
					break;
				case "asstActName2":
					voucher.setAsstActName2(value);
					break;
				case "asstActType3":
					voucher.setAsstActType3(value);
					break;
				case "asstActNumber3":
					voucher.setAsstActNumber3(value);
					break;
				case "asstActName3":
					voucher.setAsstActName3(value);
					break;
				case "asstActType4":
					voucher.setAsstActType4(value);
					break;
				case "asstActNumber4":
					voucher.setAsstActNumber4(value);
					break;
				case "asstActName4":
					voucher.setAsstActName4(value);
					break;
				case "asstActType5":
					voucher.setAsstActType5(value);
					break;
				case "asstActNumber5":
					voucher.setAsstActNumber5(value);
					break;
				case "asstActName5":
					voucher.setAsstActName5(value);
					break;
				case "asstActType6":
					voucher.setAsstActType6(value);
					break;
				case "asstActNumber6":
					voucher.setAsstActNumber6(value);
					break;
				case "asstActName6":
					voucher.setAsstActName6(value);
					break;
				case "asstActType7":
					voucher.setAsstActType7(value);
					break;
				case "asstActNumber7":
					voucher.setAsstActNumber7(value);
					break;
				case "asstActName7":
					voucher.setAsstActName7(value);
					break;
				case "asstActType8":
					voucher.setAsstActType8(value);
					break;
				case "asstActNumber8":
					voucher.setAsstActNumber8(value);
					break;
				case "asstActName8":
					voucher.setAsstActName8(value);
					break;
				case "itemFlag":
					voucher.setItemFlag(value);
					break;
				case "oppAccountSeq":
					voucher.setOppAccountSeq(value);
					break;
				case "oppAsstSeq":
					voucher.setOppAsstSeq(value);
					break;
				case "primaryItem":
					voucher.setPrimaryItem(value);
					break;
				case "primaryCoef":
					voucher.setPrimaryCoef(value);
					break;
				case "supplyItem":
					voucher.setSupplyItem(value);
					break;
				case "supplyCoef":
					voucher.setSupplyCoef(value);
					break;
				case "fullInfoItem":
					voucher.setFullInfoItem(value);
					break;
				case "type":
					voucher.setType(value);
					break;
				case "cashflowAmountOriginal":
					try {
						voucher.setCashflowAmountOriginal(new BigDecimal(value));
					} catch (NumberFormatException e) {
						// 数值转换失败，跳过
					}
					break;
				case "cashflowAmountLocal":
					try {
						voucher.setCashflowAmountLocal(new BigDecimal(value));
					} catch (NumberFormatException e) {
						// 数值转换失败，跳过
					}
					break;
				case "cashflowAmountRpt":
					try {
						voucher.setCashflowAmountRpt(new BigDecimal(value));
					} catch (NumberFormatException e) {
						// 数值转换失败，跳过
					}
					break;
				}
			}
			vouchers.add(voucher);
		}
		return vouchers;
	}
}
