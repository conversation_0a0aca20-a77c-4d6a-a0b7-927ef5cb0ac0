package com.yonyou.ucf.mdf.sync.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest.QueryParams;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomer;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeResponse;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.publisher.CustomerSyncEventPublisher;
import com.yonyou.ucf.mdf.sync.service.CustomerSyncService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 客户数据同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Service
public class CustomerSyncServiceImpl implements CustomerSyncService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Autowired
	private CustomerSyncEventPublisher customerSyncEventPublisher;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 同步起始页
	 */
	private Integer dataStart = 0;

	/**
	 * 同步每页获取数量
	 */
	@Value("${kingdee.sync.pageSize}")
	private Integer pageSize = 100;

	@Override
	public void syncCustomer(LocalDateTime startTime, LocalDateTime endTime) {
		log.error("开始同步客户数据，时间范围：{} - {}", startTime, endTime);
		try {
			// 1. 构建金蝶接口请求参数
			KingdeeEasRequest.QueryParams queryParams = new KingdeeEasRequest.QueryParams();
			queryParams.setModifytimeStart(startTime);
			queryParams.setModifytimeEnd(endTime);

			KingdeeEasRequest<KingdeeEasRequest.QueryParams> request = new KingdeeEasRequest<>();
			request.setData(queryParams);
			request.setDataStart(dataStart);
			request.setPageSize(pageSize);

			// 2. 调用金蝶接口获取数据
			String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpCustomer",
					request);
			if (StringUtils.isBlank(result)) {
				log.error("未获取到客户数据");
				return;
			}
			KingdeeResponse<KingdeeCustomer> response = JSON.parseObject(result,
					new TypeReference<KingdeeResponse<KingdeeCustomer>>() {
					});

			if (response == null || response.getRows() == null) {
				log.error("未获取到客户数据");
				return;
			}

			// 3. 发布同步事件
			publishEvent(response.getRows());

			// 4. 如果还有更多数据，继续获取
			if (response.getRows().size() == pageSize) {
				request.setDataStart(request.getDataStart() + pageSize);
				syncCustomer(request);
			}
			log.error("同步客户数据结束------");
		} catch (Exception e) {
			log.error("同步客户数据失败", e);
			throw new RuntimeException("同步客户数据失败", e);
		}
	}

	/**
	 * 递归调用
	 * 
	 * @param request
	 */
	private void syncCustomer(KingdeeEasRequest<QueryParams> request) {
		// 2. 调用金蝶接口获取数据
		String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpCustomer", request);
		if (StringUtils.isBlank(result)) {
			log.error("未获取到客户数据");
			return;
		}
		KingdeeResponse<KingdeeCustomer> response = JSON.parseObject(result,
				new TypeReference<KingdeeResponse<KingdeeCustomer>>() {
				});

		if (response == null || response.getRows() == null) {
			log.error("未获取到客户数据");
			return;
		}

		// 3. 发布同步事件
		publishEvent(response.getRows());

		// 4. 如果还有更多数据，继续获取
		if (response.getRows().size() == pageSize) {
			request.setDataStart(request.getDataStart() + pageSize);
			syncCustomer(request);
		}
	}

	/**
	 * 发布事件
	 * 
	 * @param kingdeeCustomers
	 */
	@Override
	public void publishEvent(List<KingdeeCustomer> kingdeeCustomers) {
		for (KingdeeCustomer kingdeeCustomer : kingdeeCustomers) {
			try {
				KingdeeCustomer failCustomer = (KingdeeCustomer) ehCacheUtil
						.getFail(CacheConstant.CACHE_KINGDEE_CUSTOMER, kingdeeCustomer.getNumber());
				if (failCustomer != null) {
					customerSyncEventPublisher.publish(kingdeeCustomer);
				} else {
					KingdeeCustomer successCustomer = (KingdeeCustomer) ehCacheUtil
							.getSuccess(CacheConstant.CACHE_KINGDEE_CUSTOMER, kingdeeCustomer.getNumber());
					if (successCustomer == null) {
						customerSyncEventPublisher.publish(kingdeeCustomer);
					} else if (!successCustomer.equals(kingdeeCustomer)) {
						customerSyncEventPublisher.publish(kingdeeCustomer);
					}
				}
			} catch (Exception e) {
				log.error("发布客户同步事件失败，客户编码：{}", kingdeeCustomer.getNumber(), e);
			}
		}
	}

}