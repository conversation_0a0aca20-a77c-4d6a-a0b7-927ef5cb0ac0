package com.yonyou.ucf.mdf.kingdee.fixedasset.service;

import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetClearParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.CommonResponse;

/**
 * 固定资产清理服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface FixedAssetClearService {
    
    /**
     * 清理固定资产
     *
     * @param param 固定资产清理参数
     * @return 响应结果
     */
    CommonResponse clearFixedAsset(FixedAssetClearParam param);
} 