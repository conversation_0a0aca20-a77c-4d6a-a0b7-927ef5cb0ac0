# VS Code Java项目(Eclipse迁移)配置指南

本文档旨在帮助您将在Eclipse中开发的历史Java项目成功迁移到Visual Studio Code (VS Code)中，并解决常见的配置问题，特别是Lombok注解不识别导致的编辑器报错。

## 1. 安装必要的VS Code扩展

为了在VS Code中获得良好的Java开发体验，您需要安装以下两个核心扩展：

1.  **Extension Pack for Java**:
    *   **作用**: 这是由Microsoft官方提供的Java开发扩展包，它捆绑了多个关键插件，包括：
        *   Language Support for Java™ by Red Hat
        *   Debugger for Java
        *   Test Runner for Java
        *   Maven for Java
        *   Project Manager for Java
    *   **安装**:
        1.  打开VS Code。
        2.  点击左侧边栏的 **扩展(Extensions)** 图标 (快捷键: `Ctrl+Shift+X`)。
        3.  在搜索框中输入 `Extension Pack for Java`。
        4.  找到由Microsoft发布的扩展包，点击 **Install**。

2.  **Lombok Annotations Support for VS Code**:
    *   **作用**: 这个插件是解决Lombok注解不识别问题的关键。它能够让VS Code的Java语言服务器正确解析Lombok的注解，如 `@Data`, `@Getter`, `@Setter` 等。
    *   **安装**:
        1.  在扩展市场中搜索 `Lombok Annotations Support for VS Code`。
        2.  找到由 `Gabriel Basilio Brito` 发布的插件，点击 **Install**。

## 2. 配置VS Code设置 (settings.json)

安装完插件后，您需要配置VS Code的设置，以确保Java语言服务器能够找到并使用Lombok。

1.  **打开 `settings.json`**:
    *   使用快捷键 `Ctrl+Shift+P` 打开命令面板。
    *   输入 `Preferences: Open User Settings (JSON)` 并回车。

2.  **添加或修改Java配置**:
    在 `settings.json` 文件中，添加以下配置。这段配置会告诉Java语言服务器Lombok `jar`包的位置。通常，Maven会将依赖下载到您的本地 `.m2` 仓库中。您需要将 `C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar` 替换为您系统中Lombok jar包的实际路径。

    **注意**: 您需要根据项目中实际使用的Lombok版本来确定路径。虽然在`pom.xml`中没有直接看到版本号，但通常可以从父POM继承，或者在Maven依赖树中找到。如果不确定，可以先在本地的`.m2`仓库中搜索`lombok`来找到具体的jar包路径。

    ```json
    "java.jdt.ls.vmargs": "-javaagent:\"C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.24/lombok-1.18.24.jar\""
    ```
    *请务必将 `YourUsername` 和 `1.18.24` 替换为您的实际用户名和项目中使用的Lombok版本。*

## 3. 重新加载并清理Java项目

完成上述配置后，需要让VS Code重新加载Java项目以应用新的设置。

1.  **打开命令面板** (`Ctrl+Shift+P`)。
2.  运行 **Java: Clean Java Language Server Workspace** 命令。这个命令会清理掉旧的缓存和设置，强制Java语言服务器重新初始化项目。
3.  **重启VS Code**: 清理工作区后，最好重启一下VS Code，以确保所有设置都已生效。

## 4. 验证配置

重启VS Code后，打开一个使用了Lombok注解的Java文件（例如，任何一个包含 `@Data` 或 `@Slf4j` 注解的类）。

*   **检查错误**: 之前关于Lombok注解的错误应该已经消失了。
*   **检查代码提示**: 尝试在一个使用了 `@Data` 注解的类的实例上调用一个getter方法（例如 `getSomeField()`），VS Code应该能够提供正确的代码提示。
*   **查看大纲视图**: 在大纲(Outline)视图中，您应该能看到由Lombok生成的方法和字段。

如果问题仍然存在，请检查以下几点：
*   `settings.json` 中的Lombok jar路径是否正确无误。
*   VS Code右下角是否显示了正确的JDK版本（本项目应为Java 1.8）。
*   查看 "PROBLEMS" 面板中是否还有与Java或Maven相关的错误信息。

通过以上步骤，您应该能够成功配置好VS Code的Java开发环境，并解决Lombok注解不识别的问题。