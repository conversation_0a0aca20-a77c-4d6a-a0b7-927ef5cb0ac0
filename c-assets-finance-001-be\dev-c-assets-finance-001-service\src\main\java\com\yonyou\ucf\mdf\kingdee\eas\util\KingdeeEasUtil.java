package com.yonyou.ucf.mdf.kingdee.eas.util;

import javax.xml.namespace.QName;

import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.message.SOAPHeaderElement;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.kingdee.eas.config.KingdeeEasProperties;
import com.yonyou.ucf.mdf.kingdee.eas.model.WSContext;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 金蝶EAS工具类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KingdeeEasUtil {

	private static String loginUrl = "/ormrpc/services/EASLogin?wsdl";

	private final KingdeeEasProperties properties;

	/**
	 * 执行接口操作
	 * 
	 * @param xmlStr 请求参数
	 * @param opName 操作名称
	 * @return 接口返回结果
	 * @throws Exception 异常信息
	 */
	public String doOperation(String xmlStr, String opName, String url) throws Exception {
		log.error("调用金蝶EAS接口开始，url：{}，操作名称：{}，参数：{}", url, opName, xmlStr);

		// 创建服务调用
		Service service = new Service();
		Call call = (Call) service.createCall();

		// 设置登录操作
		call.setOperationName("login");
		call.setTargetEndpointAddress(properties.getServicehost() + loginUrl);
		call.setReturnType(new QName("urn:client", "WSContext"));
		call.setReturnClass(WSContext.class);
		call.setReturnQName(new QName("", "loginReturn"));
		call.setTimeout(properties.getTimeout());
		call.setMaintainSession(true);

		// 执行登录
		WSContext wsContext = (WSContext) call.invoke(new Object[] { properties.getUsername(), properties.getPassword(),
				properties.getSlnName(), properties.getDcName(), properties.getLanguage(), properties.getDbType() });

		if (wsContext.getSessionId() == null) {
			throw new Exception("登录失败");
		}

		// 清理登录操作
		call.clearOperation();

		// 设置业务操作
		call.setOperationName(opName);
		call.setTargetEndpointAddress(properties.getServicehost() + url);
		call.setReturnQName(new QName("", "return"));
		call.setTimeout(properties.getTimeout());
		call.setMaintainSession(true);

		// 设置会话ID
		SOAPHeaderElement header = new SOAPHeaderElement("http://login.webservice.bos.kingdee.com", "SessionId",
				wsContext.getSessionId());
		call.addHeader(header);

		// 执行业务操作
		String result = (String) call.invoke(new Object[] { xmlStr });
		log.info("调用金蝶EAS接口成功，返回结果：{}", result);

		return result;
	}

}