package com.yonyou.ucf.mdf.kingdee.eas.model;

import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 金蝶EAS接口调用请求参数
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class KingdeeEasRequest<T> {

	/**
	 * 请求数据
	 */
	private T data;

	/**
	 * 每页大小
	 */
	private Integer pageSize;

	/**
	 * 起始位置
	 */
	private Integer dataStart;

	/**
	 * 通用查询参数
	 */
	@Data
	public static class QueryParams {
		/**
		 * 修改时间开始
		 */
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
		@JSONField(format = "yyyy-MM-dd HH:mm:ss")
		private LocalDateTime modifytimeStart;

		/**
		 * 修改时间结束
		 */
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
		@JSONField(format = "yyyy-MM-dd HH:mm:ss")
		private LocalDateTime modifytimeEnd;
	}
}