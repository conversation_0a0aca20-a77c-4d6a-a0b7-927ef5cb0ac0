package com.yonyou.ucf.mdf.asset.enums;

/**
 * 资产单据类型枚举
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
public enum AssetBillTypeEnum {

	/**
	 * 使用部门变动（列表）
	 */
	USEDEPT_ALTER_LIST("aum_usedept_alter_list", "使用部门变动（列表）"),

	/**
	 * 使用部门变动（卡片）
	 */
	USEDEPT_ALTER_CARD("aum_usedept_alter_card", "使用部门变动（卡片）"),

	/**
	 * 管理部门变动（列表）
	 */
	MANDEPT_ALTER_LIST("aum_mandept_alter_list", "管理部门变动（列表）"),

	/**
	 * 管理部门变动（卡片）
	 */
	MANDEPT_ALTER_CARD("aum_mandept_alter_card", "管理部门变动（卡片）"),

	/**
	 * 资产变动（列表）
	 */
	ALTER_LIST("aum_alter_list", "资产变动（列表）"),

	/**
	 * 资产变动（卡片）
	 */
	ALTER_CARD("aum_alter_card", "资产变动（卡片）"),

	/**
	 * 资产卡片
	 */
	EQUIP_CARD("aim_equip_card", "资产卡片"),

	/**
	 * 资产列表
	 */
	EQUIP_LIST("aim_equip_list", "资产列表"),

	/**
	 * 资产调拨（列表）
	 */
	DEPLOY_APPLY_LIST("aum_deploy_apply_list", "资产调拨（列表）"),

	/**
	 * 资产调拨（卡片）
	 */
	DEPLOY_APPLY_CARD("aum_deploy_apply_card", "资产调拨（卡片）"),

	/**
	 * 资产所有权调出（列表）
	 */
	DEPLOY_OUT_LIST("aum_deploy_out_list", "资产所有权调出（列表）"),

	/**
	 * 资产所有权调出（卡片）
	 */
	DEPLOY_OUT_CARD("aum_deploy_out_card", "资产所有权调出（卡片）"),

	/**
	 * 资产所有权调入（列表）
	 */
	DEPLOY_IN_LIST("aum_deploy_in_list", "资产所有权调入（列表）"),

	/**
	 * 资产所有权调入（卡片）
	 */
	DEPLOY_IN_CARD("aum_deploy_in_card", "资产所有权调入（卡片）"),

	/**
	 * 资产使用权调出（列表）
	 */
	DEPLOY_USEDOUT_LIST("aum_deploy_usedout_list", "资产使用权调出（列表）"),

	/**
	 * 资产使用权调出（卡片）
	 */
	DEPLOY_USEDOUT_CARD("aum_deploy_usedout_card", "资产使用权调出（卡片）"),

	/**
	 * 资产使用权调入（列表）
	 */
	DEPLOY_USEDIN_LIST("aum_deploy_usedin_list", "资产使用权调入（列表）"),

	/**
	 * 资产使用权调入（卡片）
	 */
	DEPLOY_USEDIN_CARD("aum_deploy_usedin_card", "资产使用权调入（卡片）"),

	/**
	 * 资产处置（列表）
	 */
	Sale_LIST("aum_sale_list", "资产处置（列表）"),

	/**
	 * 资产处置（卡片）
	 */
	Sale_CARD("aum_sale_card", "资产处置（卡片）");

	private final String code;
	private final String name;

	AssetBillTypeEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	/**
	 * 根据编码获取枚举
	 *
	 * @param code 编码
	 * @return 枚举
	 */
	public static AssetBillTypeEnum getByCode(String code) {
		for (AssetBillTypeEnum type : values()) {
			if (type.getCode().equals(code)) {
				return type;
			}
		}
		return null;
	}
}