package com.yonyou.ucf.mdf.customdoc.service;

import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocDefinition;

/**
 * 自定义档案定义服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface ICustomDocDefinitionService {

	/**
	 * 保存自定义档案定义
	 * 
	 * @param customDocDefinition 自定义档案定义对象
	 * @return 保存后的对象
	 */
	ResponseResult<CustomDocDefinition> saveAndUpdate(CustomDocDefinition customDocDefinition);

	/**
	 * 根据编码查询自定义档案定义
	 * 
	 * @param code 档案编码
	 * @return 自定义档案定义对象
	 */
	CustomDocDefinition findByCode(String code);
}