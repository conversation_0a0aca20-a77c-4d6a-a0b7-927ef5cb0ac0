package com.yonyou.ucf.mdf.sync.model;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 自定义档案同步失败记录
 * @Date 2025-05-30 09:52:15
 * @since 2023/11/28
 **/
@YMSEntity(name = "cxkingdee-sync.cxkingdee-sync.CustomDocSyncFailRecord", domain = "c-assets-finance-001")
public class CustomDocSyncFailRecord extends SuperDO {
	public static final String ENTITY_NAME = "cxkingdee-sync.cxkingdee-sync.CustomDocSyncFailRecord";
	public static final String LISTCODE = "listcode";
	public static final String LISTNAME = "listname";
	public static final String CODE = "code";
	public static final String NAME = "name";
	public static final String DOCTYPE = "docType";
	public static final String RETRYCOUNT = "retryCount";
	public static final String ERRMSG = "errMsg";
	public static final String KINGDEEDATA = "kingdeeData";
	public static final String REQUESTDATA = "requestData";
	public static final String RESPONEDATA = "responeData";
	public static final String ERRSTACK = "errStack";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 自定义档案设置编码 */
	private String listcode;
	/* 自定义档案设置名称 */
	private String listname;
	/* 自定义档案编码 */
	private String code;
	/* 自定义档案名称 */
	private String name;
	/* 自定义档案类型 */
	private String docType;
	/* 重试次数 */
	private Integer retryCount;
	/* 错误原因 */
	private String errMsg;
	/* 金蝶数据 */
	private String kingdeeData;
	/* 接口保存数据 */
	private String requestData;
	/* 接口保存返回数据 */
	private String responeData;
	/* 失败堆栈 */
	private String errStack;
	/* 单据日期 */
	private String businessDate;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setListcode(String listcode) {
		this.listcode = listcode;
	}

	public void setListname(String listname) {
		this.listname = listname;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setDocType(String docType) {
		this.docType = docType;
	}

	public void setRetryCount(Integer retryCount) {
		this.retryCount = retryCount;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public void setKingdeeData(String kingdeeData) {
		this.kingdeeData = kingdeeData;
	}

	public void setRequestData(String requestData) {
		this.requestData = requestData;
	}

	public void setResponeData(String responeData) {
		this.responeData = responeData;
	}

	public void setErrStack(String errStack) {
		this.errStack = errStack;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getListcode() {
		return listcode;
	}

	public String getListname() {
		return listname;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public String getDocType() {
		return docType;
	}

	public Integer getRetryCount() {
		return retryCount;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public String getKingdeeData() {
		return kingdeeData;
	}

	public String getRequestData() {
		return requestData;
	}

	public String getResponeData() {
		return responeData;
	}

	public String getErrStack() {
		return errStack;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
