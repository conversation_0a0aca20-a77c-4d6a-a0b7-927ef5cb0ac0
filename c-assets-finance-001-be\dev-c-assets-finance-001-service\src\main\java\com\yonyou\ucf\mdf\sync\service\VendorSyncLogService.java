package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.model.VendorSyncLog;

/**
 * 供应商同步日志服务
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
public interface VendorSyncLogService {

    /**
     * 记录成功日志
     * 
     * @param syncLog 同步日志
     */
    void logSuccess(VendorSyncLog syncLog);

    /**
     * 记录错误日志
     * 
     * @param syncLog 同步日志
     */
    void logError(VendorSyncLog syncLog);
} 