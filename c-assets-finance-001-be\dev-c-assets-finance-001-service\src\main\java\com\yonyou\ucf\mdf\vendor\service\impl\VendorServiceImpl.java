package com.yonyou.ucf.mdf.vendor.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ucf.mdf.vendor.service.VendorService;
import com.yonyou.ucf.mdf.vendor.vo.VendorBatchResult;
import com.yonyou.ucf.mdf.vendor.vo.VendorVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 供应商服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Slf4j
@Service
public class VendorServiceImpl implements VendorService {

	private static final String BATCH_SAVE_API = "/yonbip/digitalModel/vendor/batchSaveV2";

	@Autowired
	private SyncApiRequest syncApiRequest;

	@Override
	public ResponseResult<VendorBatchResult> batchSaveVendor(List<VendorVO> vendorVOList) {
		log.info("批量保存供应商信息开始，入参：{}", vendorVOList);
		try {
			String result = syncApiRequest.doPostData(BATCH_SAVE_API, vendorVOList);
			return JsonUtils.parseObject(result, new TypeReference<ResponseResult<VendorBatchResult>>() {
			});
		} catch (Exception e) {
			log.error("批量保存供应商信息失败,vendorVOList:{}", JSONObject.toJSONString(vendorVOList), e);
			throw new RuntimeException("批量保存供应商信息失败", e);
		}
	}
}