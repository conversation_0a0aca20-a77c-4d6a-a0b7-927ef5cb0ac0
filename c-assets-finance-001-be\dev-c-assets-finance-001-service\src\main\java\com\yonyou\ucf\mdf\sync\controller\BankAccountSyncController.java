package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankAccount;
import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;
import com.yonyou.ucf.mdf.sync.service.BankAccountSyncService;

/**
 * 企业银行账户同步Controller
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@RestController
@RequestMapping("/rest")
public class BankAccountSyncController extends BaseSyncController {

	@Autowired
	private BankAccountSyncService bankAccountSyncService;

	/**
	 * 同步企业银行账户数据
	 * 
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 响应结果
	 */
	@PostMapping("/bankAccount/sync")
	public TaskResult syncBankAccount(@RequestBody(required = false) SyncDate syncDate) {
		LocalDateTime[] timeRange = getSyncTimeRange(syncDate);
		try {
			bankAccountSyncService.syncBankAccount(timeRange[0], timeRange[1]);
			return createTaskResult("企业银行账户同步", null);
		} catch (Exception e) {
			return createTaskResult("企业银行账户同步", e);
		}
	}

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param syncDate 同步日期参数
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/bankAccount/selectretry")
	public TaskResult syncBankAccountRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				List<KingdeeBankAccount> kingdeeBankAccounts = rows.stream().map(row -> {
					String kingdeeData = row.getString("kingdeeData");
					if (StringUtils.isBlank(kingdeeData)) {
						return null;
					}
					return JsonUtils.parseObject(kingdeeData, KingdeeBankAccount.class);
				}).filter(Objects::nonNull).collect(Collectors.toList());

				if (!kingdeeBankAccounts.isEmpty()) {
					bankAccountSyncService.publishEvent(kingdeeBankAccounts);
				}
			}
			return createTaskResult("企业银行账户同步", null);
		} catch (Exception e) {
			return createTaskResult("企业银行账户同步", e);
		}
	}

}