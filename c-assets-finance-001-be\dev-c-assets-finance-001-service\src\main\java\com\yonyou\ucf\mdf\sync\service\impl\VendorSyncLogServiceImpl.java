package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.sync.model.VendorSyncLog;
import com.yonyou.ucf.mdf.sync.service.VendorSyncLogService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 供应商同步日志服务实现
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VendorSyncLogServiceImpl implements VendorSyncLogService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public void logSuccess(VendorSyncLog syncLog) {
		log.info("保存供应商同步成功日志");
		try {
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			saveAsRobot(billDOs);
		} catch (Exception e) {
			log.error("供应商同步成功日志保存失败 - {}", JSONObject.toJSONString(syncLog));
			throw new RuntimeException("保存供应商同步日志报错！", e);
		}
	}

	@Override
	public void logError(VendorSyncLog syncLog) {
		log.info("保存供应商同步失败日志");
		try {
			List<IBillDO> billDOs = Lists.newArrayList(syncLog);
			saveAsRobot(billDOs);
		} catch (Exception e) {
			log.error("供应商同步失败日志保存失败 - {}", JSONObject.toJSONString(syncLog));
			throw new RuntimeException("保存供应商同步日志报错！", e);
		}
	}

	/**
	 * 使用机器人保存，因为定时任务没有登录上下文
	 * 
	 * @param billDOs
	 * @return
	 */
	private List<IBillDO> saveAsRobot(List<IBillDO> billDOs) {
		return RobotExecutors.runAs(tenantId, () -> {
			try {
				return billCommonRepository.commonSaveBill(billDOs, "VendorSyncLog");
			} catch (Exception e) {
				log.error("保存供应商同步日志报错！" + e.getMessage(), e);
			}
			return null;
		});
	}
}