package com.yonyou.ucf.mdf.sync.model;

import java.math.BigDecimal;
import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 凭证同步记录
 * @Date 2025-06-10 09:12:49
 * @since 2023/11/28
 **/
@YMSEntity(name = "cxkingdee-sync.cxkingdee-sync.VoucherSyncRecord", domain = "c-assets-finance-001")
public class VoucherSyncRecord extends SuperDO {
    public static final String ENTITY_NAME = "cxkingdee-sync.cxkingdee-sync.VoucherSyncRecord";
    public static final String KINGDEECODE = "kingdeeCode";
    public static final String KINGDEEID = "kingdeeId";
    public static final String COMPANYNUMBER = "companyNumber";
    public static final String COMPANYNAME = "companyName";
    public static final String PERIOD = "period";
    public static final String VOUCHERCODE = "voucherCode";
    public static final String VOUCHERID = "voucherId";
    public static final String ACCBOOK = "accbook";
    public static final String TOTALDEBITORG = "totalDebitOrg";
    public static final String TOTALCREDITORG = "totalCreditOrg";
    public static final String VOUCHERTYPE = "voucherType";
    public static final String SUCCESS = "success";
    public static final String LASTDATE = "lastDate";
    public static final String ERRMSG = "errMsg";
    public static final String KINGDEEDATA = "kingdeeData";
    public static final String REQUESTDATA = "requestData";
    public static final String RESPONEDATA = "responeData";
    public static final String ERRSTACK = "errStack";
    public static final String BUSINESSDATE = "businessDate";
    public static final String CREATETIME = "createTime";
    public static final String CREATOR = "creator";
    public static final String ID = "id";
    public static final String MODIFIER = "modifier";
    public static final String MODIFYTIME = "modifyTime";
    public static final String PUBTS = "pubts";
    public static final String YTENANTID = "ytenantId";

    /* 金蝶凭证号 */
    private String kingdeeCode;
    /* 金蝶凭证id */
    private String kingdeeId;
    /* 金蝶公司编码 */
    private String companyNumber;
    /* 金蝶公司名称 */
    private String companyName;
    /* 会计期间 */
    private String period;
    /* BIP凭证号 */
    private String voucherCode;
    /* BIP凭证id */
    private String voucherId;
    /* BIP账簿 */
    private String accbook;
    /* 借方原币金额合计 */
    private BigDecimal totalDebitOrg;
    /* 贷方原币金额合计 */
    private BigDecimal totalCreditOrg;
    /* 凭证类型 */
    private String voucherType;
    /* 最近一次同步结果 */
    private String success;
    /* 最近一次同步时间 */
    private Date lastDate;
    /* 同步错误信息 */
    private String errMsg;
    /* 金蝶原始数据 */
    private String kingdeeData;
    /* BIP接口保存数据 */
    private String requestData;
    /* BIP接口返回数据 */
    private String responeData;
    /* 错误堆栈信息 */
    private String errStack;
    /* 单据日期 */
    private String businessDate;
    /* 创建时间 */
    private Date createTime;
    /* 创建人 */
    private String creator;
    /* id */
    private String id;
    /* 修改人 */
    private String modifier;
    /* 修改时间 */
    private Date modifyTime;
    /* pubts */
    private Date pubts;
    /* 租户id */
    private String ytenantId;

    public void setKingdeeCode(String kingdeeCode) {
        this.kingdeeCode = kingdeeCode;
    }

    public void setKingdeeId(String kingdeeId) {
        this.kingdeeId = kingdeeId;
    }

    public void setCompanyNumber(String companyNumber) {
        this.companyNumber = companyNumber;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public void setVoucherCode(String voucherCode) {
        this.voucherCode = voucherCode;
    }

    public void setVoucherId(String voucherId) {
        this.voucherId = voucherId;
    }

    public void setAccbook(String accbook) {
        this.accbook = accbook;
    }

    public void setTotalDebitOrg(BigDecimal totalDebitOrg) {
        this.totalDebitOrg = totalDebitOrg;
    }

    public void setTotalCreditOrg(BigDecimal totalCreditOrg) {
        this.totalCreditOrg = totalCreditOrg;
    }

    public void setVoucherType(String voucherType) {
        this.voucherType = voucherType;
    }

    public void setSuccess(String success) {
        this.success = success;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public void setKingdeeData(String kingdeeData) {
        this.kingdeeData = kingdeeData;
    }

    public void setRequestData(String requestData) {
        this.requestData = requestData;
    }

    public void setResponeData(String responeData) {
        this.responeData = responeData;
    }

    public void setErrStack(String errStack) {
        this.errStack = errStack;
    }

    public void setBusinessDate(String businessDate) {
        this.businessDate = businessDate;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public void setPubts(Date pubts) {
        this.pubts = pubts;
    }

    public void setYtenantId(String ytenantId) {
        this.ytenantId = ytenantId;
    }

    public String getKingdeeCode() {
        return kingdeeCode;
    }

    public String getKingdeeId() {
        return kingdeeId;
    }

    public String getCompanyNumber() {
        return companyNumber;
    }

    public String getCompanyName() {
        return companyName;
    }

    public String getPeriod() {
        return period;
    }

    public String getVoucherCode() {
        return voucherCode;
    }

    public String getVoucherId() {
        return voucherId;
    }

    public String getAccbook() {
        return accbook;
    }

    public BigDecimal getTotalDebitOrg() {
        return totalDebitOrg;
    }

    public BigDecimal getTotalCreditOrg() {
        return totalCreditOrg;
    }

    public String getVoucherType() {
        return voucherType;
    }

    public String getSuccess() {
        return success;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public String getKingdeeData() {
        return kingdeeData;
    }

    public String getRequestData() {
        return requestData;
    }

    public String getResponeData() {
        return responeData;
    }

    public String getErrStack() {
        return errStack;
    }

    public String getBusinessDate() {
        return businessDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getCreator() {
        return creator;
    }

    public String getId() {
        return id;
    }

    public String getModifier() {
        return modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public Date getPubts() {
        return pubts;
    }

    public String getYtenantId() {
        return ytenantId;
    }

    public String getFullName() {
        if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
            return super.getFullName();
        } else {
            return ENTITY_NAME;
        }
    }
}
