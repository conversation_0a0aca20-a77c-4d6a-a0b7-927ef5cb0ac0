package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankBranch;
import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;
import com.yonyou.ucf.mdf.sync.service.BankBranchSyncService;

/**
 * 银行网点同步控制器
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@RestController
@RequestMapping("/rest")
public class BankBranchSyncController extends BaseSyncController {

	@Autowired
	private BankBranchSyncService bankBranchSyncService;

	/**
	 * 同步银行网点数据
	 * 
	 * @param syncDate 同步日期参数
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/bank-branch/sync")
	public TaskResult syncBankBranch(@RequestBody(required = false) SyncDate syncDate) throws Exception {
		LocalDateTime[] timeRange = getSyncTimeRange(syncDate);
		try {
			bankBranchSyncService.syncBankBranch(timeRange[0], timeRange[1]);
			return createTaskResult("银行网点同步", null);
		} catch (Exception e) {
			return createTaskResult("银行网点同步", e);
		}
	}

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param syncDate 同步日期参数
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/bank-branch/selectretry")
	public TaskResult syncBankBranchRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				List<KingdeeBankBranch> kingdeeBankBranchs = rows.stream().map(row -> {
					String kingdeeData = row.getString("kingdeeData");
					if (StringUtils.isBlank(kingdeeData)) {
						return null;
					}
					return JsonUtils.parseObject(kingdeeData, KingdeeBankBranch.class);
				}).filter(Objects::nonNull).collect(Collectors.toList());

				if (!kingdeeBankBranchs.isEmpty()) {
					bankBranchSyncService.publishEvent(kingdeeBankBranchs);
				}
			}
			return createTaskResult("银行网点同步", null);
		} catch (Exception e) {
			return createTaskResult("银行网点同步", e);
		}
	}
}