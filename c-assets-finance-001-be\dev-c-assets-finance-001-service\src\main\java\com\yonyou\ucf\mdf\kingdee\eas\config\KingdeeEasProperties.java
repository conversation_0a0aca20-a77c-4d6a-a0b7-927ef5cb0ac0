package com.yonyou.ucf.mdf.kingdee.eas.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * 金蝶EAS配置属性
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
@Component
@ConfigurationProperties(prefix = "kingdee.eas")
public class KingdeeEasProperties {

	/**
	 * 服务地址
	 */
	private String servicehost;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 密码
	 */
	private String password;

	/**
	 * 目标系统
	 */
	private String slnName;

	/**
	 * 数据中心
	 */
	private String dcName;

	/**
	 * 语言
	 */
	private String language = "l2";

	/**
	 * 数据库类型
	 */
	private Integer dbType = 2;

	/**
	 * 超时时间（毫秒）
	 */
	private Integer timeout = 3600000;

}