package com.yonyou.ucf.mdf.sync.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.sync.model.AssetSimple;
import com.yonyou.ucf.mdf.sync.service.AssetQueryService;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月13日
 */
@Slf4j
@Service
public class AssetQueryServiceImpl implements AssetQueryService {

	@Autowired
	private IBillRepository billRepository;

	@Override
	public AssetSimple queryAssetSimpleById(String id) {

		String sql = "select id,equip_code,equip_name,fa_flag from amc_ambd.pam_equip where id = ?";

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(id);

			return billRepository.queryForDTO(sql, parameter, AssetSimple.class);

		} catch (Exception e) {
			log.error("根据资产id：{} 查询资产报错！", id, e);
		}

		return null;
	}

}
