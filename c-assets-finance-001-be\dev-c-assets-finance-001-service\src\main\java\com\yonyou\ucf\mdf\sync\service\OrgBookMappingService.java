package com.yonyou.ucf.mdf.sync.service;

import java.util.List;

import com.yonyou.ucf.mdf.sync.model.OrgBookMapping;

/**
 * 会计主体和账簿映射服务接口
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface OrgBookMappingService {

	/**
	 * 根据金蝶会计主体编码获取映射信息
	 *
	 * @param kingdeeOrgCode 金蝶会计主体编码
	 * @return 映射信息
	 */
	OrgBookMapping getByKingdeeOrgCode(String kingdeeOrgCode);

	/**
	 * 刷新缓存
	 */
	void refreshCache();

	/**
	 * 获取所有金蝶组织编码
	 * 
	 * @return
	 */
	List<String> getAllKingdeeOrgCode();

	/**
	 * 根据组织编码获取金蝶组织编码映射
	 * 
	 * @param orgCode
	 * @return
	 */
	OrgBookMapping getByOrgCode(String orgCode);

	/**
	 * 根据组织id获取金蝶组织编码映射
	 * 
	 * @param orgId
	 * @return
	 */
	OrgBookMapping getByOrgId(String orgId);

	/**
	 * 根据账簿id获取
	 * 
	 * @param accountBook
	 * @return
	 */
	OrgBookMapping getByAccountBookId(String accountBook);
}