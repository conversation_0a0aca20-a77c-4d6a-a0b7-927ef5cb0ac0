package com.yonyou.ucf.mdf.customer.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customer.service.CustomerService;
import com.yonyou.ucf.mdf.customer.vo.CustomerVO;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 客户服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Service
public class CustomerServiceImpl implements CustomerService {

	private static final String BATCH_SAVE_API = "/yonbip/digitalModel/merchant/integration/newbatchsave";

	@Autowired
	private SyncApiRequest syncApiRequest;
	@Autowired
	private IBillRepository billRepository;
	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public ResponseResult<List<BatchResult>> batchSaveCustomer(List<CustomerVO> customerVOList) {
		log.info("批量保存客户信息开始，入参：{}", customerVOList);
		try {
			String result = syncApiRequest.doPostData(BATCH_SAVE_API, customerVOList);
			return JsonUtils.parseObject(result, new TypeReference<ResponseResult<List<BatchResult>>>() {
			});
		} catch (Exception e) {
			log.error("批量保存客户信息失败,customerVOList:{}", JSONObject.toJSONString(customerVOList), e);
			throw new RuntimeException("批量保存客户信息失败", e);
		}
	}

	@Override
	public CustomerVO queryByCode(String code) {
		String sql = "select id,cCode as code from iuap_apdoc_coredoc.merchant where cCode = ? and ytenant_id = ?";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(code);
		parameter.addParam(tenantId);
		return billRepository.queryForDTO(sql, parameter, CustomerVO.class);
	}

}