package com.yonyou.ucf.mdf.sync.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetClearParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetClearParam.FixedAssetClearEntry;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.model.AssetSimple;
import com.yonyou.ucf.mdf.sync.service.AssetQueryService;
import com.yonyou.ucf.mdf.sync.service.FixedAssetClearConverter;
import com.yonyou.ucf.mdf.sync.service.KingdeeSyncConfigService;
import com.yonyou.ucf.mdf.sync.util.CodeTranslator;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;

/**
 * <AUTHOR>
 *
 *         2025年6月13日
 */
@Service
public class FixedAssetClearConverterImpl implements FixedAssetClearConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;
	@Autowired
	private CodeTranslator codeTranslator;
	@Autowired
	private AssetQueryService assetQueryService;
	@Autowired
	private KingdeeSyncConfigService kingdeeSyncConfigService;

	@Override
	public FixedAssetClearParam convert(BizObject param) {
		if (param == null) {
			return null;
		}

		FixedAssetClearParam fixedAssetClearParam = new FixedAssetClearParam();
		defaultValueUtil.setAssetSyncDefaultValue(fixedAssetClearParam, FixedAssetSyncTypeEnum.CLEAR);
		Date audittime = param.getDate("audittime"); // 审批通过时间
		if (audittime != null) {
			fixedAssetClearParam.setClearDate(new SimpleDateFormat("yyyy-MM-dd").format(audittime)); // 清理日期
		}
		String pk_org = param.getString("pk_org"); // 资产组织
		if (StringUtils.isNotBlank(pk_org)) {
			fixedAssetClearParam.setCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
			fixedAssetClearParam.setControlUnitID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
		}
		Date billmaketime = param.getDate("billmaketime");
		if (billmaketime != null) {
			fixedAssetClearParam.setBizDate(new SimpleDateFormat("yyyy-MM-dd").format(billmaketime));
			fixedAssetClearParam.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(billmaketime));
		}
		fixedAssetClearParam.setNumber(param.getString("bill_code")); // 单据编号
		String billmaker = param.getString("billmaker");
		if (StringUtils.isNotBlank(billmaker)) {
			String staffId = codeTranslator.translate(MappingTypeEnum.USER_MAPPING, billmaker);
			if (StringUtils.isNotBlank(staffId)) {
				fixedAssetClearParam.setHandlerID(codeTranslator.translate(MappingTypeEnum.STAFF_MAPPING, staffId));
			}
		}
		fixedAssetClearParam.setDescription(param.getString("memo")); // 备注

		List<BizObject> bodyvos = param.getBizObjects("bodyvos", BizObject.class);
		if (CollectionUtils.isEmpty(bodyvos)) {
			return null;
		}

//		fixedAssetClearParam.setClearModeID(billmaker);

		List<FixedAssetClearEntry> entrys = Lists.newArrayList();
		for (BizObject bizObject : bodyvos) {
			FixedAssetClearEntry entry = new FixedAssetClearEntry();
			String pk_equip = bizObject.getString("pk_equip");
			if (StringUtils.isEmpty(pk_equip)) {
				continue;
			}
			AssetSimple asset = assetQueryService.queryAssetSimpleById(pk_equip);
			if (asset == null || !asset.isFa_flag()) {
				continue;
			}
			entry.setAssetName(asset.getEquip_name());
			entry.setNumber(asset.getEquip_code());
			entrys.add(entry);

			// 设置表头处置方式
			String pk_addreducestyle = bizObject.getString("pk_addreducestyle");
			if (StringUtils.isBlank(fixedAssetClearParam.getClearModeID())
					&& StringUtils.isNotBlank(pk_addreducestyle)) {
				fixedAssetClearParam.setClearModeID(getKingdeeClearMode(pk_addreducestyle));
			}

		}
		if (entrys.isEmpty()) {
			return null;
		}

		fixedAssetClearParam.setEntry(entrys);

		return fixedAssetClearParam;
	}

	/**
	 * 通过BIP处置方式主键，获取金蝶处置方式编码
	 * 
	 * @param pk_addreducestyle
	 * @return
	 */
	private String getKingdeeClearMode(String pk_addreducestyle) {
		if (StringUtils.isBlank(pk_addreducestyle)) {
			return null;
		}
		// 首选获取BIP处置方式编码
		String addreducestyleCode = codeTranslator.translate(MappingTypeEnum.REDUCE_WAY_MAPPING, pk_addreducestyle);
		if (StringUtils.isBlank(addreducestyleCode)) {
			return null;
		}
		// 获取BIP编码和金蝶的映射关系
		Map<String, List<Map<String, Object>>> allConfig = kingdeeSyncConfigService.queryAllCfg(SyncTypeEnum.FIX_ASSET);
		if (allConfig == null || allConfig.isEmpty()) {
			return null;
		}
		List<Map<String, Object>> config = allConfig.get("处置方式编码映射");
		if (config == null || config.isEmpty()) {
			return null;
		}
		for (Map<String, Object> map : config) {
			if (addreducestyleCode.equals(map.getOrDefault("config_key", "").toString())) {
				return map.getOrDefault("config_value", "").toString();
			}
		}
		return null;
	}

}
