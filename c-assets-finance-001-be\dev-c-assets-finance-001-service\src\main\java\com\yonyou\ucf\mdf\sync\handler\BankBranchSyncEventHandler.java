package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.bank.service.BankBranchService;
import com.yonyou.ucf.mdf.bank.vo.BankBranchVO;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankBranch;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.BankBranchSyncEvent;
import com.yonyou.ucf.mdf.sync.model.BankBranchSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.BankBranchSyncLog;
import com.yonyou.ucf.mdf.sync.service.BankBranchConverter;
import com.yonyou.ucf.mdf.sync.service.BankBranchSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.BankBranchSyncLogService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 银行网点同步事件处理器
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BankBranchSyncEventHandler implements EventHandler<BankBranchSyncEvent> {

	private final BankBranchConverter bankBranchConverter;
	private final BankBranchService bankBranchService;
	private final BankBranchSyncLogService bankBranchSyncLogService;
	private final BankBranchSyncFailRecordService bankBranchSyncFailRecordService;
	private final EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(BankBranchSyncEvent event, long sequence, boolean endOfBatch) throws Exception {
		KingdeeBankBranch kingdeeBankBranch = event.getKingdeeBankBranch();
		log.info("开始处理银行网点同步事件，金蝶银行网点：{}，序列号：{}", kingdeeBankBranch, sequence);
		BankBranchVO bankBranchVO = null;
		BankBranchSyncLog syncLog = null;
		long begin = System.currentTimeMillis();
		try {
			// 1. 转换数据
			bankBranchVO = bankBranchConverter.convert(kingdeeBankBranch);
			if (bankBranchVO == null) {
				return;
			}

			// 2. 保存数据
			ResponseResult<BatchResult> responseResult = bankBranchService
					.batchSaveBankBranch(Collections.singletonList(bankBranchVO));

			// 3. 记录同步日志
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog = new BankBranchSyncLog();
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setCode(bankBranchVO.getCode());
			syncLog.setName(bankBranchVO.getName());
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeBankBranch));
			syncLog.setRequestData(JSONObject.toJSONString(bankBranchVO));
			syncLog.setResponeData(JSONObject.toJSONString(responseResult));
			syncLog.setCostTime(costTime);
			if (!"200".equals(responseResult.getCode())) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(responseResult.getMessage());
			} else {
				BatchResult batchResult = responseResult.getData();
				if (batchResult != null) {
					if (batchResult.getFailCount() != null && batchResult.getFailCount() > 0) {
						syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
						if (CollectionUtil.isNotEmpty(batchResult.getMessages())) {
							syncLog.setErrMsg(batchResult.getMessages().get(0).getMessage());
						}
					} else {
						syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
					}
				} else {
					syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
					syncLog.setErrMsg("调用银行网点保存接口，返回数据BatchResult为空，无法判断是否保存成功");
				}
			}

			bankBranchSyncLogService.logSuccess(syncLog);

			log.info("银行网点同步事件处理完成，金蝶银行网点：{}，序列号：{}", event.getKingdeeBankBranch(), sequence);
		} catch (Exception e) {
			log.error("银行网点同步事件处理失败，金蝶银行网点：{}，序列号：{}", event.getKingdeeBankBranch(), sequence, e);
			// 记录失败日志
			if (syncLog == null) {
				syncLog = new BankBranchSyncLog();
			}
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg("银行网点同步事件处理失败:" + e.getMessage());
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			if (bankBranchVO != null) {
				syncLog.setCode(bankBranchVO.getCode());
				syncLog.setName(bankBranchVO.getName());
				syncLog.setRequestData(JSONObject.toJSONString(bankBranchVO));
			}
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeBankBranch));
			syncLog.setCostTime(costTime);
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			bankBranchSyncLogService.logError(syncLog);
		}

		BankBranchSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			bankBranchSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_BANK_BRANCH, kingdeeBankBranch.getNumber(), kingdeeBankBranch);
			ehCacheUtil.removeFail(CacheConstant.CACHE_BANK_BRANCH, kingdeeBankBranch.getNumber());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_BANK_BRANCH, kingdeeBankBranch.getNumber(), kingdeeBankBranch);
		}
	}

	/**
	 * 删除同步失败记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(BankBranchSyncLog syncLog) {
		bankBranchSyncFailRecordService.deleteByCode(syncLog.getCode());
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private BankBranchSyncFailRecord convertFailRecord(BankBranchSyncLog syncLog) {
		if (syncLog == null || SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			return null;
		}
		BankBranchSyncFailRecord failRecord = new BankBranchSyncFailRecord();
		failRecord.setCode(syncLog.getCode());
		failRecord.setName(syncLog.getName());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setRetryCount(0);
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		return failRecord;
	}
}