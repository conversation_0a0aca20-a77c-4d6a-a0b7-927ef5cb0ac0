package com.yonyou.ucf.mdf.api.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2025/3/21 08:57
 * @DESCRIPTION 签名类
 */
public class SignUtil {
    public static final String H_MAC_SHA256 = "HmacSHA256";
    public static String sign(Map<String, Object> parameterMap, String secret) throws Exception {
        StringBuilder builder = new StringBuilder();
        parameterMap.forEach((key, value) -> {
            builder.append(key).append(value);
        });
        Mac mac = Mac.getInstance(H_MAC_SHA256);
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), H_MAC_SHA256));
        byte[] signData = mac.doFinal(builder.toString().getBytes(StandardCharsets.UTF_8));
        String base64Str = new String(Base64.encodeBase64(signData));
        return URLEncoder.encode(base64Str, "UTF-8");
    }

    public static String sign(String appKey, Long timestamp, String secret) throws Exception {
        return sign(appKey, timestamp, (String)null, secret);
    }

    public static String sign(String appKey, Long timestamp, String code, String secret) throws Exception {
        Map<String, Object> parameterMap = new TreeMap<>();
        parameterMap.put("appKey", appKey);
        parameterMap.put("timestamp", String.valueOf(timestamp));
        if (StringUtils.isNotEmpty(code)) {
            parameterMap.put("code", code);
        }

        return sign(parameterMap, secret);
    }
}
