package com.yonyou.ucf.mdf.vendor.vo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 供应商批量保存结果
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VendorBatchResult {
    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failCount;

    /**
     * 失败消息列表
     */
    private List<VendorBatchMessage> messages;
} 