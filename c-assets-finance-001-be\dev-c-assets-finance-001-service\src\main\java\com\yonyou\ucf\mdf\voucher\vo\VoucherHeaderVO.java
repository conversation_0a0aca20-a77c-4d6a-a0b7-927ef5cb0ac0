package com.yonyou.ucf.mdf.voucher.vo;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 凭证头信息
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class VoucherHeaderVO {

	/**
	 * 凭证ID
	 */
	private String id;

	/**
	 * 凭证号
	 */
	private Integer billcode;

	/**
	 * 展示凭证号（同凭证号）
	 */
	private String displaybillcode;

	/**
	 * 凭证摘要
	 */
	private String description;

	/**
	 * 会计期间
	 */
	private String period;

	/**
	 * 展示名称（凭证字-凭证号）
	 */
	private String displayname;

	/**
	 * 来源系统名称
	 */
	private String srcsystem;

	/**
	 * 凭证状态（00暂存，01保存，02错误，03已审核，04已记账，05作废）
	 */
	private String voucherstatus;

	/**
	 * 借方本币合计（账簿）
	 */
	private BigDecimal totaldebit_org;

	/**
	 * 贷方本币合计（账簿）
	 */
	private BigDecimal totalcredit_org;

	/**
	 * 借方本币合计（组织）
	 */
	private BigDecimal organizeDebitTotalAmount;

	/**
	 * 贷方本币合计（组织）
	 */
	private BigDecimal organizeCreditTotalAmount;

	/**
	 * 借方本币合计（集团）
	 */
	private BigDecimal totaldebit_group;

	/**
	 * 贷方本币合计（集团）
	 */
	private BigDecimal totalcredit_group;

	/**
	 * 借方本币合计（全局）
	 */
	private BigDecimal totaldebit_global;

	/**
	 * 贷方本币合计（全局）
	 */
	private BigDecimal totalcredit_global;

	/**
	 * 制单日期
	 */
	private String maketime;

	/**
	 * 最后操作时间
	 */
	private String ts;

	/**
	 * 制单人
	 */
	private Object maker;

	/**
	 * 审核人
	 */
	private Object auditor;

	/**
	 * 记账人
	 */
	private Object tallyman;

	/**
	 * 来源系统
	 */
	private Object srcsystemid;

	/**
	 * 账簿
	 */
	private Object accbook;

	/**
	 * 凭证类型
	 */
	private Object vouchertype;
}