package com.yonyou.ucf.mdf.sync.controller;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.asset.service.FixedAssetPushService;
import com.yonyou.ucf.mdf.sync.model.TaskResult;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/rest")
public class FixedAssetSyncController extends BaseSyncController {
	
	@Autowired
	private FixedAssetPushService fixedAssetPushService;

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param rows 同步失败记录列表
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/fixedAsset/selectretry")
	public TaskResult syncFixedAssetRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				for (BizObject bizObject : rows) {
					String rawData = bizObject.getString("rawData");
					String assetBillTypeCode = bizObject.getString("assetBillType");
					if (StringUtils.isNoneBlank(rawData,assetBillTypeCode)) {
						AssetBillTypeEnum assetBillType = AssetBillTypeEnum.getByCode(assetBillTypeCode);
						BizObject rawDataBiz = JsonUtils.parseObject(rawData, BizObject.class);
						fixedAssetPushService.pushFixedAsset(assetBillType, rawDataBiz);
					}
				}
			}
			return createTaskResult("固定资产同步", null);
		} catch (Exception e) {
			throw new RuntimeException("同步固定资产报错：" + e.getMessage(), e);
		}
	}
	
}
