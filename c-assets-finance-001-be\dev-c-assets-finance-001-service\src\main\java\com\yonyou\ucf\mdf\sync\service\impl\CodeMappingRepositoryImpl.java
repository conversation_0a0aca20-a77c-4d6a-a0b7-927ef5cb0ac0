package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.service.CodeMappingRepository;
import com.yonyou.ypd.bill.basic.bean.CharacteristicsEntity;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月12日
 */
@Slf4j
@Service
public class CodeMappingRepositoryImpl implements CodeMappingRepository {

	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IBillQueryRepository billQryRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public Map<String, String> query(MappingTypeEnum mappingType) {
		if (mappingType == MappingTypeEnum.ASSET_CATEGORY_MAPPING) {
			return queryAssetCategory(mappingType);
		}
		String sql = String.format("select %s,%s from %s where %s", mappingType.getKeyField(),
				mappingType.getValueField(), mappingType.getTableName(), mappingType.getCondition());

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(tenantId);

			List<Map<String, Object>> mappings = billRepository.queryForList(sql, parameter, new MapListProcessor());
			if (mappings != null && !mappings.isEmpty()) {
				return mappings.stream().reduce(new HashMap<String, String>(), (h, v) -> {
					Object key = v.get(mappingType.getKeyField());
					Object value = v.get(mappingType.getValueField());
					if (key != null) {
						h.put(key.toString(), value != null ? value.toString() : null);
					}
					return h;
				}, (h, v) -> h);
			}
		} catch (Exception e) {
			log.error("查询映射失败：{}", mappingType, e);
		}

		return null;
	}

	/**
	 * 资产类别映射比较特殊，金蝶编码使用的是特征项
	 * 
	 * @param mappingType
	 * @return
	 */
	private Map<String, String> queryAssetCategory(MappingTypeEnum mappingType) {
		return RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("*");
			// dr = 0 and enablestate = 2 and ytenant = ?
			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("dr").eq(0),
					QueryCondition.name("enablestate").eq(2), QueryCondition.name("ytenant").eq(tenantId)));
			List<? extends IBillDO> result = billQryRepository.queryBySchema("ampub.ambase.CategoryVO", schema);
			if (CollectionUtils.isEmpty(result)) {
				return null;
			}
			Map<String, String> mapping = Maps.newConcurrentMap();
			for (IBillDO iBillDO : result) {
				CharacteristicsEntity entity = (CharacteristicsEntity) iBillDO.getAttrValue("userDefines");
				if (entity == null) {
					continue;
				}
				String kingdeeCode = entity.getAttribute("ZCKP_10");
				mapping.put(iBillDO.getPrimaryKey().toString(), kingdeeCode);
			}
			return mapping;
		});
	}

	@Override
	public String queryValueByKey(MappingTypeEnum mappingType, String mappingKey) {
		if (mappingType == MappingTypeEnum.ASSET_CATEGORY_MAPPING) {
			return queryAssetCategoryValueByKey(mappingType, mappingKey);
		}
		String sql = String.format("select %s from %s where %s", mappingType.getValueField(),
				mappingType.getTableName(), mappingType.getKeyField() + " = ? ");

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(mappingKey);

			List<Map<String, Object>> mappings = billRepository.queryForList(sql, parameter, new MapListProcessor());
			if (mappings != null && !mappings.isEmpty()) {
				return mappings.get(0).getOrDefault(mappingType.getValueField(), "").toString();
			}
		} catch (Exception e) {
			log.error("查询映射失败：{}", mappingType, e);
		}

		return null;
	}

	/**
	 * 资产类别特殊处理
	 * 
	 * @param mappingType
	 * @param mappingKey
	 * @return
	 */
	private String queryAssetCategoryValueByKey(MappingTypeEnum mappingType, String mappingKey) {
		return RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("*");
			// dr = 0 and enablestate = 2 and ytenant = ?
			schema.addCondition(
					QueryConditionGroup.and(QueryCondition.name("dr").eq(0), QueryCondition.name("enablestate").eq(2),
							QueryCondition.name("ytenant").eq(tenantId), QueryCondition.name("id").eq(mappingKey)));
			List<? extends IBillDO> result = billQryRepository.queryBySchema("ampub.ambase.CategoryVO", schema);
			if (CollectionUtils.isEmpty(result)) {
				return null;
			}
			CharacteristicsEntity entity = (CharacteristicsEntity) result.get(0).getAttrValue("userDefines");
			if (entity == null) {
				return null;
			}
			return entity.getAttribute("ZCKP_10");
		});

	}

}
