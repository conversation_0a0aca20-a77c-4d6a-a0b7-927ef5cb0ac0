package com.yonyou.ucf.mdf.kingdee.fixedasset.vo;

import lombok.Data;
import java.util.List;

/**
 * 固定资产变更申请实体
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class FixedAssetChangeParam {
    /**
     * 变更日期
     */
    private String changeDate;
    
    /**
     * 变更方式
     */
    private String changeModeID;
    
    /**
     * 使用部门是否发生了变更
     */
    private Integer hasDepartmentChanged;
    
    /**
     * 公司
     */
    private String companyID;
    
    /**
     * 单据编号
     */
    private String number;
    
    /**
     * 业务日期
     */
    private String bizDate;
    
    /**
     * 经手人
     */
    private String handlerID;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 控制单元
     */
    private String controlUnitID;
    
    /**
     * 分录
     */
    private List<FixedAssetChangeEntry> entry;
    
    /**
     * 固定资产变更分录实体
     */
    @Data
    public static class FixedAssetChangeEntry {
        /**
         * 资产名称
         */
        private String assetName;
        
        /**
         * 单据编号（资产编码）
         */
        private String numberAsset;
        
        /**
         * 管理部门
         */
        private String deptID;
        
        /**
         * 使用部门
         */
        private String sybmID;
    }
} 