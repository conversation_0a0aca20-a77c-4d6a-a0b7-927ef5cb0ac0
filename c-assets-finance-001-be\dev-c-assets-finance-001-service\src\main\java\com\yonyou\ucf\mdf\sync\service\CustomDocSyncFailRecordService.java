package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.model.CustomDocSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncLog;

/**
 * 自定义档案同步失败记录服务
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface CustomDocSyncFailRecordService {

	/**
	 * 保存失败记录
	 * 
	 * @param failRecord 失败记录
	 * @param docType
	 */
	void saveFailRecord(CustomDocSyncFailRecord failRecord);

	/**
	 * 删除失败记录
	 * 
	 * @param syncLog
	 */
	void deleteBySyncLog(CustomDocSyncLog syncLog);
}