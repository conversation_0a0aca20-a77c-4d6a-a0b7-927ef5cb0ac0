package com.yonyou.ucf.mdf.sync.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.yonyou.ucf.mdf.customdoc.model.CustomDocDefinition;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;
import com.yonyou.ucf.mdf.sync.service.CustomDocDefinitionConverter;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;

/**
 * 自定义档案定义数据转换器实现
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Component
public class CustomDocDefinitionConverterImpl implements CustomDocDefinitionConverter {

	@Autowired
	private DefaultValueUtil defaultValueUtil;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public CustomDocDefinition convert(KingdeeCustomDocDefinition kingdeeCustomDocDefinition) {
		if (kingdeeCustomDocDefinition == null) {
			return null;
		}

		CustomDocDefinition customDocDefinition = new CustomDocDefinition();
		// 先设置默认值
		defaultValueUtil.setDefaultValue(customDocDefinition, SyncTypeEnum.CUSTOM_DOC_DEFINITION);

		// 设置基本信息
		customDocDefinition.setCode(kingdeeCustomDocDefinition.getNumber());
		customDocDefinition.setName(kingdeeCustomDocDefinition.getName());
		customDocDefinition.set_status(SyncActionEnum.INSERT.getValue());

		return customDocDefinition;
	}
}