package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocVO;
import com.yonyou.ucf.mdf.customdoc.service.CustomDocService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDoc;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.DocTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.CustomDocSyncEvent;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncLog;
import com.yonyou.ucf.mdf.sync.service.CustomDocConverter;
import com.yonyou.ucf.mdf.sync.service.CustomDocSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.CustomDocSyncLogService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案同步事件处理类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class CustomDocSyncEventHandler implements EventHandler<CustomDocSyncEvent> {

	@Autowired
	private CustomDocConverter customDocConverter;
	@Autowired
	private CustomDocService customDocService;
	@Autowired
	private CustomDocSyncLogService customDocSyncLogService;
	@Autowired
	private CustomDocSyncFailRecordService customDocSyncFailRecordService;

	@Autowired
	private EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(CustomDocSyncEvent event, long sequence, boolean endOfBatch) throws Exception {
		KingdeeCustomDoc kingdeeCustomDoc = event.getKingdeeCustomDoc();
		log.info("开始处理自定义档案定义同步事件，金蝶自定义档案定义：{}，序列号：{}", kingdeeCustomDoc, sequence);
		CustomDocVO customDocVO = null;
		CustomDocSyncLog syncLog = null;
		long begin = System.currentTimeMillis();

		try {
			// 1. 转换数据
			customDocVO = customDocConverter.convert(kingdeeCustomDoc);
			if (customDocVO == null) {
				return;
			}

			// 2. 保存数据
			ResponseResult<BatchResult> responeResult = customDocService
					.batchSaveCustomDoc(Collections.singletonList(customDocVO));

			// 3. 记录同步日志
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog = new CustomDocSyncLog();
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setListcode(kingdeeCustomDoc.getGroupNum());
			syncLog.setListname(kingdeeCustomDoc.getGroupName());
			syncLog.setCode(kingdeeCustomDoc.getNumber());
			syncLog.setName(kingdeeCustomDoc.getName());
			syncLog.setDocType(DocTypeEnum.CUSTOM_DOC_MAINTENANCE.getCode());
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeCustomDoc));
			syncLog.setRequestData(JSONObject.toJSONString(customDocVO));
			syncLog.setResponeData(JSONObject.toJSONString(responeResult));
			syncLog.setCostTime(costTime);
			if (!responeResult.isSuccess()) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(responeResult.getMessage());
			} else {
				BatchResult batchResult = responeResult.getData();
				if (batchResult == null || (batchResult.getFailCount() != null && batchResult.getFailCount() > 0)) {
					syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
					if (batchResult != null && CollectionUtil.isNotEmpty(batchResult.getMessages())) {
						syncLog.setErrMsg(batchResult.getMessages().get(0).getMessage());
					}
				} else {
					syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
				}
			}

			customDocSyncLogService.logSuccess(syncLog);

			log.info("处理自定义档案同步事件成功，自定义档案编码：{}", kingdeeCustomDoc.getNumber());
		} catch (Exception e) {
			log.error("处理自定义档案同步事件失败，自定义档案编码：{}", kingdeeCustomDoc.getNumber(), e);
			// 记录失败日志
			if (syncLog == null) {
				syncLog = new CustomDocSyncLog();
			}
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog.setDocType(DocTypeEnum.CUSTOM_DOC_MAINTENANCE.getCode());
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg("自定义档案定义同步事件处理失败" + e.getMessage());
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setListcode(kingdeeCustomDoc.getGroupNum());
			syncLog.setListname(kingdeeCustomDoc.getGroupName());
			syncLog.setCode(kingdeeCustomDoc.getNumber());
			syncLog.setName(kingdeeCustomDoc.getName());
			if (customDocVO != null) {
				syncLog.setRequestData(JSONObject.toJSONString(customDocVO));
			}
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeCustomDoc));
			syncLog.setCostTime(costTime);
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			customDocSyncLogService.logError(syncLog);
		}

		// 保存失败记录
		CustomDocSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			customDocSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_CUSTOM_DOC, kingdeeCustomDoc.getNumber(), kingdeeCustomDoc);
			ehCacheUtil.removeFail(CacheConstant.CACHE_CUSTOM_DOC, kingdeeCustomDoc.getNumber());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_CUSTOM_DOC, kingdeeCustomDoc.getNumber(), kingdeeCustomDoc);
		}

	}

	/**
	 * 删除同步失败记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(CustomDocSyncLog syncLog) {
		customDocSyncFailRecordService.deleteBySyncLog(syncLog);
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private CustomDocSyncFailRecord convertFailRecord(CustomDocSyncLog syncLog) {
		if (syncLog == null) {
			return null;
		}
		CustomDocSyncFailRecord failRecord = new CustomDocSyncFailRecord();
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		failRecord.setListcode(syncLog.getListcode());
		failRecord.setListname(syncLog.getListname());
		failRecord.setCode(syncLog.getCode());
		failRecord.setName(syncLog.getName());
		failRecord.setDocType(syncLog.getDocType());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setRetryCount(0);
		return failRecord;
	}

}