package com.yonyou.ucf.mdf.sso.service;

import cn.hutool.json.JSONUtil;
import com.yonyou.ucf.mdf.sso.common.HttpClientUtil;
import com.yonyou.ucf.mdf.sso.common.SeeyonParam;
import com.yonyou.ucf.mdf.sso.common.SignHelper;
import com.yonyou.ucf.mdf.sso.common.UrlConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @className: CasService
 * @author: wjc
 * @date: 2025/5/20 10:36
 * @Version: 1.0
 * @description:
 */
@Service
public class CasService {

    public String getAccessToken(String apiUrl, String appKey, String appSecret) {
        String access_token = null;
        Map<String, Object> params = new HashMap<>();
        // 除签名外的其他参数
        params.put("appKey", appKey);
        params.put("timestamp", System.currentTimeMillis());
        // 计算签名
        try {
            String signature = SignHelper.sign(params, appSecret);
            params.put("signature", signature);
            // 请求
            String httpResponse = HttpClientUtil.doGet(apiUrl, null, params);
            if (StringUtils.isNotBlank(httpResponse)) {
                access_token = JSONUtil.parseObj(JSONUtil.parseObj(httpResponse).get("data")).get("access_token").toString();
            }
        } catch (Exception e) {
        }
        return access_token;
    }

    public String getThirdBindUserId(String ticket, SeeyonParam thirdPublicParam){
        Map<String, Object> params = new HashMap<>();
        // 除签名外的其他参数
        params.put("ticket", ticket);
        return HttpClientUtil.doGet(thirdPublicParam.getSeeyonUrl()+ UrlConstant.THIRD_BIND_USER_ID, null, params);
    }

    /**
     * 获取单点登录code
     * @param url
     * @param access_token
     * @param thirdUcId
     * @param userId
     * @return 单点登录code
     */
    public  String getThirdLoginCode(String url ,String access_token, String thirdUcId, String userId) {
        String thirdLoginCode = null;
        Map queryParams = new HashMap();
        queryParams.put("access_token", access_token);
        Map bodyParams = new HashMap();
        bodyParams.put("thirdUcId", thirdUcId);
        bodyParams.put("userId", userId);
        String httpResponse = HttpClientUtil.doPost(url, null, queryParams, bodyParams);
        if (StringUtils.isNotBlank(httpResponse)) {
            thirdLoginCode = JSONUtil.parseObj(JSONUtil.parseObj(httpResponse).get("data")).get("code").toString();
        }
        return thirdLoginCode;
    }
}
