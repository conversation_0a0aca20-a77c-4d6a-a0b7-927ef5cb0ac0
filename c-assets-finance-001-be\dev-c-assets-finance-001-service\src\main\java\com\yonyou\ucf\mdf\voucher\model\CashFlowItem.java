package com.yonyou.ucf.mdf.voucher.model;

import java.math.BigDecimal;

/**
 * 凭证保持接口凭证实体现金流量
 * 
 * <AUTHOR>
 *
 *         2025年5月14日
 */
import lombok.Data;

@Data
public class CashFlowItem {

	/**
	 * 内部单位code 示例：AAA
	 */
	private String innerOrgCode;

	/**
	 * 主表项目code 示例：1111
	 */
	private String mainItemCode;

	/**
	 * 附表项目code 示例：031501
	 */
	private String supItemCode;

	/**
	 * 现金流量原币金额 示例：12.00
	 */
	private BigDecimal amountOriginal;

	/**
	 * 现金流量本币金额 示例：12.00
	 */
	private BigDecimal amountOrg;
}
