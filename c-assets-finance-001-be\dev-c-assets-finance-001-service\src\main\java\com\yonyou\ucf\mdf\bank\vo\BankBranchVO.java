package com.yonyou.ucf.mdf.bank.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 银行网点实体类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BankBranchVO {
	/**
	 * 银行网点id，新增时无需填写，修改必填
	 */
	private String id;

	/**
	 * 编码 示例：********
	 */
	private String code;

	/**
	 * 名称，中文
	 */
	private String name;

	/**
	 * 名称，英文
	 */
	private String name_en_US;

	/**
	 * 名称，繁体
	 */
	private String name_zh_TW;

	/**
	 * 国家主键,与国家编码二选一必填 示例：0040be98-735b-44c0-afe5-54d11a96037b
	 */
	private String country;

	/**
	 * 国家编码，与国家主键二选一必填 示例：CN
	 */
	private String country_code;

	/**
	 * 银行类别主键，与银行类别编码二选一必填 示例：f3892280-28cb-11ec-a871-f80f41f69848
	 */
	private String bank;

	/**
	 * 银行类别编码，与银行类别主键二选一必填 示例：中国信托商业银行
	 */
	private String bank_code;

	/**
	 * 联行号 示例：TW8229203
	 */
	private String linenumber;

	/**
	 * 启用状态 1启用 2停用，不填写默认为启用状态 示例：1 默认值：1
	 */
	private String enable;

	/**
	 * swift编码 示例：3333
	 */
	private String swiftCode;

	/**
	 * IBAN码 示例：********
	 */
	private String IBAN;

	/**
	 * 操作标识，Insert新增 Update更新 示例：Insert
	 */
	private String _status;

	/**
	 * 地址1，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：1
	 */
	private String addressone;

	/**
	 * 地址2，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：2
	 */
	private String addresstwo;

	/**
	 * 地址3，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：3
	 */
	private String addressthree;

	/**
	 * 地址4，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：4
	 */
	private String addressfour;

	/**
	 * 区域名称，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：西青区
	 */
	private String district_name;

	/**
	 * 省名称，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：天津
	 */
	private String province_name;

	/**
	 * 城市名称，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：天津市
	 */
	private String city_name;

	/**
	 * 特征组bd.bank.BankDotVO
	 */
	private Object defineCharacter;

	/**
	 * 邮编，省、市、区、地址1-4、邮编需要同时存在，才可保存地址信息 示例：111
	 */
	private String postcode;

	/**
	 * 来源标识
	 */
	private String sourceUnique;
}