package com.yonyou.ucf.mdf.sync.service;

import java.time.LocalDateTime;
import java.util.List;

import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDoc;

/**
 * 自定义档案同步服务
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface CustomDocSyncService {
    
    /**
     * 同步自定义档案数据
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void syncCustomDoc(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 发布自定义档案同步事件
     * 
     * @param kingdeeCustomDocs 金蝶自定义档案数据列表
     */
    void publishEvent(List<KingdeeCustomDoc> kingdeeCustomDocs);
} 