package com.yonyou.ucf.mdf.bank.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 企业银行账户实体
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class BankAccountVO {

	/**
	 * 开户类型，0-银行开户，1-结算中心开户，默认不填写为"银行开户"
	 */
	private String acctopentype = "0";

	/**
	 * 结算中心主键或编码，开户类型为结算中心开户时必填，为银行开户时不可填
	 */
	private String settleorgid;

	/**
	 * 企业银行账户id，更新时必填
	 */
	private String id;

	/**
	 * 组织id或编码
	 */
	private String orgid;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 银行账号
	 */
	private String account;

	/**
	 * 名称，中文
	 */
	private String name;

	/**
	 * 名称，英文
	 */
	private String name_en_US;

	/**
	 * 名称，繁体
	 */
	private String name_zh_TW;

	/**
	 * 开户行ID或编码，开户类型为结算中心开户时不可填，为银行开户时必填
	 */
	private String bankNumber;

	/**
	 * 电票代理行Id或编码
	 */
	private String agentBank;

	/**
	 * 开户名，中文
	 */
	private String acctName;

	/**
	 * 开户名，英文
	 */
	private String acctName_en_US;

	/**
	 * 开户名，繁体
	 */
	private String acctName_zh_TW;

	/**
	 * 上级企业银行主键或编码
	 */
	private String parent;

	/**
	 * 账户用途主键或编码
	 */
	private String accountPurpose;

	/**
	 * 备注，中文
	 */
	private String description;

	/**
	 * 备注，英文
	 */
	private String description_en_US;

	/**
	 * 备注，繁体
	 */
	private String description_zh_TW;

	/**
	 * 开户日期
	 */
	private Date accountOpenDate;

	/**
	 * 纳税登记,true-是，false-否，开户类型为结算中心开户时必须为否
	 */
	private Boolean taxRegistration;

	/**
	 * 账户类型，银行开户时可选0-3、5-6，结算中心开户时可选1和4 0:基本、1:一般、2:临时、3:专用,4：票据，5：其他，6：第三方账户
	 */
	private String acctType;

	/**
	 * 自定义项特征
	 */
	private Object defineCharacter;

	/**
	 * 账户性质，0-活期，1-定期，2-通知，3-保证金，开户类型为银行开户时必填；开户类型为结算中心开户时不可填
	 */
	private Integer accountNature;

	/**
	 * 状态，0：未启用，1：启用，2：停用，未传值默认为"未启用"
	 */
	private String enable = "0";

	/**
	 * 币种子表
	 */
	private List<BankAccountCurrencyVO> currencyList;

	/**
	 * 操作标识, Insert:新增、Update:更新
	 */
	private String _status;

	/**
	 * 来源标识
	 */
	private String sourceUnique;
}