package com.yonyou.ucf.mdf.customdoc.model;

import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;

import lombok.Data;

/**
 * 自定义档案实体
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Data
public class CustomDocVO {
	/**
	 * 主键id，有主键时表示修改，无主键时表示新增
	 */
	private String id;

	/**
	 * 档案维护数据编码
	 */
	private String code;

	/**
	 * 档案维护数据名称
	 */
	private MultiLanguageVO name;

	/**
	 * 档案维护数据简称
	 */
	private String shortname;

	/**
	 * 所属自定义档案设置主键，与custdocdefid_code二选一必填
	 */
	private String custdocdefid;

	/**
	 * 所属自定义档案设置编码，与custdocdefid二选一必填
	 */
	private String custdocdefid_code;

	/**
	 * 所属组织主键，与orgCode二选一必填，单组织或未开启分级管控的档案，请填写666666
	 */
	private String orgid;

	/**
	 * 所属组织编码，与orgid二选一必填，单组织或未开启分级管控的档案，请填写global00
	 */
	private String orgCode;

	/**
	 * 档案维护数据描述
	 */
	private MultiLanguageVO description;

	/**
	 * 所属上级主键，列表型为null，树形新增时如果为根节点为null，子节点时与parentCode二选一为必输项
	 */
	private String parent;

	/**
	 * 所属上级编码，列表型为null，树形新增时如果为根节点为null，子节点时与parent二选一为必输项
	 */
	private String parentCode;

	/**
	 * 启用状态，0初始化 1启用 2停用
	 */
	private Integer enable;

	/**
	 * 来源数据唯一标识
	 */
	private String sourceUnique;
}