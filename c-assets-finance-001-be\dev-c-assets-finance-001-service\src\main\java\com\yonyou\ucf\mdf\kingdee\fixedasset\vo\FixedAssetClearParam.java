package com.yonyou.ucf.mdf.kingdee.fixedasset.vo;

import lombok.Data;
import java.util.List;

/**
 * 固定资产清理申请实体
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class FixedAssetClearParam {
    /**
     * 变更日期
     */
    private String clearDate;
    
    /**
     * 变更方式
     */
    private String clearModeID;
    
    /**
     * 公司
     */
    private String companyID;
    
    /**
     * 单据编号
     */
    private String number;
    
    /**
     * 业务日期
     */
    private String bizDate;
    
    /**
     * 经手人
     */
    private String handlerID;
    
    /**
     * 备注
     */
    private String description;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 控制单元
     */
    private String controlUnitID;
    
    /**
     * 分录
     */
    private List<FixedAssetClearEntry> entry;
    
    /**
     * 固定资产清理分录实体
     */
    @Data
    public static class FixedAssetClearEntry {
        /**
         * 资产名称
         */
        private String assetName;
        
        /**
         * 单据编号（资产编码）
         */
        private String number;
    }
} 