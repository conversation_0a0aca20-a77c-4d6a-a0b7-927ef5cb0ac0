package com.yonyou.ucf.mdf.sync.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 同步缓存控制器
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@RestController
@RequestMapping("/sync/cache")
public class SyncCacheController {

	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 清除指定缓存
	 * 
	 * @param cacheName 缓存名称
	 * @return 操作结果
	 */
	@PostMapping("/clear")
	public ResponseResult<Object> clearCache(@RequestParam String cacheName) {
		log.info("开始清除缓存，缓存名称：{}", cacheName);
		try {
			ehCacheUtil.clearValue(cacheName);
			log.info("缓存清除成功");
			return ResponseResult.success();
		} catch (Exception e) {
			log.error("缓存清除失败：{}", e.getMessage(), e);
			return ResponseResult.error("failed: " + e.getMessage());
		}
	}

}
