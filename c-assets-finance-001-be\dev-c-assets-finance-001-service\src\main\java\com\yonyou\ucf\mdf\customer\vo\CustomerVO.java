package com.yonyou.ucf.mdf.customer.vo;

import java.time.LocalDate;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 *
 *         2025年5月26日
 */
@Data
public class CustomerVO {
	// 基础信息
	private Long id; // 客户id；更新时需要更改客户编码需要传值。优先级高于客户编码
	private String code; // 客户编码；必填
	private MultiLanguageVO name; // 客户名称；简体必填
	private String createOrgCode; // 管理组织；必填；填写组织编码
	private String createOrgName; // 管理组织名称；
	private String belongOrgCode; // 使用组织；填写组织编码；更新时必填，不传默认为管理组织
	private String transTypeCode; // 客户类型；填写交易类型编码，不传默认为默认交易类型
	private MultiLanguageVO shortname; // 客户简称
	private String countryName; // 国家；填写国家名称
	private String newTimeZone; // 时区(夏令时)；填写时区枚举编码
	private String languageCode; // 语言；填写语言编码
	private String parentManageOrgCode; // 上级发展组织；填写组织编码
	private String parentCustomerCode; // 上级客户；填写客户编码
	private String suppliersCode; // 对应供应商；填写供应商编码
	private Boolean retailInvestors; // 是否散户；true：是，false：否
	private Boolean internalOrg; // 是否内部组织，true：是，false：否
	private String internalOrgIdCode; // 内部组织；填写组织编码，是否内部组织为是时必填

	// 企业/个人信息
	private Short taxPayingCategories; // 纳税类别；0：一般纳税人，1：小规模纳税人，2：海外纳税，99：其他
	private String customerClassCode; // 客户分类；填写客户分类编码
	private String customerIndustryCode; // 客户行业；填写客户行业编码
	private String developmentPartnerCode; // 发展伙伴；填写发展伙伴编码
	private Short enterpriseNature; // 企业类型；0：企业，1：个人，2：其他
	private String enterpriseName; // 企业名称；企业类型为企业时填写
	private String personName; // 姓名；企业类型为个人时填写并必填
	private String orgName; // 组织名称；企业类型为其他时填写

	// 证件信息
	private Short licenseType; // 证照类型；0：统一社会信用代码，1：营业执照，2：其他证照，3：居民身份证，4：护照，5：其他身份证件
	private String creditCode; // 证照号码/个人证照号
	private String leaderName; // 法人代表；企业类型为企业时填写
	private String leaderNameIdNo; // 法人代表身份证号；企业类型为企业时填写
	private String personCharge; // 负责人；企业类型为其他时填写
	private String personChargeIdNo; // 负责人身份证号；企业类型为其他时填写
	private String businessLicenseNo; // 经营许可证号；当企业类型为企业时，填写
	private LocalDate businessterm; // 营业期限至；资质信息页签下；例如：YYYY-MM-DD
	private LocalDate buildTime; // 成立时间；资质信息页签下；例如：YYYY-MM-DD

	// 联系信息
	private String contactName; // 联系人；资质信息页签下
	private String contactTel; // 联系电话；资质信息页签下
	private MultiLanguageVO address; // 详细地址；资质信息页签下
	private String regionCode; // 注册地区；资质信息页签下；填写地址档案编码
	private String email; // 邮箱；资质信息页签下
	private String fax; // 传真；资质信息页签下
	private String enterNatureCode; // 企业性质；资质信息页签下，填写企业性质编码

	// 财务信息
	private String currencyCode; // 注册资金币种；资质信息页签下
	private String money; // 注册资金(万计)；资质信息页签下；支持小数点后2位
	private Long peopleNum; // 员工人数；资质信息页签下
	private Short scopeModel; // 经营模式；0：生产加工，1：批发经销；资质信息页签下
	private String yearMoney; // 年营业额(万计)；资质信息页签下；支持小数点后2位
	private MultiLanguageVO scope; // 经营范围；资质信息页签下

	// 其他信息
	private String website; // 网址；资质信息页签下
	private String wid; // 微信公众号；资质信息页签下
	private String postCode; // 邮编；资质信息页签下

	// 特征组和关联信息
	private Object merchantCharacter; // 客户基本信息特征
	private List<CustomerAddressVO> merchantAddressInfos; // 客户档案地址信息
	private List<CustomerContactVO> merchantContactInfos; // 联系人信息
	private List<CustomerBankVO> merchantAgentFinancialInfos; // 银行信息
	private List<CustomerInvoiceVO> merchantAgentInvoiceInfos; // 发票信息
	private List<CustomerApplyRangeVO> merchantApplyRanges; // 客户适用范围
	private CustomerAppliedDetailVO merchantAppliedDetail; // 客户档案业务信息(客户适用范围详情)
	private CustomerRoleVO merchantRole; // 客户角色信息(营销角色)
	private CustomerManagerVO merchantsManager; // 客户管理员信息
	private List<CustomerAreaVO> customerAreas; // 客户档案销售区域
	private List<CustomerPrincipalVO> principals; // 客户档案负责人
	private List<CustomerInvoicingVO> invoicingCustomers; // 客户档案开票客户

	// 系统控制字段
	private String _status; // 保存状态；Insert：新增客户档案；Update：修改客户档案
	private String sourceUnique; // 来源数据唯一标识
}
