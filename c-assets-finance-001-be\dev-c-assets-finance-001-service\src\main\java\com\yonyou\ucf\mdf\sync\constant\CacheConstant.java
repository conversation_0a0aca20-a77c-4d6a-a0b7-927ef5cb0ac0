package com.yonyou.ucf.mdf.sync.constant;

/**
 * 缓存常量
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public class CacheConstant {

	/**
	 * 数据同步配置缓存
	 */
	public static final String CACHE_SYNC_CONFIG = "sync_config";

	/**
	 * 业务单元缓存
	 */
	public static final String CACHE_ORG = "org";

	/**
	 * 成功处理数据缓存名称前缀
	 */
	public static final String CACHE_SUCCESS_PREFIX = "success_";

	/**
	 * 失败处理数据缓存名称前缀
	 */
	public static final String CACHE_FAIL_PREFIX = "fail_";

	/**
	 * 金蝶客户数据缓存
	 */
	public static final String CACHE_KINGDEE_CUSTOMER = "customer_kingdee";

	/**
	 * BIP客户数据缓存
	 */
	public static final String CACHE_BIP_CUSTOMER = "customer_bip";

	/**
	 * 缓存最大数量
	 */
	public static final int MAX_CACHE_SIZE = 10000;

	/**
	 * 缓存过期时间（秒）
	 */
	public static final int CACHE_EXPIRE_TIME = 24 * 60 * 60;

	/**
	 * 供应商缓存
	 */
	public static final String CACHE_VENDOR = "vendor";

	/**
	 * 银行网点缓存
	 */
	public static final String CACHE_BANK_BRANCH = "bank_branch";

	/**
	 * 银行网点ID缓存
	 */
	public static final String CACHE_BANK_BRANCH_ID = "bank_branch_id";

	/**
	 * 自定义档案定义缓存
	 */
	public static final String CACHE_CUSTOM_DOC_DEFINITION = "custom_doc_definition";

	/**
	 * 自定义档案缓存
	 */
	public static final String CACHE_CUSTOM_DOC = "CACHE_CUSTOM_DOC";

	/**
	 * 企业银行账户缓存
	 */
	public static final String CACHE_BANK_ACCOUNT = "CACHE_BANK_ACCOUNT";

	/**
	 * 凭证缓存
	 */
	public static final String CACHE_VOUCHER = "CACHE_VOUCHER";

	/**
	 * 会计主体和账簿映射缓存
	 */
	public static final String CACHE_ORG_BOOK_MAPPING = "org_book_mapping";
	
	/**
	 * 代码映射缓存
	 */
	public static final String CACHE_CODE_MAPPING = "code_mapping";
}
