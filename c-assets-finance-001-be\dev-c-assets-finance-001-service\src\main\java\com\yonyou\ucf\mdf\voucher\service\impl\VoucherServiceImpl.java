package com.yonyou.ucf.mdf.voucher.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ucf.mdf.voucher.model.Voucher;
import com.yonyou.ucf.mdf.voucher.service.VoucherService;
import com.yonyou.ucf.mdf.voucher.vo.VoucherDeleteParams;
import com.yonyou.ucf.mdf.voucher.vo.VoucherHeaderVO;
import com.yonyou.ucf.mdf.voucher.vo.VoucherQueryParams;
import com.yonyou.ucf.mdf.voucher.vo.VoucherQueryResult;
import com.yonyou.ucf.mdf.voucher.vo.VoucherSaveResult;
import com.yonyou.ucf.mdf.voucher.vo.VoucherVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class VoucherServiceImpl implements VoucherService {

	private static final String QUERY_VOUCHER_API = "/yonbip/fi/ficloud/openapi/voucher/queryVouchers";
	private static final String SAVE_VOUCHER_API = "/yonbip/fi/ficloud/openapi/voucher/addVoucher";
	private static final String DELETE_VOUCHER_API = "/yonbip/fi/voucher/del";

	@Autowired
	private SyncApiRequest syncApiRequest;

	@Override
	public List<VoucherVO> query(VoucherQueryParams queryParams) {
		log.info("开始查询凭证，查询参数：{}", queryParams);
		try {
			String result = syncApiRequest.doPost(QUERY_VOUCHER_API, queryParams);
			ResponseResult<VoucherQueryResult> responeResult = JsonUtils.parseObject(result,
					new TypeReference<ResponseResult<VoucherQueryResult>>() {
					});

			if (!responeResult.isSuccess()) {
				throw new RuntimeException(responeResult.getMessage());
			}

			VoucherQueryResult voucherQueryResult = responeResult.getData();

			log.info("查询凭证完成，共获取{}条数据", voucherQueryResult.getRecordCount());
			return voucherQueryResult.getRecordList();
		} catch (Exception e) {
			log.error("查询凭证异常：" + e.getMessage(), e);
			throw new RuntimeException("查询凭证异常：" + e.getMessage(), e);
		}
	}

	@Override
	public ResponseResult<VoucherSaveResult> save(Voucher voucher) {
		log.info("开始保存凭证，查询参数：{}", voucher);
		try {
			// 先查询一下凭证是否存在，如果存在，则先删除
			VoucherQueryParams qryParams = new VoucherQueryParams();
			qryParams.setAccbookCode(voucher.getAccbookCode());
			qryParams.setBillcodeMin(voucher.getBillCode());
			qryParams.setBillcodeMax(voucher.getBillCode());
			qryParams.setDescription(voucher.getDescription());
			// 经测试，这个接口必须加上制单日期限定才能查出结果
			qryParams.setMakeTimeStart(voucher.getMakeTime());
			qryParams.setMakeTimeEnd(voucher.getMakeTime());
			List<VoucherVO> oldVouchers = query(qryParams);
			if (CollectionUtils.isNotEmpty(oldVouchers)) {
				List<String> ids = oldVouchers.stream().map(VoucherVO::getHeader).map(VoucherHeaderVO::getId)
						.collect(Collectors.toList());
				VoucherDeleteParams deleteParams = new VoucherDeleteParams();
				deleteParams.setIds(ids);
				delete(deleteParams);
			}
			String result = syncApiRequest.doPost(SAVE_VOUCHER_API, voucher);
			ResponseResult<VoucherSaveResult> responeResult = JsonUtils.parseObject(result,
					new TypeReference<ResponseResult<VoucherSaveResult>>() {
					});
			log.info("保存凭证完成，返回数据:{}", result);
			return responeResult;
		} catch (Exception e) {
			log.error("保存凭证异常:" + e.getMessage(), e);
			throw new RuntimeException("保存凭证异常" + e.getMessage(), e);
		}
	}

	@Override
	public void delete(VoucherDeleteParams deleteParams) {
		log.info("开始删除凭证，查询参数：{}", deleteParams);
		try {
			String result = syncApiRequest.doPost(DELETE_VOUCHER_API, deleteParams);
			ResponseResult<Object> responeResult = JsonUtils.parseObject(result,
					new TypeReference<ResponseResult<Object>>() {
					});
			if (!responeResult.isSuccess()) {
				throw new RuntimeException(responeResult.getMessage());
			}
			log.info("删除凭证完成，返回数据:{}", result);
		} catch (Exception e) {
			log.error("删除凭证异常：" + e.getMessage(), e);
			throw new RuntimeException("删除凭证异常：" + e.getMessage(), e);
		}
	}
}