package com.yonyou.ucf.mdf.kingdee.voucher.service;

import java.util.List;

import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucherQueryParams;

/**
 * 金蝶凭证查询服务
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface KingdeeVoucherService {
    
    /**
     * 查询金蝶凭证
     * 
     * @param queryParams 查询参数
     * @return 凭证列表
     */
    List<KingdeeVoucher> queryVouchers(KingdeeVoucherQueryParams queryParams);
} 