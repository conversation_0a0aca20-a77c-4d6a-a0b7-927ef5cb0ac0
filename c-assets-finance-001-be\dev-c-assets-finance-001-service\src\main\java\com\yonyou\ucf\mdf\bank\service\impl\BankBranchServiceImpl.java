package com.yonyou.ucf.mdf.bank.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.bank.service.BankBranchService;
import com.yonyou.ucf.mdf.bank.vo.BankBranchVO;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 银行网点服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Service
public class BankBranchServiceImpl implements BankBranchService {

	private static final String BATCH_SAVE_API = "/yonbip/digitalModel/bankdot/batchSave_integration";

	@Autowired
	private SyncApiRequest syncApiRequest;
	@Autowired
	private IBillRepository billRepository;
	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public ResponseResult<BatchResult> batchSaveBankBranch(List<BankBranchVO> bankBranchVOList) {
		log.info("批量保存银行网点信息开始，入参：{}", bankBranchVOList);
		try {
			String result = syncApiRequest.doPostData(BATCH_SAVE_API, bankBranchVOList);
			return JsonUtils.parseObject(result, new TypeReference<ResponseResult<BatchResult>>() {
			});
		} catch (Exception e) {
			log.error("批量保存银行网点信息失败,bankBranchVOList:{}", JSONObject.toJSONString(bankBranchVOList), e);
			throw new RuntimeException("批量保存银行网点信息失败", e);
		}
	}

	@Override
	public String findIdByCode(String code) {
		String sql = "select id from iuap_apdoc_basedoc.bd_bank_dot where code = ? and ytenant_id = ? and dr = 0";
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(code);
		parameter.addParam(tenantId);
		BankBranchVO result = billRepository.queryForDTO(sql, parameter, BankBranchVO.class);
		if (result != null) {
			return result.getId();
		}
		return null;
	}
}