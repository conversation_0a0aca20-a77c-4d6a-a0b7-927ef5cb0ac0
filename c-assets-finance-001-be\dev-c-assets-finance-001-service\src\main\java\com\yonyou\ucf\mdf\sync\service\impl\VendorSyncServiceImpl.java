package com.yonyou.ucf.mdf.sync.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest.QueryParams;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeResponse;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeVendor;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.publisher.VendorSyncEventPublisher;
import com.yonyou.ucf.mdf.sync.service.VendorSyncService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 供应商数据同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Service
public class VendorSyncServiceImpl implements VendorSyncService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Autowired
	private VendorSyncEventPublisher vendorSyncEventPublisher;

	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 同步起始页
	 */
	private Integer dataStart = 0;

	/**
	 * 同步每页获取数量
	 */
	@Value("${kingdee.sync.pageSize}")
	private Integer pageSize = 100;

	@Override
	public void syncVendor(LocalDateTime startTime, LocalDateTime endTime) {
		log.error("开始同步供应商数据，时间范围：{} - {}", startTime, endTime);
		try {
			// 1. 构建金蝶接口请求参数
			KingdeeEasRequest.QueryParams queryParams = new KingdeeEasRequest.QueryParams();
			queryParams.setModifytimeStart(startTime);
			queryParams.setModifytimeEnd(endTime);

			KingdeeEasRequest<KingdeeEasRequest.QueryParams> request = new KingdeeEasRequest<>();
			request.setData(queryParams);
			request.setDataStart(dataStart);
			request.setPageSize(pageSize);

			// 2. 调用金蝶接口获取数据
			String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpSupplier",
					request);
			if (StringUtils.isBlank(result)) {
				log.error("未获取到供应商数据");
				return;
			}
			KingdeeResponse<KingdeeVendor> response = JSON.parseObject(result,
					new TypeReference<KingdeeResponse<KingdeeVendor>>() {
					});

			if (response == null || response.getRows() == null) {
				log.error("未获取到供应商数据");
				return;
			}

			// 3. 发布同步事件
			publishEvent(response.getRows());

			// 4. 如果还有更多数据，继续获取
			if (response.getRows().size() == pageSize) {
				request.setDataStart(request.getDataStart() + pageSize);
				syncVendor(request);
			}
			log.error("同步供应商数据结束------");
		} catch (Exception e) {
			log.error("同步供应商数据失败", e);
			throw new RuntimeException("同步供应商数据失败", e);
		}
	}

	/**
	 * 递归调用
	 * 
	 * @param request
	 */
	private void syncVendor(KingdeeEasRequest<QueryParams> request) {
		// 2. 调用金蝶接口获取数据
		String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA, "ExpSupplier", request);
		if (StringUtils.isBlank(result)) {
			log.error("未获取到供应商数据");
			return;
		}
		KingdeeResponse<KingdeeVendor> response = JSON.parseObject(result,
				new TypeReference<KingdeeResponse<KingdeeVendor>>() {
				});

		if (response == null || response.getRows() == null) {
			log.error("未获取到供应商数据");
			return;
		}

		// 3. 发布同步事件
		publishEvent(response.getRows());

		// 4. 如果还有更多数据，继续获取
		if (response.getRows().size() == pageSize) {
			request.setDataStart(request.getDataStart() + pageSize);
			syncVendor(request);
		}
	}

	/**
	 * 发布事件
	 * 
	 * @param kingdeeVendors
	 */
	@Override
	public void publishEvent(List<KingdeeVendor> kingdeeVendors) {
		for (KingdeeVendor kingdeeVendor : kingdeeVendors) {
			try {
				KingdeeVendor failVendor = (KingdeeVendor) ehCacheUtil.getFail(CacheConstant.CACHE_VENDOR,
						kingdeeVendor.getNumber());
				if (failVendor != null) {
					vendorSyncEventPublisher.publish(kingdeeVendor);
				} else {
					KingdeeVendor successVendor = (KingdeeVendor) ehCacheUtil.getSuccess(CacheConstant.CACHE_VENDOR,
							kingdeeVendor.getNumber());
					if (successVendor == null) {
						vendorSyncEventPublisher.publish(kingdeeVendor);
					} else if (!successVendor.equals(kingdeeVendor)) {
						vendorSyncEventPublisher.publish(kingdeeVendor);
					}
				}
			} catch (Exception e) {
				log.error("发布供应商同步事件失败，供应商编码：{}", kingdeeVendor.getNumber(), e);
			}
		}
	}
}