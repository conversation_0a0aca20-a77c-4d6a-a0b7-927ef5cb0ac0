package com.yonyou.ucf.mdf.kingdee.voucher.vo;

import lombok.Data;

/**
 * 金蝶凭证查询请求参数
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Data
public class KingdeeVoucherQueryParams {

	/**
	 * 是否导出列别名
	 */
	private Boolean isColumnAlias;

	/**
	 * 凭证编码可以;号隔开多个
	 */
	private String voucherNumber;

	/**
	 * 是否导出现金流
	 */
	private Boolean isExpCashflow;

	/**
	 * 公司编码
	 */
	private String companyNumber;

	/**
	 * 凭证状态 0;1;2;3;5
	 */
	private String bizStatus;

	/**
	 * 开始期间 如：202203
	 */
	private String fromPeriodNumber;

	/**
	 * 结束期间 如：202203
	 */
	private String toPeriodNumber;
}