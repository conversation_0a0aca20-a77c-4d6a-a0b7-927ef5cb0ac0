package com.yonyou.ucf.mdf.voucher.model;

import java.math.BigDecimal;
import java.util.List;

/**
 * 凭证保持接口凭证实体明细
 * <AUTHOR>
 *
 * 2025年5月14日
 */
import lombok.Data;

@Data
public class VoucherDetail {

	/**
	 * 摘要
	 */
	private String description;

	/**
	 * 科目编码
	 */
	private String accsubjectCode;

	/**
	 * 单价
	 */
	private BigDecimal price;

	/**
	 * 数量
	 */
	private BigDecimal quantity;

	/**
	 * 原币币种简称，默认账簿币种（本币币种）
	 */
	private String currencyCode;

	/**
	 * 业务日期（默认同制单时间）格式为yyyy-MM-dd
	 */
	private String busidate;

	/**
	 * 账簿币汇率类型（01基准类型，02自定义类型）
	 */
	private String rateType;

	/**
	 * 组织币汇率类型编码(开启组织币允许录入)
	 */
	private String organizeRateTypeCode;

	/**
	 * 集团币汇率类型编码(开启集团币允许录入)
	 */
	private String groupRatetypeCode;

	/**
	 * 全局币汇率类型编码(开启全局币允许录入)
	 */
	private String globalRatetypeCode;

	/**
	 * 账簿币汇率
	 */
	private BigDecimal rateOrg;

	/**
	 * 组织币汇率(开启组织币允许录入)
	 */
	private BigDecimal organizeRate;

	/**
	 * 集团币汇率(开启集团币允许录入)
	 */
	private BigDecimal rateGroup;

	/**
	 * 全局币汇率(开启全局币允许录入)
	 */
	private BigDecimal rateGlobal;

	/**
	 * 原币借方金额（借贷不能同时填写，原币本币都要填写）
	 */
	private BigDecimal debitOriginal;

	/**
	 * 原币贷方金额（借贷不能同时填写，原币本币都要填写）
	 */
	private BigDecimal creditOriginal;

	/**
	 * 借方账簿本币金额（借贷不能同时填写，原币本币都要填写）
	 */
	private BigDecimal debitOrg;

	/**
	 * 借方组织本币金额(开启组织币允许录入)
	 */
	private BigDecimal organizeDebitAmount;

	/**
	 * 借方集团本币金额(开启集团币允许录入)
	 */
	private BigDecimal debitGroup;

	/**
	 * 借方全局本币金额(开启全局币允许录入)
	 */
	private BigDecimal debitGlobal;

	/**
	 * 贷方账簿本币金额（借贷不能同时填写，原币本币都要填写）
	 */
	private BigDecimal creditOrg;

	/**
	 * 贷方组织本币金额(开启组织币允许录入)
	 */
	private BigDecimal organizeCreditAmount;

	/**
	 * 贷方集团本币金额(开启集团币允许录入)
	 */
	private BigDecimal creditGroup;

	/**
	 * 贷方全局本币金额(开启全局币允许录入)
	 */
	private BigDecimal creditGlobal;

	/**
	 * 二级核算会计主体code
	 */
	private String secondOrgCode;

	/**
	 * 结算方式code
	 */
	private String settlementModeCode;

	/**
	 * 票据日期 格式为yyyy-MM-dd
	 */
	private String billTime;

	/**
	 * 票据号
	 */
	private String billNo;

	/**
	 * 银行对账码
	 */
	private String bankVerifyCode;

	/**
	 * 辅助核算
	 */
	private List<AuxiliaryAccountingItem> clientAuxiliaryList;

	/**
	 * 现金流量
	 */
	private List<CashFlowItem> cashflowList;
}