package com.yonyou.ucf.mdf.kingdee.fixedasset.service;

import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetChangeParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.CommonResponse;

/**
 * 固定资产变更服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface FixedAssetChangeService {
    
    /**
     * 变更固定资产
     *
     * @param param 固定资产变更参数
     * @return 响应结果
     */
    CommonResponse changeFixedAsset(FixedAssetChangeParam param);
} 