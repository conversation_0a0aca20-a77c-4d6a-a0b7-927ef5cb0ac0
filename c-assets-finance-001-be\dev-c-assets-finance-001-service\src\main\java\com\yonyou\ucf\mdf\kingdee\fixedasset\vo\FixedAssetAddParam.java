package com.yonyou.ucf.mdf.kingdee.fixedasset.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 固定资产新增参数实体
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class FixedAssetAddParam {
	/**
	 * 期间
	 */
	private String periodId;

	/**
	 * 单据编号
	 */
	private String number;

	/**
	 * 控制单元
	 */
	private String controlUnitID;

	/**
	 * 公司
	 */
	private String companyID;

	/**
	 * 分录
	 */
	private List<FixedAssetEntry> entry;

	/**
	 * 固定资产分录实体
	 */
	@Data
	public static class FixedAssetEntry {
		/**
		 * 资产名称
		 */
		private String zcmc;

		/**
		 * 验收日期
		 */
		private String ysrq;

		/**
		 * 资产数量
		 */
		private BigDecimal zcsl;

		/**
		 * 计量单位
		 */
		private String jldwId;

		/**
		 * 实物入账日期
		 */
		private String swrzrq;

		/**
		 * 资金来源方式
		 */
		private String zjlyfsId;

		/**
		 * 财务入账日期
		 */
		private String cwrzrq;

		/**
		 * 管理部门
		 */
		private String glbmId;

		/**
		 * 规格型号
		 */
		private String ggxh;

		/**
		 * 供应商
		 */
		private String supplierId;

		/**
		 * 使用人
		 */
		private String userId;

		/**
		 * 使用部门
		 */
		private String sybmId;

		/**
		 * 币别
		 */
		private String currencyId;

		/**
		 * 汇率
		 */
		private BigDecimal hl;

		/**
		 * 资产价值（含税）
		 */
		private BigDecimal ybje;

		/**
		 * 资产原值
		 */
		private BigDecimal zcyz;

		/**
		 * 资产类别
		 */
		private String zclbId;

		/**
		 * 来源方式
		 */
		private String lyfsId;

		/**
		 * 存放地点
		 */
		private String cfddId;

		/**
		 * 使用状态
		 */
		private String syztId;

		/**
		 * 经济用途
		 */
		private String jjytId;
	}
}