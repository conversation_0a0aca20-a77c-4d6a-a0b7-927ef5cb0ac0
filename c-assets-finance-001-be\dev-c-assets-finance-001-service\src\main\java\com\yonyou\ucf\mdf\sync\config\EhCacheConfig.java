package com.yonyou.ucf.mdf.sync.config;

import javax.cache.Caching;
import javax.cache.configuration.MutableConfiguration;
import javax.cache.expiry.CreatedExpiryPolicy;
import javax.cache.expiry.Duration;
import javax.cache.spi.CachingProvider;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.yonyou.ucf.mdf.sync.constant.CacheConstant;

import lombok.extern.slf4j.Slf4j;

/**
 * 金蝶数据缓存配置
 */
@Slf4j
@Configuration
@EnableCaching
public class EhCacheConfig {

	/**
	 * 缓存管理器
	 */
	@Bean
	public CacheManager cacheManager() {
		try {
			CachingProvider provider = Caching.getCachingProvider();
			javax.cache.CacheManager cacheManager = provider.getCacheManager();

			// 确保缓存存在
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SYNC_CONFIG);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_BANK_BRANCH_ID);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_ORG);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_BIP_CUSTOMER);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_ORG_BOOK_MAPPING);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_KINGDEE_CUSTOMER);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_KINGDEE_CUSTOMER);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_VENDOR);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_VENDOR);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_BANK_BRANCH);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_BANK_BRANCH);
			ensureCacheExists(cacheManager,
					CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_CUSTOM_DOC_DEFINITION);
			ensureCacheExists(cacheManager,
					CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_CUSTOM_DOC_DEFINITION);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_CUSTOM_DOC);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_CUSTOM_DOC);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_BANK_ACCOUNT);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_BANK_ACCOUNT);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_SUCCESS_PREFIX + CacheConstant.CACHE_VOUCHER);
			ensureCacheExists(cacheManager, CacheConstant.CACHE_FAIL_PREFIX + CacheConstant.CACHE_VOUCHER);

			// 为代码映射创建专用缓存配置
			MutableConfiguration<Object, Object> codeMappingConfig = new MutableConfiguration<>()
					.setTypes(Object.class, Object.class).setStoreByValue(false)
					.setExpiryPolicyFactory(CreatedExpiryPolicy.factoryOf(Duration.ONE_HOUR)).setStatisticsEnabled(true)
					.setManagementEnabled(true);

			cacheManager.createCache(CacheConstant.CACHE_CODE_MAPPING, codeMappingConfig);

			return new JCacheCacheManager(cacheManager);
		} catch (Exception e) {
			log.error("初始化缓存管理器失败", e);
			throw new RuntimeException("初始化缓存管理器失败", e);
		}
	}

	/**
	 * 确保缓存存在
	 * 
	 * @param cacheManager 缓存管理器
	 * @param cacheName    缓存名称
	 */
	private void ensureCacheExists(javax.cache.CacheManager cacheManager, String cacheName) {
		if (cacheManager.getCache(cacheName) == null) {
			log.info("创建缓存：{}", cacheName);
			MutableConfiguration<Object, Object> configuration = new MutableConfiguration<>()
					.setTypes(Object.class, Object.class).setStoreByValue(false)
					.setExpiryPolicyFactory(CreatedExpiryPolicy.factoryOf(Duration.ONE_DAY)).setStatisticsEnabled(true)
					.setManagementEnabled(true);

			cacheManager.createCache(cacheName, configuration);
		}
	}
}
