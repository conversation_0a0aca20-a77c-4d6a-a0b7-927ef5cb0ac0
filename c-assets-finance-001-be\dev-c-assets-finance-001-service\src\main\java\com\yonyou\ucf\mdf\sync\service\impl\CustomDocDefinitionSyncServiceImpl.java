package com.yonyou.ucf.mdf.sync.service.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest;
import com.yonyou.ucf.mdf.kingdee.eas.model.KingdeeEasRequest.QueryParams;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeResponse;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.publisher.CustomDocDefinitionSyncEventPublisher;
import com.yonyou.ucf.mdf.sync.service.CustomDocDefinitionSyncService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案定义同步服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class CustomDocDefinitionSyncServiceImpl implements CustomDocDefinitionSyncService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Autowired
	private CustomDocDefinitionSyncEventPublisher customDocDefinitionSyncEventPublisher;

	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 同步起始页
	 */
	private Integer dataStart = 0;

	/**
	 * 同步每页获取数量
	 */
	@Value("${kingdee.sync.pageSize}")
	private Integer pageSize = 100;

	@Override
	public void syncCustomDocDefinition(LocalDateTime startTime, LocalDateTime endTime) {
		log.error("开始同步自定义档案定义数据，时间范围：{} - {}", startTime, endTime);
		try {
			// 1. 构建金蝶接口请求参数
			KingdeeEasRequest.QueryParams queryParams = new KingdeeEasRequest.QueryParams();
			queryParams.setModifytimeStart(startTime);
			queryParams.setModifytimeEnd(endTime);

			KingdeeEasRequest<KingdeeEasRequest.QueryParams> request = new KingdeeEasRequest<>();
			request.setData(queryParams);
			request.setDataStart(dataStart);
			request.setPageSize(pageSize);

			// 2. 调用金蝶接口获取数据
			String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA,
					"ExpGeneralAsstActTypeGroup", request);
			if (StringUtils.isBlank(result)) {
				log.error("未获取到自定义档案定义数据");
				return;
			}
			KingdeeResponse<KingdeeCustomDocDefinition> response = JSON.parseObject(result,
					new TypeReference<KingdeeResponse<KingdeeCustomDocDefinition>>() {
					});

			if (response == null || response.getRows() == null) {
				log.error("未获取到自定义档案定义数据");
				return;
			}

			// 3. 发布同步事件
			publishEvent(response.getRows());

			// 4. 如果还有更多数据，继续获取
			if (response.getRows().size() == pageSize) {
				request.setDataStart(request.getDataStart() + pageSize);
				syncCustomDocDefinition(request);
			}
			log.error("同步自定义档案定义数据结束------");
		} catch (Exception e) {
			log.error("同步自定义档案定义数据失败", e);
			throw new RuntimeException("同步自定义档案定义数据失败", e);
		}
	}

	/**
	 * 递归调用
	 * 
	 * @param request
	 */
	private void syncCustomDocDefinition(KingdeeEasRequest<QueryParams> request) {
		// 2. 调用金蝶接口获取数据
		String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.BASE_DATA,
				"ExpGeneralAsstActTypeGroup", request);
		if (StringUtils.isBlank(result)) {
			log.error("未获取到自定义档案定义数据");
			return;
		}
		KingdeeResponse<KingdeeCustomDocDefinition> response = JSON.parseObject(result,
				new TypeReference<KingdeeResponse<KingdeeCustomDocDefinition>>() {
				});

		if (response == null || response.getRows() == null) {
			log.error("未获取到自定义档案定义数据");
			return;
		}

		// 3. 发布同步事件
		publishEvent(response.getRows());

		// 4. 如果还有更多数据，继续获取
		if (response.getRows().size() == pageSize) {
			request.setDataStart(request.getDataStart() + pageSize);
			syncCustomDocDefinition(request);
		}
	}

	/**
	 * 发布事件
	 * 
	 * @param kingdeeCustomDocDefinitions
	 */
	@Override
	public void publishEvent(List<KingdeeCustomDocDefinition> kingdeeCustomDocDefinitions) {
		for (KingdeeCustomDocDefinition kingdeeCustomDocDefinition : kingdeeCustomDocDefinitions) {
			try {
				KingdeeCustomDocDefinition failCustomDocDefinition = (KingdeeCustomDocDefinition) ehCacheUtil
						.getFail(CacheConstant.CACHE_CUSTOM_DOC_DEFINITION, kingdeeCustomDocDefinition.getNumber());
				if (failCustomDocDefinition != null) {
					customDocDefinitionSyncEventPublisher.publish(kingdeeCustomDocDefinition);
				} else {
					KingdeeCustomDocDefinition successCustomDocDefinition = (KingdeeCustomDocDefinition) ehCacheUtil
							.getSuccess(CacheConstant.CACHE_CUSTOM_DOC_DEFINITION,
									kingdeeCustomDocDefinition.getNumber());
					if (successCustomDocDefinition == null) {
						customDocDefinitionSyncEventPublisher.publish(kingdeeCustomDocDefinition);
					} else if (!successCustomDocDefinition.equals(kingdeeCustomDocDefinition)) {
						customDocDefinitionSyncEventPublisher.publish(kingdeeCustomDocDefinition);
					}
				}
			} catch (Exception e) {
				log.error("发布自定义档案定义同步事件失败，自定义档案定义编码：{}", kingdeeCustomDocDefinition.getNumber(), e);
			}
		}
	}
}