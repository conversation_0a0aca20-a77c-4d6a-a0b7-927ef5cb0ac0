package com.yonyou.ucf.mdf.kingdee.fixedasset.service;

import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetAddParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.CommonResponse;

/**
 * 固定资产新增服务接口
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
public interface FixedAssetAddService {
    
    /**
     * 新增固定资产
     *
     * @param param 固定资产新增参数
     * @return 响应结果
     */
    CommonResponse addFixedAsset(FixedAssetAddParam param);
} 