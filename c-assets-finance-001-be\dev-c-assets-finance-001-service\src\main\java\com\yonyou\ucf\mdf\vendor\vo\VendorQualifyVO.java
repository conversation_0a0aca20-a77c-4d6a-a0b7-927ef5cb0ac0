package com.yonyou.ucf.mdf.vendor.vo;

import lombok.Data;

/**
 * 供应商资质信息实体类
 * 
 * <AUTHOR>
 * @date 2024年3月26日
 */
@Data
public class VendorQualifyVO {
    /**
     * id,修改的时候传，新增的时候不用传
     */
    private Long id;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新操作或者新增操作
     */
    private String _status;

    /**
     * 资质证照id
     */
    private String qualifydoc;

    /**
     * 证件编码
     */
    private String qualifyCode;

    /**
     * 资质证照编码
     */
    private String qualifydoc_code;

    /**
     * 发证日期（生效期）
     */
    private String qualifyDate;

    /**
     * 到期日
     */
    private String qualifyExpDate;

    /**
     * 是否长期有效
     */
    private Boolean longEffective;

    /**
     * 资质特征自定义项
     */
    private Object vendorQualifyCharacterDefine;

    /**
     * 证件等级id
     */
    private String qualifyGrade;
} 