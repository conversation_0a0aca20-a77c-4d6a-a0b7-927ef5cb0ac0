package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.model.OrgBookMapping;
import com.yonyou.ucf.mdf.sync.service.OrgBookMappingService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 会计主体和账簿映射服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class OrgBookMappingServiceImpl implements OrgBookMappingService {

	@Autowired
	private IBillRepository billRepository;

	@Autowired
	private EhCacheUtil ehCacheUtil;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public OrgBookMapping getByKingdeeOrgCode(String kingdeeOrgCode) {
		// 先从缓存获取
		OrgBookMapping mapping = (OrgBookMapping) ehCacheUtil.getValue(CacheConstant.CACHE_ORG_BOOK_MAPPING,
				kingdeeOrgCode);
		if (mapping != null) {
			return mapping;
		}

		// 缓存未命中，从数据库查询
		String sql = "select c.id bookid, c.code bookcode, c.name bookname, "
				+ "a.id orgid, a.code orgcode, a.name orgname, b.description kingdeeOrgCode "
				+ "from iuap_apdoc_finbd.bd_virtualaccbody a "
				+ "left join iuap_apdoc_basedoc.org_orgs b on a.id = b.id "
				+ "left join fiepub.epub_accountbook c on a.id = c.accentity "
				+ "where b.description = ? and a.ytenant_id = ?";

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(kingdeeOrgCode);
			parameter.addParam(tenantId);

			List<OrgBookMapping> mappings = billRepository.queryForDTOList(sql, parameter, OrgBookMapping.class);
			if (mappings != null && !mappings.isEmpty()) {
				mapping = mappings.get(0);
				// 放入缓存
				ehCacheUtil.putValue(CacheConstant.CACHE_ORG_BOOK_MAPPING, kingdeeOrgCode, mapping);
				return mapping;
			}
		} catch (Exception e) {
			log.error("查询会计主体和账簿映射关系失败，金蝶会计主体编码：{}", kingdeeOrgCode, e);
		}

		return null;
	}

	@Override
	public void refreshCache() {
		ehCacheUtil.clearValue(CacheConstant.CACHE_ORG_BOOK_MAPPING);
		log.info("会计主体和账簿映射缓存已清空");
	}

	@Override
	public List<String> getAllKingdeeOrgCode() {
		// 从数据库查询所有金蝶组织编码
		String sql = "select distinct b.description kingdeeOrgCode " + "from iuap_apdoc_finbd.bd_virtualaccbody a "
				+ "left join iuap_apdoc_basedoc.org_orgs b on a.id = b.id "
				+ "where b.description is not null and a.ytenant_id = ?";

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(tenantId);

			List<OrgBookMapping> mappings = billRepository.queryForDTOList(sql, parameter, OrgBookMapping.class);
			if (mappings != null && !mappings.isEmpty()) {
				// 提取金蝶组织编码并放入缓存
				List<String> kingdeeOrgCodes = mappings.stream().map(OrgBookMapping::getKingdeeOrgCode)
						.filter(code -> code != null).collect(Collectors.toList());

				return kingdeeOrgCodes;
			}
		} catch (Exception e) {
			log.error("查询所有金蝶组织编码失败", e);
		}

		return null;
	}

	@Override
	public OrgBookMapping getByOrgCode(String orgCode) {

		// 先从缓存获取
		OrgBookMapping mapping = (OrgBookMapping) ehCacheUtil.getValue(CacheConstant.CACHE_ORG_BOOK_MAPPING, orgCode);
		if (mapping != null) {
			return mapping;
		}

		// 缓存未命中，从数据库查询
		String sql = "select c.id bookid, c.code bookcode, c.name bookname, "
				+ "a.id orgid, a.code orgcode, a.name orgname, b.description kingdeeOrgCode "
				+ "from iuap_apdoc_finbd.bd_virtualaccbody a "
				+ "left join iuap_apdoc_basedoc.org_orgs b on a.id = b.id "
				+ "left join fiepub.epub_accountbook c on a.id = c.accentity "
				+ "where a.code = ? and a.ytenant_id = ?";

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(orgCode);
			parameter.addParam(tenantId);

			List<OrgBookMapping> mappings = billRepository.queryForDTOList(sql, parameter, OrgBookMapping.class);
			if (mappings != null && !mappings.isEmpty()) {
				mapping = mappings.get(0);
				// 放入缓存
				ehCacheUtil.putValue(CacheConstant.CACHE_ORG_BOOK_MAPPING, orgCode, mapping);
				return mapping;
			}
		} catch (Exception e) {
			log.error("查询会计主体和账簿映射关系失败，会计主体编码：{}", orgCode, e);
		}

		return null;
	}

	@Override
	public OrgBookMapping getByAccountBookId(String accountBook) {

		// 先从缓存获取
		OrgBookMapping mapping = (OrgBookMapping) ehCacheUtil.getValue(CacheConstant.CACHE_ORG_BOOK_MAPPING,
				accountBook);
		if (mapping != null) {
			return mapping;
		}

		// 缓存未命中，从数据库查询
		String sql = "select c.id bookid, c.code bookcode, c.name bookname, "
				+ "a.id orgid, a.code orgcode, a.name orgname, b.description kingdeeOrgCode "
				+ "from iuap_apdoc_finbd.bd_virtualaccbody a "
				+ "left join iuap_apdoc_basedoc.org_orgs b on a.id = b.id "
				+ "left join fiepub.epub_accountbook c on a.id = c.accentity where c.id = ? and a.ytenant_id = ?";

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(accountBook);
			parameter.addParam(tenantId);

			List<OrgBookMapping> mappings = billRepository.queryForDTOList(sql, parameter, OrgBookMapping.class);
			if (mappings != null && !mappings.isEmpty()) {
				mapping = mappings.get(0);
				// 放入缓存
				ehCacheUtil.putValue(CacheConstant.CACHE_ORG_BOOK_MAPPING, accountBook, mapping);
				return mapping;
			}
		} catch (Exception e) {
			log.error("查询会计主体和账簿映射关系失败，账簿id：{}", accountBook, e);
		}

		return null;
	}

	@Override
	public OrgBookMapping getByOrgId(String orgId) {

		// 先从缓存获取
		OrgBookMapping mapping = (OrgBookMapping) ehCacheUtil.getValue(CacheConstant.CACHE_ORG_BOOK_MAPPING, orgId);
		if (mapping != null) {
			return mapping;
		}

		// 缓存未命中，从数据库查询
		String sql = "select c.id bookid, c.code bookcode, c.name bookname, "
				+ "a.id orgid, a.code orgcode, a.name orgname, b.description kingdeeOrgCode "
				+ "from iuap_apdoc_finbd.bd_virtualaccbody a "
				+ "left join iuap_apdoc_basedoc.org_orgs b on a.id = b.id "
				+ "left join fiepub.epub_accountbook c on a.id = c.accentity where a.id = ? and a.ytenant_id = ?";

		try {
			SQLParameter parameter = new SQLParameter();
			parameter.addParam(orgId);
			parameter.addParam(tenantId);

			List<OrgBookMapping> mappings = billRepository.queryForDTOList(sql, parameter, OrgBookMapping.class);
			if (mappings != null && !mappings.isEmpty()) {
				mapping = mappings.get(0);
				// 放入缓存
				ehCacheUtil.putValue(CacheConstant.CACHE_ORG_BOOK_MAPPING, orgId, mapping);
				return mapping;
			}
		} catch (Exception e) {
			log.error("查询会计主体和账簿映射关系失败，组织id：{}", orgId, e);
		}

		return null;
	}
}