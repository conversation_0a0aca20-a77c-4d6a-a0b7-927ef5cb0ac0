package com.yonyou.ucf.mdf.sync.enums;

/**
 * 同步状态枚举
 */
public enum SyncStatusEnum {
    
    /**
     * 同步成功
     */
    SUCCESS("Y", "同步成功"),
    
    /**
     * 同步失败
     */
    FAIL("N", "同步失败");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态描述
     */
    private final String desc;
    
    SyncStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 同步状态枚举
     */
    public static SyncStatusEnum getByCode(String code) {
        for (SyncStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return 是否成功
     */
    public static boolean isSuccess(String code) {
        return SUCCESS.getCode().equals(code);
    }
    
    /**
     * 判断是否为失败状态
     *
     * @param code 状态码
     * @return 是否失败
     */
    public static boolean isFail(String code) {
        return FAIL.getCode().equals(code);
    }
} 