package com.yonyou.ucf.mdf.customer.service;

import java.util.List;

import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customer.vo.CustomerVO;

/**
 * 客户服务接口
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
public interface CustomerService {

	/**
	 * 批量保存客户信息
	 * 
	 * @param customerVOList 客户信息列表
	 * @return 批量保存结果
	 */
	ResponseResult<List<BatchResult>> batchSaveCustomer(List<CustomerVO> customerVOList);

	/**
	 * 根据编码查询客户数据
	 * 
	 * @param code
	 * @return
	 */
	CustomerVO queryByCode(String code);

}