package com.yonyou.ucf.mdf.vendor.vo;

import com.yonyou.ucf.mdf.base.vo.MultiLanguageVO;
import lombok.Data;

/**
 * 供应商联系人实体类
 * 
 * <AUTHOR>
 * @date 2024年3月26日
 */
@Data
public class VendorContactsVO {
    /**
     * 供应商联系人id，新增时无需填写，修改时必填
     */
    private String id;

    /**
     * 姓名
     */
    private String contactname;

    /**
     * 职务,支持多语
     */
    private MultiLanguageVO position;

    /**
     * 手机号
     */
    private String contactmobile;

    /**
     * 固定电话
     */
    private String contactphone;

    /**
     * 电子邮箱
     */
    private String contactemail;

    /**
     * QQ
     */
    private String qq;

    /**
     * 微信
     */
    private String WeChat;

    /**
     * 默认联系人, true:是、false:否
     */
    private Boolean defaultcontact;

    /**
     * 备注,支持多语
     */
    private MultiLanguageVO memo;

    /**
     * 操作标识, Insert:新增、Update:更新
     */
    private String _status;

    /**
     * 性别（枚举）1:男2:女
     */
    private String gender;

    /**
     * 区号（枚举）
     * "000" "其他(+000)"
     * "250" "卢旺达(+250)"
     * "593" "厄瓜多尔(+593)"
     * "60" "马来西亚(+60)"
     * "61" "澳大利亚(+61)"
     * "62" "印度尼西亚(+62)"
     * "63" "菲律宾(+63)"
     * "65" "新加坡(+65)"
     * "66" "泰国(+66)"
     * "7" "哈萨克斯坦(+7)"
     * "81" "日本(+81)"
     * "84" "越南(+84)"
     * "852" "中国香港(+852)"
     * "853" "中国澳门(+853)"
     * "855" "柬埔寨(+855)"
     * "86" "中国(+86)"
     * "880" "孟加拉(+880)"
     * "886" "中国台湾(+886)"
     * "90" "土耳其(+90)"
     * "92" "巴基斯坦(+92)"
     * "95" "缅甸(+95)"
     */
    private String mobileCountrycode;

    /**
     * 联系人特征自定义项
     */
    private Object vendorContactsCharacterDefine;
} 