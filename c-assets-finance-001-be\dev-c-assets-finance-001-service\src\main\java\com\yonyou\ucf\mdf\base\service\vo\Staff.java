package com.yonyou.ucf.mdf.base.service.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:08
 * @DESCRIPTION 员工信息实体类
 */
@Data
public class Staff {

    /** 主键 */
    private String id;

    /** 编码（必填） */
    private String code;

    /** 名称（必填） */
    private String name;

    /** 邮箱（与手机号至少填一项） */
    private String email;

    /** 生日，格式：yyyy-MM-dd HH:mm:ss */
    private String birthdate;

    /** 序号 */
    private String ordernumber;

    /** 头像 */
    private String photo;

    /** 手机号（与邮箱至少填一项） */
    private String mobile;

    /** 外部系统主键id */
    private String objid;

    /** 证件类型 */
    private String cert_type;

    /** 证件号 */
    private String cert_no;

    /** 办公电话 */
    private String officetel;

    /** 是否支持业务员 */
    private Boolean biz_man_tag;

    /** 是否支持店员 */
    private Boolean shop_assis_tag;

    /** 性别（0女 1男） */
    private String sex;

    /** 备注 */
    private String remark;

    /** 员工状态（0未启用/1启用/2停用） */
    private Integer enable;

    /** 操作标识（Insert/Update） */
    private String _status;

    /** 主职信息列表（必填） */
    private List<StaffJob> mainJobList;

    /** 兼职信息列表 */
    private List<StaffPtJob> ptJobList;

    /** 银行账户信息列表 */
    private List<StaffBankAcct> bankAcctList;

    /**
     * 主职信息
     */
    @Data
    public static class StaffJob {
        /** 操作标识（Insert/Update） */
        private String _status;

        /** 上级主管 */
        private String director;

        /** 所属部门主键（必填） */
        private String dept_id;

        /** 所属职务主键 */
        private String job_id;

        /** 工作职责 */
        private String responsibilities;

        /** 任职开始时间（必填），格式：yyyy-MM-dd HH:mm:ss */
        private String begindate;

        /** 任职结束时间，格式：yyyy-MM-dd HH:mm:ss */
        private String enddate;

        /** 所属组织主键（必填） */
        private String org_id;

        /** 员工类别主键 */
        private String psncl_id;

        /** 职位主键id */
        private String post_id;

        /** 职级主键id */
        private String jobgrade_id;
    }

    /**
     * 兼职信息
     */
    @Data
    public static class StaffPtJob {
        /** 所属部门主键（必填） */
        private String dept_id;

        /** 上级主管 */
        private String director;

        /** 兼职开始时间（必填），格式：yyyy-MM-dd HH:mm:ss */
        private String begindate;

        /** 兼职结束时间，格式：yyyy-MM-dd HH:mm:ss */
        private String enddate;

        /** 工作职责 */
        private String responsibilities;

        /** 操作标识（Insert/Update） */
        private String _status;

        /** 所属组织主键（必填） */
        private String org_id;

        /** 所属职务主键 */
        private String job_id;

        /** 职位主键id */
        private String post_id;

        /** 职级主键id */
        private String jobgrade_id;
    }

    /**
     * 银行账户信息
     */
    @Data
    public static class StaffBankAcct {
        /** 币种（必填） */
        private String currency;

        /** 银行类别主键（必填） */
        private String bank;

        /** 操作标识（Insert/Update） */
        private String _status;

        /** 是否默认账户（0否/1是） */
        private String isdefault;

        /** 银行账号（必填） */
        private String account;

        /** 账户名 */
        private String accountname;

        /** 描述 */
        private String memo;

        /** 账号类型（必填） */
        private String accttype;

        /** 银行网点主键（必填） */
        private String bankname;
    }
}
