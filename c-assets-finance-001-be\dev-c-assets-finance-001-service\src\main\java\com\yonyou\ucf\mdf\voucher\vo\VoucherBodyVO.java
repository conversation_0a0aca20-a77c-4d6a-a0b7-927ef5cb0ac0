package com.yonyou.ucf.mdf.voucher.vo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 凭证分录信息
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class VoucherBodyVO {

	/**
	 * 分录ID
	 */
	private String id;

	/**
	 * 关联凭证头ID
	 */
	private String voucherid;

	/**
	 * 分录号
	 */
	private Integer recordnumber;

	/**
	 * 凭证摘要
	 */
	private String description;

	/**
	 * 借方原币金额
	 */
	private BigDecimal debit_original;

	/**
	 * 贷方原币金额
	 */
	private BigDecimal credit_original;

	/**
	 * 借方本币（账簿）
	 */
	private BigDecimal debit_org;

	/**
	 * 贷方本币（账簿）
	 */
	private BigDecimal credit_org;

	/**
	 * 借方本币（组织）
	 */
	private BigDecimal organize_debit_amount;

	/**
	 * 贷方本币（组织）
	 */
	private BigDecimal organize_credit_amount;

	/**
	 * 借方本币（集团）
	 */
	private BigDecimal debit_group;

	/**
	 * 贷方本币（集团）
	 */
	private BigDecimal credit_group;

	/**
	 * 借方本币（全局）
	 */
	private BigDecimal debit_global;

	/**
	 * 贷方本币（全局）
	 */
	private BigDecimal credit_global;

	/**
	 * 数量
	 */
	private BigDecimal quantity;

	/**
	 * 借方数量
	 */
	private BigDecimal debit_quantity;

	/**
	 * 贷方数量
	 */
	private BigDecimal credit_quantity;

	/**
	 * 单价
	 */
	private BigDecimal price;

	/**
	 * 汇率
	 */
	private BigDecimal rate_org;

	/**
	 * 汇率（组织）
	 */
	private BigDecimal organize_rate;

	/**
	 * 汇率（集团）
	 */
	private BigDecimal rate_group;

	/**
	 * 汇率（全局）
	 */
	private BigDecimal rate_global;

	/**
	 * 票据日期
	 */
	private String billTime;

	/**
	 * 票据号
	 */
	private String billNo;

	/**
	 * 银行对账码
	 */
	private String bankVerifyCode;

	/**
	 * 银行对账状态（true-已对账，false-未对账）
	 */
	private Boolean checkflag;

	/**
	 * 关联辅助核算记录ID
	 */
	private String auxiliary;

	/**
	 * 分录关联的辅助核算项来源档案取值名称
	 */
	private String auxiliaryShow;

	/**
	 * 分录关联的现金流量名称
	 */
	private String cashFlowProjectDisplayName;

	/**
	 * 科目
	 */
	private Object accsubject;

	/**
	 * 币种
	 */
	private Object currency;

	/**
	 * 汇率类型
	 */
	private Object ratetype;

	/**
	 * 结算方式
	 */
	private Object settlementMode;

	/**
	 * 二级核算会计主体
	 */
	private Object secondorg;

	/**
	 * 辅助核算项
	 */
	private List<Object> clientauxiliary;

	/**
	 * 现金流量
	 */
	private List<Object> cashFlowProjectList;
}