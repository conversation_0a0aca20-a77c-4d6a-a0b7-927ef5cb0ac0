package com.yonyou.ucf.mdf;

import com.yonyou.iuap.ucf.multilang.runtime.UcfStaticMessageSource;
import com.yonyou.iuap.yms.cache.adapter.YMSCacheFactory;
import com.yonyou.ypd.bill.infrastructure.service.api.IEnvService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * Bean注册完成后操作
 *
 *
 */
@Component
public class MdfSmartLifecycle implements SmartLifecycle {

    private boolean isRunning = false;
    @Value("iuap.boot.redis.refs.defaultDef")
    private static String redisClientYMS;
    private static Logger log = LoggerFactory.getLogger(MdfSmartLifecycle.class);

    // MDFBeanConfig中配置ResourceBundleMessageSource的bean
    @Autowired(required = false)
    private UcfStaticMessageSource messageSource;
    @Autowired
    private YMSCacheFactory ymsCacheFactory;

    @Autowired
    private IEnvService iEnvService;

    /**
     * bean初始化完毕后，该方法会被执行
     */
    @Override
    public void start() {
        log.error("bean初始化完成，走初始化完成后的指定业务逻辑...");

        // 处理多语缓存加载，配置多语redis缓存
        mlCacheConfigRedis();

        // 执行完其他业务后 isRunning = true;
        isRunning = true;
    }

    /**
     * 多个实现接口SmartLifecycle的类时，start的执行顺序按getPhase方法返回值从小到大执行，stop方法的执行顺序则相反
     */
    @Override
    public int getPhase() {
        return 99;
    }

    /**
     * 是否自动执行
     */
    @Override
    public boolean isAutoStartup() {
        return true;
    }

    /**
     * 当前状态，当false时才能执行start，当true时，才会执行stop
     */
    @Override
    public boolean isRunning() {
        return isRunning;
    }

    /**
     * spring容器发现当前对象实现了SmartLifecycle，就调用stop(Runnable)，
     */
    @Override
    public void stop(Runnable callback) {
        // 这个callback是要加入的啊
        callback.run();
        isRunning = false;
    }

    /**
     * 接口Lifecycle的子类的方法，如果只是实现了Lifecycle，就调用stop(),这个方法在这里相当于没有用
     */
    @Override
    public void stop() {
        isRunning = false;
    }

    /**
     * 多语redis缓存服务
     */
    private void mlCacheConfigRedis() {
        log.error(">>>#mlCacheConfigRedis#进入多语言运行时服务启动");
        redisClientYMS = iEnvService.getProperty("iuap.boot.redis.refs.defaultDef");
        try {
            String profile = iEnvService.getProperty("spring.profile");
            String domainUrl = iEnvService.getProperty("runtime.server.url");// 用于多语资源变化时通过事件中心调用RedisTemplate redisTemplate = ymsCacheFactory.createStringRedisTemplate(redisClientYMS);

            RedisTemplate redisTemplate = ymsCacheFactory.createStringRedisTemplate(redisClientYMS);
            messageSource.configRedis(profile, redisTemplate, domainUrl);
        } catch (Throwable e) {
            log.error(">>>#mlCacheConfigRedis#多语redis启用异常:{}", e.getMessage(), e);
        }
    }



}