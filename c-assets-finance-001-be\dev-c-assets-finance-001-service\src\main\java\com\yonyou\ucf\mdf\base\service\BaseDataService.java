package com.yonyou.ucf.mdf.base.service;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.common.BatchResult;
import com.yonyou.ucf.mdf.api.common.BatchResultAI;
import com.yonyou.ucf.mdf.api.common.ResponseResult;
import com.yonyou.ucf.mdf.base.service.vo.AttributeEntity;
import com.yonyou.ucf.mdf.base.service.vo.BaseOrg;
import com.yonyou.ucf.mdf.base.service.vo.AdminOrgVO;
import com.yonyou.ucf.mdf.base.service.vo.Staff;
import org.imeta.orm.schema.QuerySchema;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/21 8:46
 * @DESCRIPTION 基础数据接口
 */
public interface BaseDataService {
    /**
     * 组织保存url
     */
    String orgSaveUrl = "/yonbip/digitalModel/orgunit/save";
    /**
     * 组织批量保存url
     */
    String orgBatchSaveUrl = "/yonbip/digitalModel/orgunit/batchSave";
    /**
     * 部门保存url
     */
    String deptSaveUrl = "/yonbip/digitalModel/admindept/save";
    /**
     * 部门批量保存url
     */
    String deptBatchSaveUrl = "/yonbip/digitalModel/admindept/batchSave";
    /**
     * 员工保存
     */
    String staffSave = "/yonbip/digitalModel/staff/save";
    /**
     * 员工批量保存
     */
    String StaffBatchSave = "/yonbip/digitalModel/staff/batchSave";
    /**
     * 员工启用
     */
    String StaffUnstop = "/yonbip/digitalModel/staff/unstop";
    /**
     * 员工停用
     */
    String StaffStop = "/yonbip/digitalModel/staff/stop";

    String baseOrgFullName = "org.func.BaseOrg";
    String deptFullName = "bd.adminOrg.AdminOrgVO";
    String staffFullName = "bd.staff.Staff";
    String orgDomain = "ucf-org-center";
    String staffDomain = "iuap_apdoc_basedoc";

    ResponseResult<BatchResult> orgBatchSave(List<BaseOrg> baseOrgList);

    ResponseResult<BatchResultAI> deptBatchSave(List<AdminOrgVO> adminOrgVOList);

    ResponseResult<BatchResult> staffBatchSave(List<Staff> staffList);
    ResponseResult<JSONObject> staffUnstop(Staff staff);
    ResponseResult<JSONObject> staffStop(Staff staff);


    Map<String, Object> selectBaseOrgByCode(String code);


    List<Map<String, Object>> selectBaseOrgByCodes(Object[] codes);

    List<Map<String, Object>> selectBaseOrgBySchema(QuerySchema querySchema);


    Map<String, Object> selectDeptByCode(String code);


    List<Map<String, Object>> selectDeptByCodes(Object[] codes);


    List<Map<String, Object>> selectDeptBySchema(QuerySchema querySchema);

    Map<String, Object> selectStaffByCode(String code);

    List<Map<String, Object>> selectStaffByCodes(Object[] codes);

    List<Map<String, Object>> selectStaffBySchema(QuerySchema querySchema);

    List<AttributeEntity> getSchemeAttribute(String attrType);

    ResponseResult<BatchResult> staffBatchUpdate(Map<String, Object> staff);

    ResponseResult<BatchResult> orgBatchUpdate(Map<String, Object> org);

    ResponseResult<BatchResultAI> deptBatchUpdate(Map<String, Object> dept);
}