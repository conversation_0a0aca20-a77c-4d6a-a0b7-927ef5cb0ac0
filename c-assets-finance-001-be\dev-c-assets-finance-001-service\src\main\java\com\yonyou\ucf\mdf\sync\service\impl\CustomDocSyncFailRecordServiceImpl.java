package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.imeta.core.base.ConditionOperator;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.imeta.orm.schema.SimpleCondition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.CustomDocSyncLog;
import com.yonyou.ucf.mdf.sync.service.CustomDocSyncFailRecordService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案同步失败记录服务实现
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class CustomDocSyncFailRecordServiceImpl implements CustomDocSyncFailRecordService {

	@Autowired
	private IBillCommonRepository billCommonRepository;
	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IBillQueryRepository billQryRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public void saveFailRecord(CustomDocSyncFailRecord failRecord) {
		log.info("保存自定义档案同步失败记录：{}", failRecord);

		RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("id,retryCount");
			List<QueryCondition> conditions = Lists.newArrayList();
			conditions.add(QueryCondition.name("listcode").eq(failRecord.getListcode()));
			conditions.add(QueryCondition.name("docType").eq(failRecord.getDocType()));
			if (StringUtils.isNotBlank(failRecord.getCode())) {
				conditions.add(QueryCondition.name("code").eq(failRecord.getCode()));
			}
			schema.addCondition(QueryConditionGroup.and(conditions.toArray(new QueryCondition[0])));
			List<CustomDocSyncFailRecord> result = (List<CustomDocSyncFailRecord>) billQryRepository
					.queryBySchema("cxkingdee-sync.cxkingdee-sync.CustomDocSyncFailRecord", schema);
			if (CollectionUtil.isNotEmpty(result)) {
				CustomDocSyncFailRecord oldRecord = result.get(0);
				failRecord.setId(oldRecord.getId());
				if (oldRecord.getRetryCount() != null) {
					failRecord.setRetryCount(oldRecord.getRetryCount() + 1);
				}
				failRecord.set_status(ActionEnum.UPDATE.getValueInt());
			}
			try {
				List<IBillDO> billDOs = Lists.newArrayList(failRecord);
				billCommonRepository.commonSaveBill(billDOs, "CustomDocSyncFailRecord");
			} catch (Exception e) {
				log.error("保存保存自定义档案同步失败记录报错！", e);
			}
		});

	}

	@Override
	public void deleteBySyncLog(CustomDocSyncLog syncLog) {
		log.info("删除自定义档案同步失败记");
		RobotExecutors.runAs(tenantId, () -> {
			try {
				List<SimpleCondition> conditions = Lists.newArrayList();
				conditions.add(new SimpleCondition("listcode", ConditionOperator.eq, syncLog.getListcode()));
				conditions.add(new SimpleCondition("docType", ConditionOperator.eq, syncLog.getDocType()));
				if (StringUtils.isNotBlank(syncLog.getCode())) {
					conditions.add(new SimpleCondition("code", ConditionOperator.eq, syncLog.getCode()));
				}
				billRepository.batchRemove("cxkingdee-sync.cxkingdee-sync.CustomDocSyncFailRecord", conditions);
			} catch (Exception e) {
				log.error("删除自定义档案同步失败记录报错！" + e.getMessage(), e);
			}
		});
	}
}