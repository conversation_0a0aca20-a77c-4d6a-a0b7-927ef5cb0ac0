package com.yonyou.ucf.mdf.sync.publisher;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.sync.event.VoucherSyncEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证同步事件发布者
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Component
public class VoucherSyncEventPublisher {

    @Autowired
    private RingBuffer<VoucherSyncEvent> ringBuffer;

    /**
     * 发布凭证同步事件
     * 
     * @param kingdeeVouchers 金蝶凭证数据列表（同一张凭证的明细数据）
     */
    public void publish(List<KingdeeVoucher> kingdeeVouchers) {
        if (kingdeeVouchers == null || kingdeeVouchers.isEmpty()) {
            return;
        }

        // 获取第一张凭证的基本信息用于日志
        KingdeeVoucher firstVoucher = kingdeeVouchers.get(0);
        log.info("开始发布凭证同步事件，凭证号：{}，会计主体：{}，期间：{}{}，明细数量：{}", 
                firstVoucher.getVoucherNumber(),
                firstVoucher.getCompanyNumber(),
                firstVoucher.getPeriodYear(),
                firstVoucher.getPeriodNumber(),
                kingdeeVouchers.size());

        try {
            ringBuffer.publishEvent((event, sequence) -> {
                event.setKingdeeVouchers(kingdeeVouchers);
                event.setSyncTime(String.valueOf(System.currentTimeMillis()));
            });
            log.info("凭证同步事件发布成功，凭证号：{}", firstVoucher.getVoucherNumber());
        } catch (Exception e) {
            log.error("发布凭证同步事件失败，凭证号：{}", firstVoucher.getVoucherNumber(), e);
        }
    }
} 