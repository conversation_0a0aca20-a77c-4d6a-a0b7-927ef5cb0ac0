package com.yonyou.ucf.mdf.sync.publisher;

import org.springframework.stereotype.Component;

import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomer;
import com.yonyou.ucf.mdf.sync.event.CustomerSyncEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户同步事件发布者
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerSyncEventPublisher {

	private final Disruptor<CustomerSyncEvent> customerSyncDisruptor;
	private final RingBuffer<CustomerSyncEvent> ringBuffer;

	/**
	 * 发布客户同步事件
	 * 
	 * @param kingdeeCustomer 金蝶客户数据
	 */
	public void publish(KingdeeCustomer kingdeeCustomer) {
		try {
			// 获取下一个序列号
			long sequence = ringBuffer.next();
			try {
				// 获取该序列号对应的事件对象
				CustomerSyncEvent event = ringBuffer.get(sequence);
				// 填充事件数据
				event.setKingdeeCustomer(kingdeeCustomer);
				event.setSyncTime(String.valueOf(System.currentTimeMillis()));
			} finally {
				// 发布事件
				ringBuffer.publish(sequence);
			}
		} catch (Exception e) {
			log.error("发布客户同步事件失败，金蝶客户：{}", kingdeeCustomer, e);
			throw e;
		}
	}

	/**
	 * 关闭Disruptor
	 */
	public void shutdown() {
		log.error("客户同步事件发布关闭");
		customerSyncDisruptor.shutdown();
	}
}