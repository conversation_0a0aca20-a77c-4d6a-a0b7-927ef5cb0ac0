package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案销售区域
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerAreaVO {
    /**
     * 客户档案销售区域子表id，更新和删除时必填
     * 示例：123456
     */
    private Long id;

    /**
     * 客户档案销售区域子表销售区域；填写销售区域编码；必填
     * 示例：销售区域
     */
    private String saleAreaIdCode;

    /**
     * 是否默认销售区域；必填；true：是，false：否；默认销售区域只能并且必须设置一个
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案销售区域实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;
} 