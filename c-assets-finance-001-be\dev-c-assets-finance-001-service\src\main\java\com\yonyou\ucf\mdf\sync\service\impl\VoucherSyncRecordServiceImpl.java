package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.sync.model.VoucherSyncRecord;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncRecordService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月10日
 */
@Slf4j
@Service
public class VoucherSyncRecordServiceImpl implements VoucherSyncRecordService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	@Autowired
	private IBillQueryRepository billQryRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SuppressWarnings("unchecked")
	@Override
	public void save(VoucherSyncRecord syncRecord) {
		RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("id,kingdeeCode,kingdeeId");
			schema.addCondition(
					QueryConditionGroup.and(QueryCondition.name("kingdeeId").eq(syncRecord.getKingdeeId())));
			List<VoucherSyncRecord> result = (List<VoucherSyncRecord>) billQryRepository
					.queryBySchema("cxkingdee-sync.cxkingdee-sync.VoucherSyncRecord", schema);
			if (CollectionUtil.isNotEmpty(result)) {
				VoucherSyncRecord oldRecord = result.get(0);
				syncRecord.setId(oldRecord.getId());
				syncRecord.set_status(ActionEnum.UPDATE.getValueInt());
			}
			try {
				List<IBillDO> billDOs = Lists.newArrayList(syncRecord);
				billCommonRepository.commonSaveBill(billDOs, "VoucherSyncRecord");
			} catch (Exception e) {
				log.error("保存凭证同步记录报错！", e);
			}
		});
	}

}
