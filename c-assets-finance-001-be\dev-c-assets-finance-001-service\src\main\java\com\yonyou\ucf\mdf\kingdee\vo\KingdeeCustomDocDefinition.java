package com.yonyou.ucf.mdf.kingdee.vo;

import com.alibaba.fastjson.JSON;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金蝶自定义档案定义VO
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Data
@EqualsAndHashCode(of = {"name", "number"})
public class KingdeeCustomDocDefinition {

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 编码
	 */
	private String number;

	/**
	 * 创建者编码
	 */
	private String creatorNum;

	/**
	 * 创建者名称
	 */
	private String creatorName;

	/**
	 * 创建时间
	 */
	private String createTime;

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}
}