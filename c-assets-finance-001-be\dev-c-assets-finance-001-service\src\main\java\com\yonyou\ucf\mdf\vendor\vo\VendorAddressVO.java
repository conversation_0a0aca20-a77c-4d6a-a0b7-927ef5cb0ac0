package com.yonyou.ucf.mdf.vendor.vo;

import lombok.Data;

/**
 * 供应商地址信息实体类
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Data
public class VendorAddressVO {
    /**
     * 地址ID，更新时必填，新增时不填
     */
    private String id;

    /**
     * 更新操作或者新增
     */
    private String _status;

    /**
     * 地址名称
     */
    private String addressName;

    /**
     * 国家档案ID
     */
    private String country;

    /**
     * 行政区划
     */
    private String region;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 联系人id
     */
    private String contact;

    /**
     * 是否是默认地址
     */
    private Boolean isDefault;

    /**
     * 地址类型（枚举：1.工厂地址,2.发运地地址,3.发运地关境线,4.目的地关境线,5.目的地地址）
     */
    private String addressType;

    /**
     * 备注
     */
    private String memo;

    /**
     * 地址特征自定义项
     */
    private Object vendorAddressCharacterDefine;

    /**
     * 邮政编码
     */
    private String zipcode;
} 