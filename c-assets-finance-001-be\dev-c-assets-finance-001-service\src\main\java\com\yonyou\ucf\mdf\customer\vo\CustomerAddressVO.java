package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案地址信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerAddressVO {
    /**
     * 地址id；新增时不传，更新、删除时必传
     * 示例：123456
     */
    private Long id;

    /**
     * 地址编码；必输字段
     * 示例：地址编码
     */
    private String addressCode;

    /**
     * 运输区域；填写运输区域编码
     * 示例：运输区域
     */
    private String shipregionCode;

    /**
     * 行政区划；填写地址档案编码
     * 示例：110101000000
     */
    private String regionCode;

    /**
     * 详细地址；必填
     * 示例：详细地址
     */
    private MultiLanguageVO address;

    /**
     * 邮政编码；地址信息页签下
     * 示例：邮政编码
     */
    private String zipCode;

    /**
     * 联系人姓名；地址信息页签下
     * 示例：联系人姓名
     */
    private String receiver;

    /**
     * 联系人手机；地址信息页签下
     * 示例：联系人手机
     */
    private String mobile;

    /**
     * 固定电话；地址信息页签下
     * 示例：固定电话
     */
    private String telePhone;

    /**
     * 邮箱；地址信息页签下
     * 示例：邮箱
     */
    private String email;

    /**
     * QQ；地址信息页签下
     * 示例：QQ
     */
    private String qq;

    /**
     * 微信；地址信息页签下
     * 示例：微信
     */
    private String weChat;

    /**
     * 备注；地址信息页签下
     * 示例：备注
     */
    private String remarks;

    /**
     * 启用状态；地址信息页签下；'0'：启用，'1'：停用，该字段是string类型
     * 示例："0"
     */
    private String stopStatus;

    /**
     * 默认地址；必填；地址信息页签下；地址信息的默认值只能设置一个；true：是；false：否
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案地址信息特征
     */
    private Object addressInfoCharacter;

    /**
     * 客户档案地址信息实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;

    /**
     * 来源数据唯一标识
     * 示例：123456
     */
    private String sourceUnique;
} 