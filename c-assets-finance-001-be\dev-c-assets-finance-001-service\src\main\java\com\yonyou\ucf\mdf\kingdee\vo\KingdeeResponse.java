package com.yonyou.ucf.mdf.kingdee.vo;

import java.util.List;

import lombok.Data;

/**
 * 金蝶接口通用响应类
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class KingdeeResponse<T> {
    
    /**
     * 数据总数
     */
    private Integer dataCount;
    
    /**
     * 错误信息
     */
    private String errorMsg;
    
    /**
     * 页码
     */
    private Integer pageNo;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 数据行
     */
    private List<T> rows;
    
    /**
     * 状态码
     */
    private String statusCode;
} 