package com.yonyou.ucf.mdf.asset.rule;

import java.util.List;
import java.util.Map;

import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.util.YpdRuleBillUtil;
import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.asset.service.FixedAssetPushService;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;

import lombok.extern.slf4j.Slf4j;

/**
 * 资产审批通过后规则
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class FixedAssetApproveRule implements IYpdCommonRul {

	@Autowired
	private FixedAssetPushService fixedAssetPushService;

	@Override
	public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
		log.error("开始执行资产审批通过后规则，billnum：{},domain:{},action:{},fullname:{}", rulCtxVO.getBillnum(),
				rulCtxVO.getDomain(), rulCtxVO.getAction(), rulCtxVO.getFullname());

		String action = rulCtxVO.getAction();
		if (!"approve".equals(action)) {
			return null;
		}

		List<BizObject> bills = null;
		try {
			bills = YpdRuleBillUtil.getBills(rulCtxVO, params);
		} catch (Exception e) {
			log.error("资产审批通过后规则单据转换异常：{}", e.getMessage(), e);
			return null;
		}

		// 根据单据类型调用不同的服务
		log.error("执行资产审批通过后规则，bills：{}", JSONObject.toJSONString(bills));

		for (BizObject bizObject : bills) {
			Integer verifystate = bizObject.getInteger("verifystate");
			if (verifystate == null || verifystate != 2) {
				// 如果不是审批通过，则跳过
				continue;
			}
			AssetBillTypeEnum assetBillType = AssetBillTypeEnum.getByCode(rulCtxVO.getBillnum());
			if (assetBillType == null) {
				continue;
			}
			fixedAssetPushService.pushFixedAsset(assetBillType,bizObject);
		}

		log.error("资产审批通过后规则执行完成");

		return null;
	}

}