package com.yonyou.ucf.mdf.sync.handler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.event.FixedAssetSyncEvent;
import com.yonyou.ucf.mdf.sync.handler.impl.FixedAssetAddProcessor;
import com.yonyou.ucf.mdf.sync.handler.impl.FixedAssetChangeProcessor;
import com.yonyou.ucf.mdf.sync.handler.impl.FixedAssetClearProcessor;
import com.yonyou.ucf.mdf.sync.handler.impl.FixedAssetDispatchProcessor;

import lombok.extern.slf4j.Slf4j;

/**
 * 固定资产同步事件处理器
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class FixedAssetSyncEventHandler implements EventHandler<FixedAssetSyncEvent> {
    
    private final Map<FixedAssetSyncTypeEnum, FixedAssetSyncProcessor> processorMap = new ConcurrentHashMap<>();
    
    @Autowired
    public FixedAssetSyncEventHandler(
            FixedAssetAddProcessor addProcessor,
            FixedAssetChangeProcessor changeProcessor,
            FixedAssetClearProcessor clearProcessor,
            FixedAssetDispatchProcessor dispatchProcessor) {
        // 初始化处理器映射
        processorMap.put(FixedAssetSyncTypeEnum.ADD, addProcessor);
        processorMap.put(FixedAssetSyncTypeEnum.CHANGE, changeProcessor);
        processorMap.put(FixedAssetSyncTypeEnum.CLEAR, clearProcessor);
        processorMap.put(FixedAssetSyncTypeEnum.DISPATCH, dispatchProcessor);
    }
    
    @Override
    public void onEvent(FixedAssetSyncEvent event, long sequence, boolean endOfBatch) {
        try {
            log.info("开始处理固定资产同步事件，类型：{}", event.getSyncType());
            
            // 获取对应的处理器
            FixedAssetSyncProcessor processor = processorMap.get(event.getSyncType());
            if (processor == null) {
                log.error("未找到对应的处理器，事件类型：{}", event.getSyncType());
                return;
            }
            
            // 处理事件
            processor.process(event);
            
            log.info("固定资产同步事件处理完成，类型：{}", event.getSyncType());
        } catch (Exception e) {
            log.error("处理固定资产同步事件失败，事件类型：{}", event.getSyncType(), e);
        }
    }
} 