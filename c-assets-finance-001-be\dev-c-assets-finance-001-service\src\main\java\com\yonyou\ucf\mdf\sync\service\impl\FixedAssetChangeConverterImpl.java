package com.yonyou.ucf.mdf.sync.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetChangeParam;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetChangeParam.FixedAssetChangeEntry;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.model.AssetSimple;
import com.yonyou.ucf.mdf.sync.service.AssetQueryService;
import com.yonyou.ucf.mdf.sync.service.FixedAssetChangeConverter;
import com.yonyou.ucf.mdf.sync.util.CodeTranslator;
import com.yonyou.ucf.mdf.sync.util.DefaultValueUtil;

/**
 * <AUTHOR>
 *
 *         2025年6月13日
 */
@Service
public class FixedAssetChangeConverterImpl implements FixedAssetChangeConverter {

	@Autowired
	private CodeTranslator codeTranslator;
	@Autowired
	private AssetQueryService assetQueryService;
	@Autowired
	private DefaultValueUtil defaultValueUtil;

	@Override
	public FixedAssetChangeParam convert(BizObject param, AssetBillTypeEnum assetBillType) {
		if (param == null) {
			return null;
		}

		FixedAssetChangeParam fixedAssetChangeParam = new FixedAssetChangeParam();
		defaultValueUtil.setAssetSyncDefaultValue(fixedAssetChangeParam, FixedAssetSyncTypeEnum.CHANGE);
		fixedAssetChangeParam.setNumber(param.getString("bill_code")); // 单据号
		Date audittime = param.getDate("audittime"); // 审批通过时间
		if (audittime != null) {
			fixedAssetChangeParam.setChangeDate(new SimpleDateFormat("yyyy-MM-dd").format(audittime));
		}
		String pk_org = param.getString("pk_org"); // 资产组织
		if (StringUtils.isNotBlank(pk_org)) {
			fixedAssetChangeParam.setCompanyID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
			fixedAssetChangeParam.setControlUnitID(codeTranslator.translate(MappingTypeEnum.ORG_MAPPING, pk_org));
		}
		Date billmaketime = param.getDate("billmaketime");
		if (billmaketime != null) {
			fixedAssetChangeParam.setBizDate(new SimpleDateFormat("yyyy-MM-dd").format(billmaketime));
			fixedAssetChangeParam.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(billmaketime));
		}
		String billmaker = param.getString("billmaker");
		if (StringUtils.isNotBlank(billmaker)) {
			String staffId = codeTranslator.translate(MappingTypeEnum.USER_MAPPING, billmaker);
			if (StringUtils.isNotBlank(staffId)) {
				fixedAssetChangeParam.setHandlerID(codeTranslator.translate(MappingTypeEnum.STAFF_MAPPING, staffId));
			}
		}

		List<BizObject> bodyvos = param.getBizObjects("bodyvos", BizObject.class);
		if (CollectionUtils.isEmpty(bodyvos)) {
			return null;
		}
		List<FixedAssetChangeEntry> entrys = Lists.newArrayList();
		for (BizObject bizObject : bodyvos) {
			FixedAssetChangeEntry entry = new FixedAssetChangeEntry();
			String pk_equip = bizObject.getString("pk_equip");
			if (StringUtils.isEmpty(pk_equip)) {
				continue;
			}
			// 管理部门变更前
			String pk_mandept_before = bizObject.getString("pk_mandept_before");
			// 管理部门变更后
			String pk_mandept_after = bizObject.getString("pk_mandept_after");
			// 使用部门变更前
			String pk_usedept_before = bizObject.getString("pk_usedept_before");
			// 使用部门变更后
			String pk_usedept_after = bizObject.getString("pk_usedept_after");
			boolean hasChange = false; // 只有管理部门变更和使用部门变更才推送到金蝶
			if (StringUtils.isNoneBlank(pk_mandept_before, pk_mandept_after)
					&& !pk_mandept_before.equals(pk_mandept_after)) {
				entry.setDeptID(codeTranslator.translate(MappingTypeEnum.DEPT_MAPPING, pk_mandept_after));
				hasChange = true;
			}
			if (StringUtils.isNoneBlank(pk_usedept_before, pk_usedept_after)
					&& !pk_usedept_before.equals(pk_usedept_after)) {
				entry.setSybmID(codeTranslator.translate(MappingTypeEnum.DEPT_MAPPING, pk_usedept_after));
				fixedAssetChangeParam.setHasDepartmentChanged(1);
				hasChange = true;
			}
			AssetSimple asset = assetQueryService.queryAssetSimpleById(pk_equip);
			if (asset == null || !asset.isFa_flag() || !hasChange) {
				continue;
			}
			entry.setNumberAsset(asset.getEquip_code());
			entry.setAssetName(asset.getEquip_name());

			entrys.add(entry);
		}
		if (entrys.isEmpty()) {
			return null;
		}

		fixedAssetChangeParam.setEntry(entrys);

		return fixedAssetChangeParam;
	}

}
