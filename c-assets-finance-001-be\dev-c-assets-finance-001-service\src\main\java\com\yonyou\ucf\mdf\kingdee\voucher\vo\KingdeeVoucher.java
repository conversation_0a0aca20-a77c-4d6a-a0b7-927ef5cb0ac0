package com.yonyou.ucf.mdf.kingdee.voucher.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers.BigDecimalDeserializer;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金蝶凭证实体
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@EqualsAndHashCode
@Data
public class KingdeeVoucher {

	/**
	 * 公司编码
	 */
	private String companyNumber;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 凭证号
	 */
	private String voucherNumber;

	/**
	 * 凭证ID
	 */
	private String voucherId;

	/**
	 * 全局标识
	 */
	private String importKey;

	/**
	 * 会计年度
	 */
	private String periodYear;

	/**
	 * 会计期间
	 */
	private String periodNumber;

	/**
	 * 记账日期
	 */
	private Date bookedDate;

	/**
	 * 业务日期
	 */
	private Date bizDate;

	/**
	 * 凭证类型
	 */
	private String voucherType;

	/**
	 * 状态
	 */
	private String bizStatus;

	/**
	 * 参考消息
	 */
	private String description;

	/**
	 * 摘要
	 */
	private String voucherAbstract;

	/**
	 * 附件数量
	 */
	private Integer attaches;

	/**
	 * 复核
	 */
	private String isCheck;

	/**
	 * 制单人
	 */
	private String creator;

	/**
	 * 审核人
	 */
	private String auditor;

	/**
	 * 过账人
	 */
	private String poster;

	/**
	 * 复核人
	 */
	private String cashier;

	/**
	 * 来源系统
	 */
	private String sourceSys;

	/**
	 * 来源类型
	 */
	private String sourceType;

	/**
	 * 源单ID
	 */
	private String sourceBillId;

	/**
	 * 可生成付款单
	 */
	private String canPayBill;

	/**
	 * 分录号
	 */
	private String entrySeq;

	/**
	 * 利润中心编码
	 */
	private String profitCenterNumber;

	/**
	 * 科目名称
	 */
	private String accountName;

	/**
	 * 科目编码
	 */
	private String accountNumber;

	/**
	 * 币种
	 */
	private String currencyNumber;

	/**
	 * 借贷方向
	 */
	private String entryDC;

	/**
	 * 计量单位
	 */
	private String measurement;

	/**
	 * 往来属性
	 */
	private String cussent;

	/**
	 * 内部单位
	 */
	private String customerNumber;

	/**
	 * 汇率
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal localRate;

	/**
	 * 原币金额
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal originalAmount;

	/**
	 * 借方金额
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal debitAmount;

	/**
	 * 贷方金额
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal creditAmount;

	/**
	 * 数量
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal qty;

	/**
	 * 单价
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal price;

	/**
	 * 辅助账号
	 */
	private String asstSeq;

	/**
	 * 辅助账摘要
	 */
	private String assistAbstract;

	/**
	 * 辅助账业务日期
	 */
	private Date assistBizDate;

	/**
	 * 到期日
	 */
	private Date assistEndDate;

	/**
	 * 客户编码
	 */
	private String cpCustomerNumber;

	/**
	 * 供应商编码
	 */
	private String cpSupplierNumber;

	/**
	 * 公司编码
	 */
	private String cpOrgUnitNumber;

	/**
	 * 物料编码
	 */
	private String cpMaterialNumber;

	/**
	 * 结算方式
	 */
	private String settlementType;

	/**
	 * 结算号
	 */
	private String settlementNumber;

	/**
	 * 业务编号
	 */
	private String bizNumber;

	/**
	 * 票证号码
	 */
	private String ticketNumber;

	/**
	 * 发票号码
	 */
	private String invoiceNumber;

	/**
	 * 核算项目类型1
	 */
	private String asstActType1;

	/**
	 * 核算项目编码1
	 */
	private String asstActNumber1;

	/**
	 * 核算项目名称1
	 */
	private String asstActName1;

	/**
	 * 核算项目类型2
	 */
	private String asstActType2;

	/**
	 * 核算项目编码2
	 */
	private String asstActNumber2;

	/**
	 * 核算项目名称2
	 */
	private String asstActName2;

	/**
	 * 核算项目类型3
	 */
	private String asstActType3;

	/**
	 * 核算项目编码3
	 */
	private String asstActNumber3;

	/**
	 * 核算项目名称3
	 */
	private String asstActName3;

	/**
	 * 核算项目类型4
	 */
	private String asstActType4;

	/**
	 * 核算项目编码4
	 */
	private String asstActNumber4;

	/**
	 * 核算项目名称4
	 */
	private String asstActName4;

	/**
	 * 核算项目类型5
	 */
	private String asstActType5;

	/**
	 * 核算项目编码5
	 */
	private String asstActNumber5;

	/**
	 * 核算项目名称5
	 */
	private String asstActName5;

	/**
	 * 核算项目类型6
	 */
	private String asstActType6;

	/**
	 * 核算项目编码6
	 */
	private String asstActNumber6;

	/**
	 * 核算项目名称6
	 */
	private String asstActName6;

	/**
	 * 核算项目类型7
	 */
	private String asstActType7;

	/**
	 * 核算项目编码7
	 */
	private String asstActNumber7;

	/**
	 * 核算项目名称7
	 */
	private String asstActName7;

	/**
	 * 核算项目类型8
	 */
	private String asstActType8;

	/**
	 * 核算项目编码8
	 */
	private String asstActNumber8;

	/**
	 * 核算项目名称8
	 */
	private String asstActName8;

	/**
	 * 现金流量标记
	 */
	private String itemFlag;

	/**
	 * 对方分录号
	 */
	private String oppAccountSeq;

	/**
	 * 对方辅助账号
	 */
	private String oppAsstSeq;

	/**
	 * 主表信息
	 */
	private String primaryItem;

	/**
	 * 主表系数
	 */
	private String primaryCoef;

	/**
	 * 附表信息
	 */
	private String supplyItem;

	/**
	 * 附表系数
	 */
	private String supplyCoef;

	/**
	 * 补充资料
	 */
	private String fullInfoItem;

	/**
	 * 性质
	 */
	private String type;

	/**
	 * 原币金额
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal cashflowAmountOriginal;

	/**
	 * 本位币金额
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal cashflowAmountLocal;

	/**
	 * 报告币金额
	 */
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	private BigDecimal cashflowAmountRpt;
}
