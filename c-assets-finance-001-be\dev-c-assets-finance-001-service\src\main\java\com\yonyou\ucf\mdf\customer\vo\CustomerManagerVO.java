package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户管理员信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerManagerVO {
    /**
     * 手机区号；填写手机号是必填
     * 示例：86
     */
    private String countryCode;

    /**
     * 手机号；客户管理员手机号或者邮箱至少有一项必填
     * 示例：手机号
     */
    private String mobile;

    /**
     * 姓名；客户管理员姓名非空时,手机或者邮箱必输；如果手机号或者邮箱用户管理存在，会和用户管理名称保持一致
     * 示例：姓名
     */
    private String fullName;

    /**
     * 邮箱；客户管理员手机号或者邮箱至少有一项必填
     * 示例：邮箱
     */
    private String email;
} 