package com.yonyou.ucf.mdf.base.service.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:22
 * @DESCRIPTION 业务单元实体类
 */
@Data
public class BaseOrg {
    /*** 编码 */
    private String code;

    /*** 名称（多语言支持） */
    private MultiLang name;

    /*** 简称（多语言支持） */
    private MultiLang shortname;

    /*** 组织形态ID（与companytype_code二选一） */
    private String companytype;

    /*** 组织形态编码（与companytype二选一） */
    private String companytype_code;

    /*** 上级组织单元ID（与parent_code二选一） */
    private String parent;

    /*** 上级组织单元编码（与parent二选一） */
    private String parent_code;

    /*** 所属上级ID（部门专用） */
    private String deptparentid;

    /*** 所属上级编码（部门专用） */
    private String deptparentid_code;

    /*** 所属人力组织ID（无人力职能时必填） */
    private String parentorgid;

    /*** 所属人力组织编码（无人力职能时必填） */
    private String parentorgid_code;

    /*** 组织类型（1-组织；2-部门型组织） */
    private String orgtype;

    /*** 部门性质ID（与depttype_code二选一） */
    private String depttype;

    /*** 部门性质编码（1-销售部门...7-基建部门） */
    private String depttype_code;

    /*** 负责人ID（与principal_code二选一） */
    private String principal;

    /*** 负责人编码（与principal二选一） */
    private String principal_code;

    /*** 分管领导ID（与branchleader_code二选一） */
    private String branchleader;

    /*** 分管领导编码（与branchleader二选一） */
    private String branchleader_code;

    /*** 纳税人识别号 */
    private String taxpayerid;

    /*** 纳税人名称 */
    private String taxpayername;

    /*** 曾用纳税人识别号 */
    private String usedtaxpayerid;

    /*** 曾用纳税人名称 */
    private String usedtaxpayername;

    /*** 纳税人类型（1-一般纳税人；2-小规模） */
    private String taxpayertype;

    /*** 汇率类型ID（与exchangerate_code二选一） */
    private String exchangerate;

    /*** 汇率类型编码 */
    private String exchangerate_code;

    /*** 语言ID（与language_select_language二选一） */
    private String language;

    /*** 语言名称 */
    private String language_select_language;

    /*** 时区ID */
    private String timezone;

    /*** 联系人 */
    private String contact;

    /*** 联系电话 */
    private String telephone;

    /*** 外部系统主键 */
    private String objid;

    /*** 联系地址（多语言） */
    private MultiLang address;

    /*** 描述（多语言） */
    private MultiLang description;

    /*** 状态（0-未启用；1-启用；2-停用） */
    private String enable;

    /*** 业务单元主键（修改时必填） */
    private String id;

    /*** 操作标识（Insert/Update） */
    private String _status;

    /*** 自定义特征组 */
    private Object unitdefinefeature;

    /*** 会计主体 */
    private FinanceOrg financeOrg;

    /*** 资金组织 */
    private FundsOrg fundsOrg;

    /*** 销售组织 */
    private CommonParentOrg salesOrg;

    /*** 采购组织 */
    private PurchaseOrg purchaseOrg;

    /*** 库存组织 */
    private CommonOrg inventoryOrg;

    /*** 工厂组织 */
    private CommonOrg commonOrg;

    /*** 资产组织 */
    private CommonOrg assetsOrg;

    /*** 纳税主体 */
    private CommonParentOrg commonParentOrg;

    /*** 行政组织 */
    private AdminOrg adminOrg;

    /*** 会计主体 */
    @Data
    public static class FinanceOrg {
        /*** 上级会计主体ID */
        private String parentid;
        /*** 上级会计主体编码 */
        private String parentid_code;
        /*** 币种ID */
        private String currency;
        /*** 币种名称 */
        private String currency_name;
        /*** 期间方案ID */
        private String periodschema;
        /*** 期间方案编码 */
        private String periodschema_code;
        /*** 对外核算（1-是；0-否） */
        private String isexternalaccounting;
        /*** 对内核算（1-是；0-否） */
        private String isinternalaccounting;
        /*** 状态 */
        private String enable;
        /*** 主键（与业务单元ID一致） */
        private String id;
        // getter/setter
    }

    /*** 资金组织 */
    @Data
    public static class FundsOrg {
        /*** 关联会计主体ID */
        private String finorgid;
        /*** 关联会计主体编码 */
        private String finorgid_code;
        /*** 上级资金组织ID */
        private String parentid;
        /*** 上级资金组织编码 */
        private String parentid_code;
        /*** 状态（0-未启用；1-启用；2-停用） */
        private Integer enable;
        /*** 主键 */
        private String id;
        // getter/setter
    }

    // 其他嵌套类（仅展示关键字段，需补充完整）
    @Data
    public static class PurchaseOrg {
        private String contactOrg;
        private String contactOrg_code;
        private String enable;
        private String id;
    }

    @Data
    public static class CommonOrg {
        /**状态, 0:未启用、1:启用、2:停用、 不传默认未启用*/
        private String enable;
        /**与业务单元主键id值一致*/
        private String id;
    }

    @Data
    public static class CommonParentOrg {
        private String parentid;
        private String parentid_code;
        private String enable;
        private String id;
    }

    /*** 行政组织 */
    @Data
    public static class AdminOrg {
        /*** 上级行政组织ID */
        private String parentorgid;
        /*** 上级行政组织编码 */
        private String parentorgid_code;
        /*** 合同主体ID */
        private String corpid;
        /*** 合同主体编码 */
        private String corpid_code;
        /*** 工作地点ID */
        private String locationid;
        /*** 工作地点编码 */
        private String locationid_code;
        /*** 主键 */
        private String id;
        /*** 状态 */
        private String enable;
        /*** 自定义特征组 */
        private Object admindefinefeature;
    }
}
