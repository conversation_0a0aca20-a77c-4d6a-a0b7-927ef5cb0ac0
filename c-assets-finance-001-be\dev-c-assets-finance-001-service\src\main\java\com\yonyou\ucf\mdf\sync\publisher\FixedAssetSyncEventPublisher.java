package com.yonyou.ucf.mdf.sync.publisher;

import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.EventTranslator;
import com.lmax.disruptor.RingBuffer;
import com.yonyou.ucf.mdf.asset.enums.AssetBillTypeEnum;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;
import com.yonyou.ucf.mdf.sync.event.FixedAssetSyncEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 固定资产同步事件发布器
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class FixedAssetSyncEventPublisher {

	@Autowired
	private RingBuffer<FixedAssetSyncEvent> ringBuffer;

	/**
	 * 发布固定资产新增事件
	 */
	public void publishAddEvent(AssetBillTypeEnum assetBillType, BizObject bizObject) {
		publishEvent(new EventTranslator<FixedAssetSyncEvent>() {
			@Override
			public void translateTo(FixedAssetSyncEvent event, long sequence) {
				event.setSyncType(FixedAssetSyncTypeEnum.ADD);
				event.setAssetBillType(assetBillType);
				event.setParam(bizObject);
			}
		});
	}

	/**
	 * 发布固定资产变更事件
	 */
	public void publishChangeEvent(AssetBillTypeEnum assetBillType, BizObject bizObject) {
		publishEvent(new EventTranslator<FixedAssetSyncEvent>() {
			@Override
			public void translateTo(FixedAssetSyncEvent event, long sequence) {
				event.setSyncType(FixedAssetSyncTypeEnum.CHANGE);
				event.setAssetBillType(assetBillType);
				event.setParam(bizObject);
			}
		});
	}

	/**
	 * 发布固定资产清理事件
	 */
	public void publishClearEvent(AssetBillTypeEnum assetBillType, BizObject bizObject) {
		publishEvent(new EventTranslator<FixedAssetSyncEvent>() {
			@Override
			public void translateTo(FixedAssetSyncEvent event, long sequence) {
				event.setSyncType(FixedAssetSyncTypeEnum.CLEAR);
				event.setAssetBillType(assetBillType);
				event.setParam(bizObject);
			}
		});
	}

	/**
	 * 发布固定资产调拨事件
	 */
	public void publishDispatchEvent(AssetBillTypeEnum assetBillType, BizObject bizObject) {
		publishEvent(new EventTranslator<FixedAssetSyncEvent>() {
			@Override
			public void translateTo(FixedAssetSyncEvent event, long sequence) {
				event.setSyncType(FixedAssetSyncTypeEnum.DISPATCH);
				event.setAssetBillType(assetBillType);
				event.setParam(bizObject);
			}
		});
	}

	/**
	 * 发布事件到RingBuffer
	 */
	private void publishEvent(EventTranslator<FixedAssetSyncEvent> translator) {
		try {
			ringBuffer.publishEvent(translator);
		} catch (Exception e) {
			log.error("发布固定资产同步事件失败", e);
			throw new RuntimeException("发布固定资产同步事件失败", e);
		}
	}
}
