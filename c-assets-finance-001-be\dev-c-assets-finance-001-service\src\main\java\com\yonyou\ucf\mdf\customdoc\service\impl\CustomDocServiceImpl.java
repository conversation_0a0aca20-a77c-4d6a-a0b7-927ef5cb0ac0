package com.yonyou.ucf.mdf.customdoc.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocVO;
import com.yonyou.ucf.mdf.customdoc.service.CustomDocService;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义档案服务实现
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Service
public class CustomDocServiceImpl implements CustomDocService {

	private static final String BATCH_SAVE_API = "/yonbip/digitalModel/custdoc/savelist_integration";

	@Autowired
	private SyncApiRequest syncApiRequest;
	@Autowired
	private IBillRepository billRepository;
	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public ResponseResult<BatchResult> batchSaveCustomDoc(List<CustomDocVO> customDocVOList) {
		try {
			Map<String, List<CustomDocVO>> groupMap = customDocVOList.stream()
					.collect(Collectors.groupingBy(CustomDocVO::getCustdocdefid_code));
			for (String defcode : groupMap.keySet()) {
				Map<String, CustomDocVO> oldData = findByCodes(
						customDocVOList.stream().map(CustomDocVO::getCode).collect(Collectors.toList()), defcode);
				if (!oldData.isEmpty()) {
					for (CustomDocVO customDocVO : customDocVOList) {
						CustomDocVO old = oldData.get(customDocVO.getCode());
						if (old != null) {
							customDocVO.setId(old.getId());
						}
					}
				}
			}
			String result = syncApiRequest.doPostData(BATCH_SAVE_API, customDocVOList);
			return JsonUtils.parseObject(result, new TypeReference<ResponseResult<BatchResult>>() {
			});
		} catch (Exception e) {
			log.error("批量保存自定义档案失败", e);
			throw new RuntimeException("批量保存自定义档案失败", e);
		}
	}

	/**
	 * 根据编码获取已存在档案
	 * 
	 * @param codes
	 * @return
	 */
	private Map<String, CustomDocVO> findByCodes(List<String> codes, String defcode) {
		String sql = String.format(
				"select a.id,a.code,a.orgid from iuap_apdoc_basedoc.bd_cust_doc a left join iuap_apdoc_basedoc.bd_cust_doc_def b on a.custdocdefid = b.id  where a.code in (%s) and b.code ='%s' and a.ytenant_id = '%s' and a.enable = 1 and a.dr = 0",
				codes.stream().map(c -> StringUtils.wrap(c, "'")).collect(Collectors.joining(",")), defcode, tenantId);

		List<CustomDocVO> result = billRepository.queryForDTOList(sql, null, CustomDocVO.class);
		if (CollectionUtils.isEmpty(result)) {
			return Collections.emptyMap();
		}
		return result.stream().collect(Collectors.toMap(v -> {
			return v.getCode();
		}, v -> v, (v1, v2) -> v2));
	}

}