{"java.configuration.updateBuildConfiguration": "interactive", "java.compile.nullAnalysis.mode": "automatic", "maven.executable.preferMavenWrapper": false, "maven.pomfile.autoUpdateEffectivePOM": true, "maven.terminal.useJavaHome": true, "java.maven.downloadSources": true, "java.maven.updateSnapshots": true, "java.configuration.maven.userSettings": "D:\\Program Files\\apache-maven-3.8.1\\conf\\settings.xml", "java.configuration.maven.globalSettings": "D:\\Program Files\\apache-maven-3.8.1\\conf\\settings.xml", "java.import.maven.disableTestClasspathFlag": true, "java.jdt.ls.java.home": "D:\\Program Files\\Java\\jdk1.8.0_291", "git.autofetch": true, "git.blame.editorDecoration.enabled": true, "git.diagnosticsCommitHook.enabled": true, "git.mergeEditor": true, "java.dependency.showMembers": true, "java.project.encoding": "ignore", "java.project.outputPath": "", "java.completion.chain.enabled": true, "java.completion.importOrder": ["#", "java", "javax", "org", "com", ""], "java.codeGeneration.generateComments": true, "java.codeGeneration.hashCodeEquals.useInstanceof": false, "java.saveActions.organizeImports": true, "java.referencesCodeLens.enabled": false, "java.eclipse.downloadSources": true, "boot-java.highlight-copilot-codelens.on": true}