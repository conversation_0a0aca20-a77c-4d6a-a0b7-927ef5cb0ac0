package com.yonyou.ucf.mdf.sync.model;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 固定资产同步失败记录
 * @Date 2025-06-13 17:37:33
 * @since 2023/11/28
 **/
@YMSEntity(name = "cxkingdee-sync.cxkingdee-sync.FixedAssetSyncFailRecord", domain = "c-assets-finance-001")
public class FixedAssetSyncFailRecord extends SuperDO {
	public static final String ENTITY_NAME = "cxkingdee-sync.cxkingdee-sync.FixedAssetSyncFailRecord";
	public static final String BUSINESSNO = "businessNo";
	public static final String ASSETCODE = "assetCode";
	public static final String ASSETNAME = "assetName";
	public static final String SYNCTYPE = "syncType";
	public static final String ASSETBILLTYPE = "assetBillType";
	public static final String ASSETBILLID = "assetBillId";
	public static final String RAWDATA = "rawData";
	public static final String REQUESTDATA = "requestData";
	public static final String RESPONEDATA = "responeData";
	public static final String SUCCESS = "success";
	public static final String ERRMSG = "errMsg";
	public static final String ERRSTACK = "errStack";
	public static final String COSTTIME = "costTime";
	public static final String RETRYCOUNT = "retryCount";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 业务单据号 */
	private String businessNo;
	/* 资产编号 */
	private String assetCode;
	/* 资产名称 */
	private String assetName;
	/* 同步类型 */
	private String syncType;
	/* 资产单据类型 */
	private String assetBillType;
	/* 资产单据id */
	private String assetBillId;
	/* 原始数据 */
	private String rawData;
	/* 推送金蝶数据 */
	private String requestData;
	/* 金蝶返回数据 */
	private String responeData;
	/* 成功标识 */
	private String success;
	/* 错误信息 */
	private String errMsg;
	/* 错误堆栈 */
	private String errStack;
	/* 耗时 */
	private Integer costTime;
	/* 重试次数 */
	private Integer retryCount;
	/* 单据日期 */
	private String businessDate;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setBusinessNo(String businessNo) {
		this.businessNo = businessNo;
	}

	public void setAssetCode(String assetCode) {
		this.assetCode = assetCode;
	}

	public void setAssetName(String assetName) {
		this.assetName = assetName;
	}

	public void setSyncType(String syncType) {
		this.syncType = syncType;
	}

	public void setAssetBillType(String assetBillType) {
		this.assetBillType = assetBillType;
	}

	public void setAssetBillId(String assetBillId) {
		this.assetBillId = assetBillId;
	}

	public void setRawData(String rawData) {
		this.rawData = rawData;
	}

	public void setRequestData(String requestData) {
		this.requestData = requestData;
	}

	public void setResponeData(String responeData) {
		this.responeData = responeData;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public void setErrStack(String errStack) {
		this.errStack = errStack;
	}

	public void setCostTime(Integer costTime) {
		this.costTime = costTime;
	}

	public void setRetryCount(Integer retryCount) {
		this.retryCount = retryCount;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getBusinessNo() {
		return businessNo;
	}

	public String getAssetCode() {
		return assetCode;
	}

	public String getAssetName() {
		return assetName;
	}

	public String getSyncType() {
		return syncType;
	}

	public String getAssetBillType() {
		return assetBillType;
	}

	public String getAssetBillId() {
		return assetBillId;
	}

	public String getRawData() {
		return rawData;
	}

	public String getRequestData() {
		return requestData;
	}

	public String getResponeData() {
		return responeData;
	}

	public String getSuccess() {
		return success;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public String getErrStack() {
		return errStack;
	}

	public Integer getCostTime() {
		return costTime;
	}

	public Integer getRetryCount() {
		return retryCount;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
