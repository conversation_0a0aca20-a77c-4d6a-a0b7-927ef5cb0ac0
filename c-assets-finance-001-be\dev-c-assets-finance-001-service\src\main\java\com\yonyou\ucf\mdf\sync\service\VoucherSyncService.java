package com.yonyou.ucf.mdf.sync.service;

import java.util.List;

import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;

/**
 * 凭证同步服务
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface VoucherSyncService {

	/**
	 * 同步凭证数据
	 * 
	 * @param fromPeriodNumber 起始期间
	 * @param toPeriodNumber   结束期间
	 * @param companyNumber    会计主体编码
	 * @param voucherNumber TODO
	 */
	void syncVoucher(String fromPeriodNumber, String toPeriodNumber, String companyNumber, String voucherNumber);

	/**
	 * 发布凭证同步事件
	 * 
	 * @param kingdeeVouchers 金蝶凭证数据列表
	 */
	void publishEvent(List<KingdeeVoucher> kingdeeVouchers);
}