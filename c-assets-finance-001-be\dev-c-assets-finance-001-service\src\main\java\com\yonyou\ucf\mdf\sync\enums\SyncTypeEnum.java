package com.yonyou.ucf.mdf.sync.enums;

/**
 * 数据同步类型枚举
 * 
 * <AUTHOR>
 *
 *         2025年5月29日
 */
public enum SyncTypeEnum {

	/**
	 * 供应商数据同步
	 */
	VENDOR("供应商同步", "数据同步默认字段"),

	/**
	 * 客户数据同步
	 */
	CUSTOMER("客户同步", "数据同步默认字段"),

	/**
	 * 银行网点数据同步
	 */
	BANK_BRANCH("银行网点同步", "数据同步默认字段"),

	/**
	 * 企业银行账户数据同步
	 */
	BANK_ACCOUNT("企业银行账户同步", "数据同步默认字段"),

	/**
	 * 自定义档案定义数据同步
	 */
	CUSTOM_DOC_DEFINITION("自定义档案定义同步", "数据同步默认字段"),

	/**
	 * 自定义档案维护数据同步
	 */
	CUSTOM_DOC("自定义档案维护同步", "数据同步默认字段"),

	/**
	 * 自定义档案维护数据同步
	 */
	VOUCHER("凭证同步", "数据同步默认字段"),

	/**
	 * 固定资产数据同步（同步到金蝶）
	 */
	FIX_ASSET("资产同步到金蝶", "数据同步默认字段");

	/**
	 * 操作标识
	 */
	private final String name;

	/**
	 * 操作描述
	 */
	private final String defaultValueName;

	SyncTypeEnum(String name, String defaultValueName) {
		this.name = name;
		this.defaultValueName = defaultValueName;
	}

	public String getName() {
		return name;
	}

	public String getDefaultValueName() {
		return defaultValueName;
	}

}
