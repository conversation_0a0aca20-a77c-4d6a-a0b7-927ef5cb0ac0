package com.yonyou.ucf.mdf.bank.service.impl;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.bank.service.BankAccountService;
import com.yonyou.ucf.mdf.bank.vo.BankAccountCurrencyVO;
import com.yonyou.ucf.mdf.bank.vo.BankAccountVO;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.sync.enums.SyncActionEnum;
import com.yonyou.ucf.mdf.sync.util.SyncApiRequest;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 企业银行账户服务实现
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
@Slf4j
@Service
public class BankAccountServiceImpl implements BankAccountService {

	private static final String BATCH_SAVE_API = "/yonbip/digitalModel/enterprisebank/batchSave_integration";

	@Autowired
	private SyncApiRequest syncApiRequest;
	@Autowired
	private IBillQueryRepository billQryRepository;
	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@Override
	public ResponseResult<BatchResult> batchSaveBankAccount(List<BankAccountVO> bankAccountVOList) {
		try {
			for (BankAccountVO bankAccountVO : bankAccountVOList) {
				BankAccountVO old = getByCode(bankAccountVO.getCode());
				if (old != null) {
					bankAccountVO.setId(old.getId());
					bankAccountVO.set_status(SyncActionEnum.UPDATE.getValue());
					if (CollectionUtils.isNotEmpty(old.getCurrencyList())) {
						BankAccountCurrencyVO currency = bankAccountVO.getCurrencyList().get(0);
						BankAccountCurrencyVO oldcurrency = old.getCurrencyList().get(0);
						currency.setId(oldcurrency.getId());
						currency.set_status(SyncActionEnum.UPDATE.getValue());
					}

				}
			}
			String result = syncApiRequest.doPostData(BATCH_SAVE_API, bankAccountVOList);
			return JsonUtils.parseObject(result, new TypeReference<ResponseResult<BatchResult>>() {
			});
		} catch (Exception e) {
			log.error("批量保存企业银行账户失败", e);
			throw new RuntimeException("批量保存企业银行账户失败", e);
		}
	}

	@Override
	public BankAccountVO getByCode(String code) {
		return RobotExecutors.runAs(tenantId, () -> {
			try {
				QuerySchema schema = QuerySchema.create();
				schema.addSelect("id,code");
				schema.addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(code)));
				QuerySchema sonSchema = QuerySchema.create().name("currencyList").addSelect("id,currency");
				schema.addCompositionSchema(sonSchema);
				List<Map<String, Object>> result = billQryRepository.queryMapBySchema("bd.enterprise.OrgFinBankacctVO",
						schema, "ucfbasedoc");
				if (CollectionUtils.isEmpty(result)) {
					return null;
				}
				return JsonUtils.parseObject(JSON.toJSONString(result.get(0)), BankAccountVO.class);
			} catch (Exception e) {
				log.error("根据编码获取企业银行账户失败，编码：{}", code, e);
				throw new RuntimeException("根据编码获取企业银行账户失败", e);
			}
		});

	}
}