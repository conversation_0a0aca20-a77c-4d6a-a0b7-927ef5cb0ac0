package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.customdoc.model.CustomDocDefinition;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;

/**
 * 自定义档案定义数据转换器
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
public interface CustomDocDefinitionConverter {

    /**
     * 将金蝶自定义档案定义数据转换为自定义档案定义VO
     * 
     * @param kingdeeCustomDocDefinition 金蝶自定义档案定义数据
     * @return 自定义档案定义VO
     */
    CustomDocDefinition convert(KingdeeCustomDocDefinition kingdeeCustomDocDefinition);
} 