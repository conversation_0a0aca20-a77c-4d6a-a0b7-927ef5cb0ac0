package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.VoucherSyncEvent;
import com.yonyou.ucf.mdf.sync.model.VoucherSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.VoucherSyncLog;
import com.yonyou.ucf.mdf.sync.model.VoucherSyncRecord;
import com.yonyou.ucf.mdf.sync.service.VoucherConverter;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncLogService;
import com.yonyou.ucf.mdf.sync.service.VoucherSyncRecordService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;
import com.yonyou.ucf.mdf.voucher.model.Voucher;
import com.yonyou.ucf.mdf.voucher.service.VoucherService;
import com.yonyou.ucf.mdf.voucher.vo.VoucherSaveResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 凭证同步事件处理器
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Component
public class VoucherSyncEventHandler implements EventHandler<VoucherSyncEvent> {

	@Autowired
	private VoucherService voucherService;

	@Autowired
	private VoucherSyncLogService voucherSyncLogService;
	@Autowired
	private VoucherSyncFailRecordService voucherSyncFailRecordService;
	@Autowired
	private VoucherSyncRecordService voucherSyncRecordService;

	@Autowired
	private VoucherConverter voucherConverter;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(VoucherSyncEvent event, long sequence, boolean endOfBatch) throws Exception {
		if (event == null || event.getKingdeeVouchers() == null || event.getKingdeeVouchers().isEmpty()) {
			log.warn("凭证同步事件数据为空");
			return;
		}

		List<KingdeeVoucher> vouchers = event.getKingdeeVouchers();
		// 获取第一张凭证的基本信息用于日志
		KingdeeVoucher firstVoucher = vouchers.get(0);
		String voucherNumber = firstVoucher.getVoucherNumber();
		String voucherId = firstVoucher.getVoucherId();
		String companyNumber = firstVoucher.getCompanyNumber();
		String companyName = firstVoucher.getCompanyName();
		String period = firstVoucher.getPeriodYear() + "-"
				+ String.format("%02d", Integer.parseInt(firstVoucher.getPeriodNumber()));

		log.info("开始处理凭证同步事件，凭证号：{}，会计主体：{}，期间：{}，明细数量：{}", voucherNumber, companyNumber, period, vouchers.size());

		// 创建同步日志
		VoucherSyncLog syncLog = initialSyncLog(vouchers, voucherNumber, voucherId, companyNumber, companyName, period);
		VoucherSyncRecord syncRecord = initialSyncRecord(syncLog);

		long startTime = System.currentTimeMillis();
		try {
			// 转换金蝶凭证数据
			Voucher voucher = voucherConverter.convert(vouchers);
			if (voucher == null) {
				throw new RuntimeException("转换金蝶凭证数据失败");
			}

			// 保存凭证数据
			ResponseResult<VoucherSaveResult> responseResult = voucherService.save(voucher);

			syncLog.setRequestData(JsonUtils.toJsonString(voucher));
			syncLog.setResponeData(JsonUtils.toJsonString(responseResult));

			syncRecord.setRequestData(syncLog.getRequestData());
			syncRecord.setResponeData(syncLog.getResponeData());

			if (!responseResult.isSuccess()) {
				throw new RuntimeException(responseResult.getMessage());
			}

			// 更新同步日志
			syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
			syncLog.setCostTime((int) (System.currentTimeMillis() - startTime));
			voucherSyncLogService.save(syncLog);

			syncRecord.setSuccess(SyncStatusEnum.SUCCESS.getCode());
			VoucherSaveResult result = responseResult.getData();
			if (result != null) {
				if (result.getAccbook() != null) {
					syncRecord.setAccbook(result.getAccbook().getId());
				}
				syncRecord.setVoucherCode(result.getBillCode());
				syncRecord.setVoucherId(result.getVoucherId());
				if (result.getVoucherType() != null) {
					syncRecord.setVoucherType(result.getVoucherType().getId());
				}
				syncRecord.setTotalCreditOrg(result.getTotalCreditOrg());
				syncRecord.setTotalDebitOrg(result.getTotalDebitOrg());
			}
			voucherSyncRecordService.save(syncRecord);

			log.info("凭证同步成功，凭证号：{}", voucherNumber);
		} catch (Exception e) {
			log.error("凭证同步失败，凭证号：{}", voucherNumber, e);

			// 更新同步日志
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg(e.getMessage());
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			syncLog.setCostTime((int) (System.currentTimeMillis() - startTime));
			voucherSyncLogService.save(syncLog);

			syncRecord.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncRecord.setErrMsg(e.getMessage());
			syncRecord.setErrStack(ExceptionUtils.getStackTrace(e));
			voucherSyncRecordService.save(syncRecord);
		}

		VoucherSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			voucherSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_VOUCHER, firstVoucher.getVoucherId(), vouchers);
			ehCacheUtil.removeFail(CacheConstant.CACHE_VOUCHER, firstVoucher.getVoucherId());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_VOUCHER, firstVoucher.getVoucherId(), vouchers);
		}

	}

	/**
	 * 初始化同步记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private VoucherSyncRecord initialSyncRecord(VoucherSyncLog syncLog) {
		VoucherSyncRecord syncRecord = new VoucherSyncRecord();
		syncRecord.setKingdeeId(syncLog.getVoucherId());
		syncRecord.setKingdeeCode(syncLog.getVoucherNumber());
		syncRecord.setCompanyNumber(syncLog.getCompanyNumber());
		syncRecord.setCompanyName(syncLog.getCompanyName());
		syncRecord.setKingdeeData(syncLog.getKingdeeData());
		syncRecord.setBusinessDate(syncLog.getBusinessDate());
		syncRecord.setLastDate(new Date());
		syncRecord.setPeriod(syncLog.getPeriod());
		syncRecord.set_status(ActionEnum.INSERT.getValueInt());
		return syncRecord;
	}

	/**
	 * 初始化同步日志
	 * 
	 * @param vouchers
	 * @param voucherNumber
	 * @param voucherId
	 * @param companyNumber
	 * @param companyName
	 * @param period
	 * @return
	 */
	private VoucherSyncLog initialSyncLog(List<KingdeeVoucher> vouchers, String voucherNumber, String voucherId,
			String companyNumber, String companyName, String period) {
		VoucherSyncLog syncLog = new VoucherSyncLog();
		syncLog.setVoucherNumber(voucherNumber);
		syncLog.setVoucherId(voucherId);
		syncLog.setCompanyNumber(companyNumber);
		syncLog.setCompanyName(companyName);
		syncLog.setPeriod(period);
		syncLog.setKingdeeData(JsonUtils.toJsonString(vouchers));
		syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
		syncLog.setCreateTime(new Date());
		syncLog.set_status(ActionEnum.INSERT.getValueInt());
		return syncLog;
	}

	/**
	 * 删除失败的记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(VoucherSyncLog syncLog) {
		voucherSyncFailRecordService.deleteByVoucherId(syncLog.getVoucherId());
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private VoucherSyncFailRecord convertFailRecord(VoucherSyncLog syncLog) {
		if (syncLog == null || SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			return null;
		}
		VoucherSyncFailRecord failRecord = new VoucherSyncFailRecord();
		failRecord.setVoucherNumber(syncLog.getVoucherNumber());
		failRecord.setVoucherId(syncLog.getVoucherId());
		failRecord.setCompanyNumber(syncLog.getCompanyNumber());
		failRecord.setCompanyName(syncLog.getCompanyName());
		failRecord.setCostTime(syncLog.getCostTime());
		failRecord.setPeriod(syncLog.getPeriod());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setRetryCount(0);
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		return failRecord;
	}
}