package com.yonyou.ucf.mdf.base.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
 * 响应结果
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ResponseResult<T> {

	/**
	 * 响应码
	 */
	private String code;

	/**
	 * 响应消息
	 */
	private String message;

	/**
	 * 响应数据
	 */
	private T data;

	/**
	 * 判断是否成功
	 * 
	 * @return true:成功 false:失败
	 */
	public boolean isSuccess() {
		return "200".equals(this.code);
	}

	/**
	 * 成功
	 * 
	 * @return 响应结果
	 */
	public static <T> ResponseResult<T> success() {
		ResponseResult<T> result = new ResponseResult<>();
		result.setCode("200");
		result.setMessage("success");
		return result;
	}

	/**
	 * 成功
	 * 
	 * @param data 数据
	 * @return 响应结果
	 */
	public static <T> ResponseResult<T> success(T data) {
		ResponseResult<T> result = new ResponseResult<>();
		result.setCode("200");
		result.setData(data);
		return result;
	}

	/**
	 * 失败
	 * 
	 * @param message 错误信息
	 * @return 响应结果
	 */
	public static <T> ResponseResult<T> error(String message) {
		ResponseResult<T> result = new ResponseResult<>();
		result.setCode("500");
		result.setMessage(message);
		return result;
	}

	/**
	 * 快速构建成功返回
	 * 
	 * @param data    数据
	 * @param message 消息
	 * @return 响应结果
	 */
	public static <T> ResponseResult<T> ok(T data, String message) {
		ResponseResult<T> result = new ResponseResult<>();
		result.setCode("200");
		result.setData(data);
		result.setMessage(message);
		return result;
	}
}