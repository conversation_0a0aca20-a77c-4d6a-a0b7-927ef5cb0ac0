package com.yonyou.ucf.mdf.customdoc.service;

import java.util.List;

import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.customdoc.model.CustomDocVO;

/**
 * 自定义档案服务
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
public interface CustomDocService {

	/**
	 * 批量保存自定义档案
	 * 
	 * @param customDocVOList 自定义档案列表
	 * @return 批量保存结果
	 */
	ResponseResult<BatchResult> batchSaveCustomDoc(List<CustomDocVO> customDocVOList);

}