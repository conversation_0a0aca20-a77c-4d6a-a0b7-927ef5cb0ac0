package com.yonyou.ucf.mdf.kingdee.voucher.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.voucher.service.KingdeeVoucherService;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucher;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucherQueryParams;
import com.yonyou.ucf.mdf.kingdee.voucher.vo.KingdeeVoucherResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 金蝶凭证查询服务实现类
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Slf4j
@Service
public class KingdeeVoucherServiceImpl implements KingdeeVoucherService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Override
	public List<KingdeeVoucher> queryVouchers(KingdeeVoucherQueryParams queryParams) {
		log.info("开始查询金蝶凭证，查询参数：{}", queryParams);
		try {
			// 调用金蝶接口获取数据
			String result = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.VOUCHER, "nExportVoucher",
					queryParams);
			if (StringUtils.isBlank(result)) {
				log.error("未获取到凭证数据");
				return new ArrayList<>();
			}

			// 解析返回结果
			KingdeeVoucherResponse response = JSON.parseObject(result, KingdeeVoucherResponse.class);
			if (!response.isSuccess()) {
				log.error("查询凭证失败，错误信息：{}", response != null ? response.errMsg() : "返回结果为空");
				throw new RuntimeException(response.errMsg());
			}

			log.info("查询金蝶凭证完成，共获取{}条数据", response.getVoucherCount());
			return response.convertToVouchers();
		} catch (Exception e) {
			log.error("查询金蝶凭证异常", e);
			throw new RuntimeException("查询金蝶凭证异常", e);
		}
	}

}