package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.model.VendorSyncFailRecord;

/**
 * 供应商同步失败记录服务
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
public interface VendorSyncFailRecordService {

    /**
     * 保存失败记录
     * 
     * @param failRecord 失败记录
     */
    void saveFailRecord(VendorSyncFailRecord failRecord);

    /**
     * 根据编码删除失败记录
     * 
     * @param code 编码
     */
    void deleteByCode(String code);
} 