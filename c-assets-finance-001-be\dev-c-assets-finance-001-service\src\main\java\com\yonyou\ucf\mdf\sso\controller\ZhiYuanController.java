package com.yonyou.ucf.mdf.sso.controller;

import com.yonyou.ucf.mdf.sso.common.SeeyonParam;
import com.yonyou.ucf.mdf.sso.common.StateInfoPO;
import com.yonyou.ucf.mdf.sso.common.UrlConstant;
import com.yonyou.ucf.mdf.sso.service.CasService;
import com.yonyou.ucf.mdf.sso.service.SeeyonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * @className: ZhiYuanController
 * @author: wjc
 * @date: 2025/5/20 10:22
 * @Version: 1.0
 * @description:
 */
@Controller
public class ZhiYuanController {
    private static final Logger logger = LoggerFactory.getLogger(ZhiYuanController.class);
    //调用用友接口
    @Value("${YonBIP.appKey}")
    private String appKeyBIP;

    @Value("${YonBIP.appSecret}")
    private String appSecretBIP;

    @Value("${domain.url:}")
    private String domainUrl;

    @Value("${YonBIP.thirdUcId}")
    private String thirdUcId;

    @Value("${third.seeyonUrl}")
    private String seeyonUrl;
    @Autowired
    private CasService casService;

    @Autowired
    private SeeyonService seeyonService;


    private static SeeyonParam thirdPublicParam = new SeeyonParam();

    @PostConstruct
    private void init() {
        thirdPublicParam.setSeeyonUrl(seeyonUrl);
    }
    /**
     * 三方单点登录入口
     */
    @GetMapping(value = "/auth/ssoBip")
    public void ssoBip(HttpServletResponse response, HttpServletRequest request) throws IOException {
        //获取最终跳转的url
        String redirectUrl;
        try {
            String ticket = request.getParameter("ticket");
            if (StringUtils.isBlank(ticket)) {
                ticket = request.getParameter("v5ticket");
            }
            if (StringUtils.isEmpty(ticket)) {
                throw new RuntimeException("Ticket Is Empty");
            }
            String state = request.getParameter("state");
            StateInfoPO stateInfo = seeyonService.getStateInfo(state);
            redirectUrl = getRedirectUri(ticket,stateInfo.getBizUrl(),stateInfo.getUserId());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            redirectUrl = "/iuap-ipaas-sso/error?msg=" + e.getMessage();
        }
        response.sendRedirect(redirectUrl);
    }

    private String getRedirectUri(String ticket,String redirectUrl,String userId) throws UnsupportedEncodingException {

        logger.error("【单点登录信息】redirectUrl={},appKeyBIP={},appSecretBIP={},thirdUcId={},openApiUrl={}",
                redirectUrl, appKeyBIP, appSecretBIP, thirdUcId, domainUrl);
        String encodeRedirctUrl = URLEncoder.encode(redirectUrl, "UTF-8");
        logger.error("【redirectUrl编码后数据】encodeRedirctUrl={}", encodeRedirctUrl);
        // 获取YonBIP AccessToken
        String yonBIPAccessToken = casService.getAccessToken(domainUrl + UrlConstant.OPEN_API_URL, appKeyBIP, appSecretBIP);
        String thirdBindUserId = casService.getThirdBindUserId(ticket, thirdPublicParam);
        if(thirdBindUserId != null){
            thirdBindUserId = thirdBindUserId.replace("\n","");
        }
        if(!StringUtils.isEmpty(userId)){
            if(!userId.equals(thirdBindUserId)){
                throw new RuntimeException("UserId Is Empty Or UserId inconsistent, currentUserId=" + userId);
            }
        }
        logger.error("【集成认证中心三方】thirdBindUserId={}", thirdBindUserId);
        // 获取YonBIP Code
        String yonBIPCode = casService.getThirdLoginCode(domainUrl + UrlConstant.THIRD_LOGIN_URL, yonBIPAccessToken, thirdUcId, thirdBindUserId);
        String encodedLoginURL = URLEncoder.encode(domainUrl + UrlConstant.LOGIN_URL + encodeRedirctUrl, "UTF-8");
        logger.error("【重定向后地址】yonBIPUrl={}", encodedLoginURL);
        //封装YonBIP单点登录URL
        String redirect_uri = String.format(domainUrl + UrlConstant.CAS_URL, thirdUcId, yonBIPCode, encodedLoginURL);
        logger.error("【OA待办】yonBIPUrl={},redirect_uri={}, thirdBindUserId={},getYonBIPAccessToken={},getYonBIPCode={}", domainUrl + UrlConstant.CAS_URL, redirect_uri, thirdBindUserId, yonBIPAccessToken, yonBIPCode);
        return redirect_uri;
    }

}
