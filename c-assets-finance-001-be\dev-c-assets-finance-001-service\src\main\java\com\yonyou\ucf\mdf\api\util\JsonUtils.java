package com.yonyou.ucf.mdf.api.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * JSON工具类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Slf4j
public class JsonUtils {

	private static final ObjectMapper objectMapper = new ObjectMapper();

	/**
	 * 将JSON字符串转换为指定类型的对象
	 *
	 * @param jsonStr JSON字符串
	 * @param clazz   目标类型
	 * @param <T>     泛型类型
	 * @return 转换后的对象
	 * @throws RuntimeException 转换异常
	 */
	public static <T> T parseObject(String jsonStr, Class<T> clazz) {
		try {
			return objectMapper.readValue(jsonStr, clazz);
		} catch (Exception e) {
			log.error("JSON转换异常，原始数据：{}", jsonStr, e);
			throw new RuntimeException("JSON转换异常", e);
		}
	}

	/**
	 * 将JSON字符串转换为指定类型的对象（支持泛型）
	 *
	 * @param jsonStr       JSON字符串
	 * @param typeReference 目标类型引用
	 * @param <T>           泛型类型
	 * @return 转换后的对象
	 * @throws RuntimeException 转换异常
	 */
	public static <T> T parseObject(String jsonStr, com.fasterxml.jackson.core.type.TypeReference<T> typeReference) {
		try {
			return objectMapper.readValue(jsonStr, typeReference);
		} catch (Exception e) {
			log.error("JSON转换异常，原始数据：{}", jsonStr, e);
			throw new RuntimeException("JSON转换异常", e);
		}
	}

	/**
	 * 将对象转换为JSON字符串
	 *
	 * @param object 要转换的对象
	 * @return JSON字符串
	 * @throws RuntimeException 转换异常
	 */
	public static String toJsonString(Object object) {
		try {
			return objectMapper.writeValueAsString(object);
		} catch (Exception e) {
			log.error("对象转JSON异常，对象：{}", object, e);
			throw new RuntimeException("对象转JSON异常", e);
		}
	}

	/**
	 * 判断字符串是否为有效的JSON格式
	 *
	 * @param jsonStr 要检查的字符串
	 * @return 是否为有效的JSON
	 */
	public static boolean isValidJson(String jsonStr) {
		try {
			JSON.parse(jsonStr);
			return true;
		} catch (Exception e) {
			return false;
		}
	}
}