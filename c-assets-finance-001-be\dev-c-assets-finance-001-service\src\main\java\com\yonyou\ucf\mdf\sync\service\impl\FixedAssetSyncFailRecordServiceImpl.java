package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.yonyou.diwork.ott.exexutors.RobotExecutors;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.sync.model.FixedAssetSyncFailRecord;
import com.yonyou.ucf.mdf.sync.service.FixedAssetSyncFailRecordService;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年6月13日
 */
@Slf4j
@Service
public class FixedAssetSyncFailRecordServiceImpl implements FixedAssetSyncFailRecordService {

	@Autowired
	private IBillCommonRepository billCommonRepository;

	@Autowired
	private IBillQueryRepository billQryRepository;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SuppressWarnings("unchecked")
	@Override
	public void saveFailRecord(FixedAssetSyncFailRecord failRecord) {
		RobotExecutors.runAs(tenantId, () -> {
			QuerySchema schema = QuerySchema.create();
			schema.addSelect("id,retryCount");
			schema.addCondition(
					QueryConditionGroup.and(QueryCondition.name("assetBillId").eq(failRecord.getAssetBillId())));
			List<FixedAssetSyncFailRecord> result = (List<FixedAssetSyncFailRecord>) billQryRepository
					.queryBySchema("cxkingdee-sync.cxkingdee-sync.FixedAssetSyncFailRecord", schema);
			if (CollectionUtil.isNotEmpty(result)) {
				FixedAssetSyncFailRecord oldRecord = result.get(0);
				failRecord.setId(oldRecord.getId());
				if (oldRecord.getRetryCount() != null) {
					failRecord.setRetryCount(oldRecord.getRetryCount() + 1);
				}
				failRecord.set_status(ActionEnum.UPDATE.getValueInt());
			}
			if (StringUtils.isNotBlank(failRecord.getErrMsg()) && failRecord.getErrMsg().length() >= 200) {
				failRecord.setErrMsg(failRecord.getErrMsg().substring(0, 180));
			}
			try {
				List<IBillDO> billDOs = Lists.newArrayList(failRecord);
				billCommonRepository.commonSaveBill(billDOs, "FixedAssetSyncFailRecord");
			} catch (Exception e) {
				log.error("保存资产同步失败记录报错！", e);
			}
		});
	}

}
