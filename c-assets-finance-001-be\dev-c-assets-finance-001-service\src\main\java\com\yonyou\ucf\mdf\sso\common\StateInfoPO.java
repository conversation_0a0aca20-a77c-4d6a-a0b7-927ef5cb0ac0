package com.yonyou.ucf.mdf.sso.common;

import java.io.Serializable;

/**
 * @className: StateInfoPO
 * @author: wjc
 * @date: 2025/5/20 10:34
 * @Version: 1.0
 * @description:
 */
public class StateInfoPO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 三方类型
     */
    private String type;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 集成认证中心编码
     */
    private String thirdUcId;
    /**
     * 待办URL
     */
    private String bizUrl;

    /**
     * 三方用户ID
     */
    private String userId;

    /**
     * 致远服务地址
     */
    private String seeyonUrl;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getThirdUcId() {
        return thirdUcId;
    }

    public void setThirdUcId(String thirdUcId) {
        this.thirdUcId = thirdUcId;
    }

    public String getBizUrl() {
        return bizUrl;
    }

    public void setBizUrl(String bizUrl) {
        this.bizUrl = bizUrl;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSeeyonUrl() {
        return seeyonUrl;
    }

    public void setSeeyonUrl(String seeyonUrl) {
        this.seeyonUrl = seeyonUrl;
    }
}
