package com.yonyou.ucf.mdf.kingdee.fixedasset.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.eas.enums.KingdeeEasServiceEnum;
import com.yonyou.ucf.mdf.kingdee.eas.service.KingdeeEasApiService;
import com.yonyou.ucf.mdf.kingdee.fixedasset.service.FixedAssetChangeService;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.CommonResponse;
import com.yonyou.ucf.mdf.kingdee.fixedasset.vo.FixedAssetChangeParam;
import com.yonyou.ucf.mdf.sync.enums.FixedAssetSyncTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 固定资产变更服务实现类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Service
public class FixedAssetChangeServiceImpl implements FixedAssetChangeService {

	@Autowired
	private KingdeeEasApiService kingdeeEasApiService;

	@Override
	public CommonResponse changeFixedAsset(FixedAssetChangeParam param) {
		try {
			log.info("开始变更固定资产，参数：{}", param);
			// 调用金蝶EAS接口推送数据
			String response = kingdeeEasApiService.callEasBaseApi(KingdeeEasServiceEnum.FIXED_ASSET,
					FixedAssetSyncTypeEnum.CHANGE.getEasMethodName(), param);
			log.info("变更固定资产完成，响应：{}", response);
			return JsonUtils.parseObject(response, CommonResponse.class);
		} catch (Exception e) {
			log.error("变更固定资产异常", e);
			throw new RuntimeException("变更固定资产失败：" + e.getMessage());
		}
	}
}