package com.yonyou.ucf.mdf.kingdee.vo;

import lombok.Data;

/**
 * 金蝶供应商VO
 * 
 * <AUTHOR>
 * @date 2025年5月27日
 */
@Data
public class KingdeeVendor {

    /**
     * 编码
     */
    private String number;

    /**
     * 名称
     */
    private String name;

    /**
     * 管理组织编码
     */
    private String adminCUNum;

    /**
     * 管理组织名称
     */
    private String adminCUName;

    /**
     * 供应商分类编码
     */
    private String supplierGroupNum;

    /**
     * 供应商分类名称
     */
    private String supplierGroupName;

    /**
     * 是否集团内公司
     */
    private String isInternalCompany;

    /**
     * 集团内公司编码
     */
    private String internalCompanyNum;

    /**
     * 集团内公司名称
     */
    private String internalCompanyName;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"number\":\"").append(number).append("\",");
        sb.append("\"name\":\"").append(name).append("\",");
        sb.append("\"adminCUNum\":\"").append(adminCUNum).append("\",");
        sb.append("\"adminCUName\":\"").append(adminCUName).append("\",");
        sb.append("\"supplierGroupNum\":\"").append(supplierGroupNum).append("\",");
        sb.append("\"supplierGroupName\":\"").append(supplierGroupName).append("\",");
        sb.append("\"isInternalCompany\":\"").append(isInternalCompany).append("\",");
        sb.append("\"internalCompanyNum\":\"").append(internalCompanyNum).append("\",");
        sb.append("\"internalCompanyName\":\"").append(internalCompanyName).append("\"");
        sb.append("}");
        return sb.toString();
    }
} 