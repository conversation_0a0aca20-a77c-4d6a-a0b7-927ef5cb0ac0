package com.yonyou.ucf.mdf.kingdee.eas.test;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

import javax.xml.namespace.QName;

import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.message.SOAPHeaderElement;

import com.yonyou.ucf.mdf.kingdee.eas.model.WSContext;

public class APIUtil {
	/** 日期格式化 yyyy-MM-dd */
	public static final DateFormat FORMAT_DAY = new SimpleDateFormat("yyyy-MM-dd");
	/** 日期格式化 yyyy-MM-dd HH:mm:ss */
	public static final DateFormat FORMAT_TIME = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	/** 日期格式化 yyyy-MM */
	public static final DateFormat FORMAT_MONTH = new SimpleDateFormat("yyyy-MM");
	/** 环境:测试 */
	public static final String Test = "Test";
	/** 环境:开发 */
	public static final String Code = "Code";
	/** 环境:测正式 */
	public static final String WORK = "Work";

	/**
	 * 执行接口操作
	 * 
	 * @param xmlStr xml字符串
	 * @param opName 操作名称
	 * @param env    操作环境类型
	 * @return
	 * @throws Exception
	 */
	public static String doOperation(String xmlStr, String opName, String env) throws Exception {
		Service s = new Service();
		Call call = (Call) s.createCall();
		call.setOperationName("login");
		if (Test.equals(env)) {
			// 测试环境地址
			call.setTargetEndpointAddress("http://**************:6888/ormrpc/services/EASLogin?wsdl");
		} else if (Code.equals(env)) {
			call.setTargetEndpointAddress("http://**************:6888/ormrpc/services/EASLogin?wsdl");
			// 开发环境地址
		} else if (WORK.equals(env)) {
			// 正式环境地址
		}
		call.setReturnType(new QName("urn:client", "WSContext"));
		call.setReturnClass(WSContext.class);
		call.setReturnQName(new QName("", "loginReturn"));
		// 超时
		call.setTimeout(Integer.valueOf(1000 * 600000 * 60));
		call.setMaintainSession(true);
		WSContext rs = null;
		if (Test.equals(env)) {
			// 测试环境地址 登录参数
			rs = (WSContext) call
					.invoke(new Object[] { "user", "Gzjc#3689743", "eas", "A99", "l2", Integer.valueOf(2) });
		} else if (Code.equals(env)) {
			// 开发环境地址 登录参数
			rs = (WSContext) call
					.invoke(new Object[] { "user", "Gzjc#3689743", "eas", "A99", "l2", Integer.valueOf(2) });
		} else if (WORK.equals(env)) {
			// 正式
		}

		if (rs.getSessionId() == null) {
			throw new Exception("login fail");
		}
		// 清理
		call.clearOperation();
		// 调用业务接口的操作名称
		call.setOperationName(opName);
		if (Test.equals(env)) {
			// 测试环境地址
			call.setTargetEndpointAddress("http://**************:6888/ormrpc/services/WSWSVoucher?wsdl");
		} else if (Code.equals(env)) {
			call.setTargetEndpointAddress("http://**************:6888/ormrpc/services/WSAirportYY?wsdl");
			// 开发环境地址
		} else if (WORK.equals(env)) {
			// 正式环境地址
		}
		call.setReturnQName(new QName("", "return"));
		call.setTimeout(Integer.valueOf(1000 * 600000 * 60));
		call.setMaintainSession(true);
		// 设置登录返回的session在soap头 "http://login.webservice.bos.kingdee.com"是固定的
		SOAPHeaderElement header = new SOAPHeaderElement("http://login.webservice.bos.kingdee.com", "SessionId",
				rs.getSessionId());
		call.addHeader(header);
		// 接口参数
		String aa = (String) call.invoke(new Object[] { xmlStr });
		return aa;
	}
}
