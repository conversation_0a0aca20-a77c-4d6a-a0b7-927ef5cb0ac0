package com.yonyou.ucf.mdf.sso.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.iuap.yms.api.IYmsJdbcApi;
import com.yonyou.iuap.yms.dao.BaseDAO;
import com.yonyou.iuap.yms.processor.MapProcessor;
import com.yonyou.ucf.mdf.sso.common.SSOConstant;
import com.yonyou.ucf.mdf.sso.service.IBimLoginService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * @className: BimLoginServiceImpl
 * @author: wjc
 * @date: 2025/5/12 11:02
 * @Version: 1.0
 * @description:
 */
@Service
public class BimLoginServiceImpl implements IBimLoginService {

    private static final Logger logger = LoggerFactory.getLogger(BimLoginServiceImpl.class);

    @Value("${third.bim.clientId}")
    private String bimClientId;
    @Value("${third.bim.clientSecret}")
    private String bimClientSecret;
    @Value("${domain.url:}")
    private String domainUrl;
    @Value("${app.tenantId:}")
    private String bipTenantId;
    @Value("${third.bim.domain}")
    private String bimDomain;

    @Resource(name = "baseDAO", type = BaseDAO.class)
    private IYmsJdbcApi ymsJdbcApi;

    @Override
    public String getRedirectUrl(HttpServletRequest request, HttpServletResponse response) {
        String code = request.getParameter("code");
        String redirectUrl = null;
        try {
            //获取token信息
            Map<String, Object> params = new HashMap<>();
            params.put("client_id", bimClientId);
            params.put("grant_type", "authorization_code");
            params.put("code", code);
            params.put("client_secret", bimClientSecret);
            String tokenRes = HttpUtil.post(bimDomain+SSOConstant.SSO_THIRD_ACCESS_TOKEN_URL,params).replace("\"","");
            if(tokenRes == null || "".equals(tokenRes)){
                throw new RuntimeException("[获取Token]返回为空,请求参数："+params.toString());
            }
            //垃圾统一认证，都不返回标准的JSON，需要特殊处理一下
            // 修复格式：添加双引号
            String fixedJson = tokenRes
                    .replaceAll(":", "\":\"") // 为所有单词添加引号
                    .replaceAll(",", "\",\"") // 为所有单词添加引号
                    .replace("{", "{\"")        // 修复开头
                    .replace("}", "\"}");          // 修复结尾
            // 解析为JSON
            ObjectMapper mapper = new ObjectMapper();
            JsonNode access = mapper.readTree(fixedJson);
            if(access.get("access_token") == null){
                throw new RuntimeException("[获取Token]未获取到access_token,返回："+tokenRes);
            }
            String access_token = access.get("access_token").asText();
            String uid = access.get("uid").asText();
            if(access_token == null || "".equals(access_token)){
                throw new RuntimeException("[获取Token]失败,返回："+tokenRes);
            }
            //通过token获取用户信息
            if(uid == null || "".equals(uid)){
                params = new HashMap<>();
                params.put("access_token", access_token);
                params.put("client_id", bimClientId);
                String userRes = HttpUtil.get(bimDomain+SSOConstant.SSO_THIRD_GETUSER_URL,params).replace("\"","");
                if(userRes == null || "".equals(userRes)){
                    throw new RuntimeException("[获取认证用户]返回为空,请求参数："+params.toString());
                }
                userRes = userRes
                        .replaceAll(":", "\":\"") // 为所有单词添加引号
                        .replaceAll(",", "\",\"") // 为所有单词添加引号
                        .replace("{", "{\"")        // 修复开头
                        .replace("}", "\"}");          // 修复结尾
                JSONObject user = JSONObject.parseObject(userRes);
                uid = (String) user.get("uid");
                if(uid == null || "".equals(uid)){
                    throw new RuntimeException("[获取认证用户]返回uid为空,返回："+userRes);
                }
            }

            //通过三方用户信息转换bip用户信息
            StringBuffer buff = new StringBuffer();
            buff.append("select r.user_id from iuap_apcom_auth.ba_user r");
            buff.append(" left join iuap_apdoc_basedoc.bd_staff s on r.user_id = s.user_id");
            buff.append(" where s.dr='0'");
            buff.append(" and r.stopstatus ='0' and r.enable ='1'");
            buff.append(" and s.objid='").append(uid).append("'");
            Map<String,String> bipUser = (Map<String,String>) ymsJdbcApi.queryForObject(buff.toString(),new MapProcessor());
            if (StringUtils.isEmpty(bipUser.get("user_id"))) {
                throw new RuntimeException("未获取到有效的["+uid+"]Bip用户");
            }
            String userId = bipUser.get("user_id");
            //通过传入userId生成免密登录token
            String tokenURL = domainUrl + SSOConstant.BIP_OA_CAS_TOKENPATH + "?userId=" + userId;
            String tokenStr = HttpUtil.get(tokenURL);
            JSONObject tokenObj = JSONObject.parseObject(tokenStr);
            String bip_token = (String) tokenObj.get("token");

            //目标节点URL-工作台
            String securityTmp = domainUrl + "/login?service=" + URLEncoder.encode(domainUrl, "UTF-8") + "&tenantId=" + bipTenantId;
            //调用业务中台友户通接口，使用获取到的 TOKEN 进行校验登录并跳转
            String security = URLEncoder.encode(securityTmp, "UTF-8");

            redirectUrl = domainUrl + SSOConstant.BIP_OA_CAS_LOGINPATH + "?token=" + bip_token + "&service=" + security; //cas
        } catch (Exception e) {
            logger.error("统一认证单点异常 : {}",e);
            throw new RuntimeException(e);
        }

        return redirectUrl;
    }
}
