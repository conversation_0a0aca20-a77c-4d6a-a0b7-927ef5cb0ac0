package com.yonyou.ucf.mdf.kingdee.vo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金蝶客户实体类
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
@EqualsAndHashCode
@JsonInclude(Include.NON_NULL)
public class KingdeeCustomer {

	/**
	 * 是否集团内公司
	 */
	private String isInternalCompany;

	/**
	 * 管理组织编码
	 */
	private String adminCUNum;

	/**
	 * 管理组织名称
	 */
	private String adminCUName;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 编码
	 */
	private String number;

	/**
	 * 客户分类
	 */
	private List<KingdeeCustomerGroup> customerGroups;

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("{");
		sb.append("\"isInternalCompany\":\"").append(isInternalCompany).append("\",");
		sb.append("\"adminCUNum\":\"").append(adminCUNum).append("\",");
		sb.append("\"adminCUName\":\"").append(adminCUName).append("\",");
		sb.append("\"name\":\"").append(name).append("\",");
		sb.append("\"number\":\"").append(number).append("\",");
		sb.append("\"customerGroups\":").append(customerGroups);
		sb.append("}");
		return sb.toString();
	}
}