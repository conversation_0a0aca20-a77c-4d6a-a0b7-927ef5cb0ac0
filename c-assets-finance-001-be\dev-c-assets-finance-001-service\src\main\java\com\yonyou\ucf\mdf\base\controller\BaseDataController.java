package com.yonyou.ucf.mdf.base.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.banboocloud.Codec.BamboocloudFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yonyou.iuap.yms.api.IYmsJdbcApi;
import com.yonyou.iuap.yms.dao.BaseDAO;
import com.yonyou.iuap.yms.processor.ColumnProcessor;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.iuap.yms.processor.MapProcessor;
import com.yonyou.ucf.mdf.api.common.BatchResult;
import com.yonyou.ucf.mdf.api.common.BatchResultAI;
import com.yonyou.ucf.mdf.api.common.ResponseResult;
import com.yonyou.ucf.mdf.api.util.BamboocloudUtils;
import com.yonyou.ucf.mdf.base.service.BaseDataService;
import com.yonyou.ucf.mdf.base.service.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @className: 统一认证平台-5数据同步集成规范
 * @author: wjc
 * @date: 2025/4/27 11:12
 * @Version: 1.0
 * @description:
 */
@RestController
@RequestMapping("/api/bim")
public class BaseDataController {

    private static final Logger logger = LoggerFactory.getLogger(BaseDataController.class);
    private static final ObjectMapper mapper = new ObjectMapper();

    private static final String type = "SM4";
    private static final String signType = "SM3";

    @Value("${third.bim.localDebug}")
    private String bimLocalDebug;
    @Value("${third.bim.key}")
    private String bimKey;
    @Value("${third.bim.remoteUser}")
    private String bimRemoteUser;
    @Value("${third.bim.remotePwd}")
    private String bimRemotePwd;
    @Value("${third.bim.open}")
    private String bimOpen;
    @Value("${app.tenantId}")
    private String tenantId;
    @Autowired
    private BaseDataService dataService;
    @Resource(name = "baseDAO", type = BaseDAO.class)
    private IYmsJdbcApi ymsJdbcApi;

    /**
     * 对象属性字段查询
     * @param
     * @return
     */
    @PostMapping("/SchemaService")
    public void schemaService(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        String message = "success";
        String status = "500";
        String bimRequestId = "";
        try {
            String bodyparam = BamboocloudUtils.getRequestBody(req);
            logger.error("[对象属性字段查询]first--param-->" + bodyparam);
            //报文解密
            if(!"Y".equals(bimLocalDebug)){
                bodyparam = BamboocloudUtils.getPlaintext(bodyparam, bimKey, type);
            }
            logger.error("[对象属性字段查询]json--param-->" + bodyparam);
            Map<String,Object> reqmap = (Map<String,Object>) JSON.parse(bodyparam);
            bimRequestId = (String) reqmap.get("bimRequestId");
            //验证签名
            Boolean verify = BamboocloudUtils.verify(reqmap, signType).booleanValue();
            if("Y".equals(bimLocalDebug)){
                verify = true;
            }
            if (verify) {
                String username = (String) reqmap.get("bimRemoteUser");
                String password = (String) reqmap.get("bimRemotePwd");
                boolean check = BamboocloudUtils.checkUsernamePassword(username, password,bimRemoteUser,bimRemotePwd);
                if(!check){
                    message = "[对象属性字段查询]账号密码不匹配";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                Map<String,Object> schema = new HashMap<String,Object>();
                List<AttributeEntity> orgs = dataService.getSchemeAttribute("organization");
                List<AttributeEntity> accs = dataService.getSchemeAttribute("account");
                schema.put("account", accs);
                schema.put("organization", orgs);
                schema.put("bimRequestId", bimRequestId);
                String mapJson = JSON.toJSONString(schema);
                logger.error("[对象属性字段查询]response---json-->" + mapJson);
                if(!"Y".equals(bimLocalDebug)){
                    mapJson = BamboocloudFacade.encrypt(mapJson, bimKey, type);
                }
                resp.setCharacterEncoding("UTF-8");
                PrintWriter out = resp.getWriter();
                out.write(mapJson);
                out.close();
            }else{
                message = "[对象属性字段查询]验证签名失败";
                callBack(bimRequestId, status, message, resp,null);
            }
        }catch (Exception e){
            logger.error("[对象属性字段查询]异常:{}",e.getMessage());
            message = "[对象属性字段查询]异常:"+e.getMessage();
            callBack(bimRequestId, status, message, resp,null);
        }
    }

    /**
     * 组织机构创建
     * @param
     * @return
     */
    @PostMapping("/OrgCreateService")
    public void orgCreateService(HttpServletRequest req, HttpServletResponse resp) throws IOException{
        String message = "success";
        String status = "500";
        String uid = "";
        String bimRequestId = "";
        try {
            if("N".equals(bimOpen)){
                message = "未开启数据同步";
                callBack(bimRequestId, status, message, resp,null);
                return;
            }
            String bodyparam = BamboocloudUtils.getRequestBody(req);
            logger.error("[组织机构创建]first--param-->" + bodyparam);
            //报文解密
            if(!"Y".equals(bimLocalDebug)){
                bodyparam = BamboocloudUtils.getPlaintext(bodyparam, bimKey, type);
            }
            logger.error("[组织机构创建]json--param-->" + bodyparam);
            Map<String,Object> reqmap = (Map<String,Object>) JSON.parse(bodyparam);
            bimRequestId = (String) reqmap.get("bimRequestId");

            //验证签名
            Boolean verify = BamboocloudUtils.verify(reqmap, signType).booleanValue();
            if("Y".equals(bimLocalDebug)){
                verify = true;
            }
            if (verify) {
                String username = (String) reqmap.get("bimRemoteUser");
                String password = (String) reqmap.get("bimRemotePwd");
                boolean check = BamboocloudUtils.checkUsernamePassword(username, password,bimRemoteUser,bimRemotePwd);
                if(!check){
                    message = "[组织机构创建]账号密码不匹配";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                String objId = (String) reqmap.get("objid");
                String code = (String) reqmap.get("code");
                StringBuffer buff = new StringBuffer();
                buff.append("select id from iuap_apdoc_basedoc.org_orgs");
                buff.append(" where dr ='0'");
                buff.append(" and objid='").append(objId).append("'");
                buff.append(" and code='").append(code).append("'");
                Object orgid = ymsJdbcApi.queryForObject(buff.toString(), new ColumnProcessor());
                if(orgid != null && !"".equals(orgid)){
                    message = "[组织机构创建]已存在！";
                    status = "0";
                    callBack(bimRequestId, status, message, resp, (String) orgid);
                    return;
                }
                String pobjid = (String) reqmap.get("parentCode");
                buff = new StringBuffer();
                buff.append("select code from iuap_apdoc_basedoc.org_orgs");
                buff.append(" where dr ='0'");
                buff.append(" and objid='").append(pobjid).append("'");
                String parentCode = (String) ymsJdbcApi.queryForObject(buff.toString(), new ColumnProcessor());
                //UN-组织；UM-部门
                if("UN".equals(reqmap.get("orgtype"))){
                    BaseOrg vo = new BaseOrg();
                    vo.setCode((String) reqmap.get("code"));
                    MultiLang name = new MultiLang();
                    name.setZh_CN((String) reqmap.get("name"));
                    vo.setName(name);
                    MultiLang shortname = new MultiLang();
                    shortname.setZh_CN((String) reqmap.get("shortname"));
                    vo.setShortname(shortname);
                    vo.setParent_code(parentCode);
                    vo.set_status("Insert");
                    vo.setCompanytype_code("company");
                    vo.setExchangerate(tenantId);
                    vo.setEnable("1");
                    vo.setOrgtype("1");
                    vo.setObjid(objId);//外部系统id
                    //会计主体
                    BaseOrg.FinanceOrg fiOrg = new BaseOrg.FinanceOrg();
                    fiOrg.setParentid_code(vo.getParent_code());
                    fiOrg.setCurrency("2147545152328040459");
                    fiOrg.setPeriodschema("2147545169506861062");
                    fiOrg.setIsexternalaccounting("1");
                    fiOrg.setEnable("1");
                    vo.setFinanceOrg(fiOrg);
                    //行政组织
                    BaseOrg.AdminOrg adOrg = new BaseOrg.AdminOrg();
                    adOrg.setEnable("1");
                    vo.setAdminOrg(adOrg);
                    //库存组织
                    BaseOrg.CommonOrg itOrg = new BaseOrg.CommonOrg();
                    itOrg.setEnable("1");
                    vo.setInventoryOrg(itOrg);
                    //资金组织
                    BaseOrg.FundsOrg fdOrg = new BaseOrg.FundsOrg();
                    fdOrg.setEnable(1);
                    vo.setFundsOrg(fdOrg);
                    List<BaseOrg> List = new ArrayList<>();
                    List.add(vo);
                    ResponseResult<BatchResult> result = dataService.orgBatchSave(List);
                    if (!result.isSuccess2()) {
                        message = "[组织机构创建]业务单元处理失败:"+result.getMessage();
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    List<Object> rtList = result.getData().getInfos();
                    if(rtList == null || rtList.size() == 0){
                        List<Object> msgs = result.getData().getMessages();
                        message = "[组织机构创建]业务单元保存失败:"+msgs.get(0);
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    Map<String,Object> info = (Map<String,Object>) result.getData().getInfos().get(0);
                    uid = (String) info.get("id");
                }else{
                    if(parentCode == null || "".equals(parentCode)){
                        message = "[组织机构创建]外系统id:"+pobjid+",在系统中不存在";
                        status = "0";
                        callBack(bimRequestId, status, message, resp, (String) orgid);
                        return;
                    }
                    AdminOrgVO vo = new AdminOrgVO();
                    vo.setCode((String) reqmap.get("code"));
                    MultiLang name = new MultiLang();
                    name.setZh_CN((String) reqmap.get("name"));
                    vo.setName(name);

                    vo.setParent_code(parentCode);
                    vo.setBranchleader_code((String) reqmap.get("branchleader_code"));//分管领导
                    vo.setPrincipal_code((String) reqmap.get("principal_code"));//部门负责人
                    vo.setEnable(1);
                    vo.set_status("Insert");
                    vo.setObjid(objId);//外部系统id

                    List<AdminOrgVO> List = new ArrayList<>();
                    List.add(vo);
                    ResponseResult<BatchResultAI> result = dataService.deptBatchSave(List);
                    if (!result.isSuccess2()) {
                        message = "[组织机构创建]部门处理失败:"+result.getMessage();
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    List<Object> rtList = result.getData().getInfos();
                    if(rtList == null || rtList.size() == 0){
                        List<Object> msgs = result.getData().getMessages();
                        message = "[组织机构创建]部门保存失败:"+msgs.get(0);
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    Map<String,Object> info = (Map<String,Object>) result.getData().getInfos().get(0);
                    uid = (String) info.get("id");
                }
                status = "0";
                callBack(bimRequestId, status, message, resp,uid);
            }else{
                message = "[组织机构创建]验证签名失败";
                callBack(bimRequestId, status, message, resp,null);
            }
        }catch (Exception e){
            logger.error("[组织机构创建]异常:{}",e.getMessage());
            message = "[组织机构创建]异常:"+e.getMessage();
            callBack(bimRequestId, status, message, resp,uid);
        }
    }

    /**
     * 组织机构修改
     * @param
     * @return
     */
    @PostMapping("/OrgUpdateService")
    public void orgUpdateService(HttpServletRequest req, HttpServletResponse resp) throws IOException{
        String message = "success";
        String status = "500";
        String bimRequestId = "";
        try {
            if("N".equals(bimOpen)){
                message = "未开启数据同步";
                callBack(bimRequestId, status, message, resp,null);
                return;
            }
            String bodyparam = BamboocloudUtils.getRequestBody(req);
            logger.error("[组织机构修改]first--param-->" + bodyparam);
            //报文解密
            if(!"Y".equals(bimLocalDebug)){
                bodyparam = BamboocloudUtils.getPlaintext(bodyparam, bimKey, type);
            }
            logger.error("[组织机构修改]json--param-->" + bodyparam);
            Map<String,Object> reqmap = (Map<String,Object>) JSON.parse(bodyparam);
            bimRequestId = (String) reqmap.get("bimRequestId");
            //验证签名
            Boolean verify = BamboocloudUtils.verify(reqmap, signType).booleanValue();
            if("Y".equals(bimLocalDebug)){
                verify = true;
            }
            if (verify) {
                String username = (String) reqmap.get("bimRemoteUser");
                String password = (String) reqmap.get("bimRemotePwd");
                boolean check = BamboocloudUtils.checkUsernamePassword(username, password,bimRemoteUser,bimRemotePwd);
                if(!check){
                    message = "[组织机构修改]账号密码不匹配";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                String id = (String) reqmap.get("bimOrgId");
                String objId = null;
                StringBuffer buff = new StringBuffer();
                buff.append("select objid from iuap_apdoc_basedoc.org_orgs");
                buff.append(" where dr ='0'");
                buff.append(" and id='").append(id).append("'");
                objId = (String) ymsJdbcApi.queryForObject(buff.toString(), new ColumnProcessor());
                if(objId == null || "".equals(objId)){
                    message = "[组织机构修改]bimOrgId:"+id+",在BIP系统中未找到相应绑定的机构信息";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                String parentCode = (String) reqmap.get("parentCode");
                buff = new StringBuffer();
                buff.append("select code from iuap_apdoc_basedoc.org_orgs");
                buff.append(" where dr ='0'");
                buff.append(" and objid='").append(parentCode).append("'");
                parentCode = (String) ymsJdbcApi.queryForObject(buff.toString(), new ColumnProcessor());
                String orgtype = (String) reqmap.get("orgtype");//1-组织；2-部门
                if("UN".equals(orgtype)){
                    buff = new StringBuffer();
                    buff.append("select * from iuap_apdoc_basedoc.org_orgs");
                    buff.append(" where dr ='0'");
                    buff.append(" and id='").append(id).append("'");
                    Map<String, Object> oldorg = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                    if(oldorg == null || oldorg.get("id") == null){
                        message = "[组织机构修改]处理失败:通过id["+id+"]未获取到业务单元信息!";
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    Map<String, Object> org = new HashMap<>();
                    org.put("id",oldorg.get("id"));
                    org.put("code",oldorg.get("code"));
                    MultiLang name = new MultiLang();
                    if(reqmap.get("name") != null){
                        name.setZh_CN((String) reqmap.get("name"));
                    }else{
                        name.setZh_CN((String) oldorg.get("name"));
                    }
                    org.put("name",name);
                    if(reqmap.get("shortname") != null){
                        MultiLang shortname = new MultiLang();
                        shortname.setZh_CN((String) reqmap.get("shortname"));
                        org.put("shortname",shortname);
                    }
                    org.put("_status","Update");
                    org.put("effectivedate",null);
                    org.put("modifiedtime",null);
                    org.put("pubts",null);
                    org.put("creationtime",null);
                    org.put("ts",null);
                    if(parentCode != null && !"".equals(parentCode)){
                        org.put("parent_code",parentCode);
                    }
                    org.put("objid",objId);
                    ResponseResult<BatchResult> result = dataService.orgBatchUpdate(org);
                    if (!result.isSuccess2()) {
                        message = "[组织机构修改]业务单元处理失败:"+result.getMessage();
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    List<Object> rtList = result.getData().getInfos();
                    if(rtList == null || rtList.size() == 0){
                        List<Object> msgs = result.getData().getMessages();
                        message = "[组织机构修改]业务单元更新失败:"+msgs.get(0);
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                }else{
                    buff = new StringBuffer();
                    buff.append("select * from iuap_apdoc_basedoc.org_admin");
                    buff.append(" where dr ='0'");
                    buff.append(" and id='").append(id).append("'");
                    Map<String, Object> olddept = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                    if(olddept == null || olddept.get("id") == null){
                        message = "[组织机构修改]处理失败:通过id["+id+"]未获取到部门信息!";
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    Map<String, Object> dept = new HashMap<>();
                    dept.put("id",olddept.get("id"));
                    dept.put("code",olddept.get("code"));
                    MultiLang name = new MultiLang();
                    if(reqmap.get("name") != null){
                        name.setZh_CN((String) reqmap.get("name"));
                    }else{
                        name.setZh_CN((String) olddept.get("name"));
                    }
                    dept.put("name",name);
                    dept.put("_status","Update");
                    dept.put("effectivedate",null);
                    dept.put("modifiedtime",null);
                    dept.put("pubts",null);
                    dept.put("creationtime",null);
                    dept.put("ts",null);
                    if(parentCode != null && !"".equals(parentCode)){
                        dept.put("parent_code",parentCode);
                    }else{
                        dept.put("parent",olddept.get("parentid"));
                    }
                    dept.put("objid",objId);
                    ResponseResult<BatchResultAI> result = dataService.deptBatchUpdate(dept);
                    if (!result.isSuccess2()) {
                        message = "[组织机构修改]部门处理失败:"+result.getMessage();
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    List<Object> rtList = result.getData().getInfos();
                    if(rtList == null || rtList.size() == 0){
                        List<Object> msgs = result.getData().getMessages();
                        message = "[组织机构修改]部门更新失败:"+msgs.get(0);
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                }
                status = "0";
                callBack(bimRequestId, status, message, resp,null);
            }else{
                message = "[组织机构修改]验证签名失败";
                callBack(bimRequestId, status, message, resp,null);
            }
        }catch (Exception e){
            logger.error("[组织机构修改]异常:{}",e.getMessage());
            message = "[组织机构修改]异常:"+e.getMessage();
            callBack(bimRequestId, status, message, resp,null);
        }
    }

    /**
     * 账号(员工)创建
     * @param
     * @return
     */
    @PostMapping("/UserCreateService")
    public void userCreateService(HttpServletRequest req, HttpServletResponse resp) throws IOException{
        String message = "success";
        String status = "500";
        String bimRequestId = "";
        String uid = "";
        try {
            if("N".equals(bimOpen)){
                message = "未开启数据同步";
                callBack(bimRequestId, status, message, resp,null);
                return;
            }
            String bodyparam = BamboocloudUtils.getRequestBody(req);
            logger.error("[账号(员工)创建]first--param-->" + bodyparam);
            //报文解密
            if(!"Y".equals(bimLocalDebug)){
                bodyparam = BamboocloudUtils.getPlaintext(bodyparam, bimKey, type);
            }
            logger.error("[账号(员工)创建]json--param-->" + bodyparam);
            Map<String,Object> reqmap = (Map<String,Object>) JSON.parse(bodyparam);
            bimRequestId = (String) reqmap.get("bimRequestId");
            //验证签名
            Boolean verify = BamboocloudUtils.verify(reqmap, signType).booleanValue();
            if("Y".equals(bimLocalDebug)){
                verify = true;
            }
            if (verify) {
                String username = (String) reqmap.get("bimRemoteUser");
                String password = (String) reqmap.get("bimRemotePwd");
                boolean check = BamboocloudUtils.checkUsernamePassword(username, password,bimRemoteUser,bimRemotePwd);
                if(!check){
                    message = "[账号(员工)创建]账号密码不匹配";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                StringBuffer buff = new StringBuffer();
                buff.append("select id,objid from iuap_apdoc_basedoc.bd_staff");
                buff.append(" where code='").append(reqmap.get("code")).append("'");
                Map<String, Object> usermap = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                if(usermap != null && usermap.get("id") != null && !"".equals(usermap.get("id"))){
                    if(usermap.get("objid") == null || "".equals(usermap.get("objid"))){
                        buff = new StringBuffer();
                        buff.append("update iuap_apdoc_basedoc.bd_staff");
                        buff.append(" set objid='").append(reqmap.get("objid")).append("'");
                        buff.append(" where id='").append(usermap.get("id")).append("'");
                        ymsJdbcApi.update(buff.toString());
                        ymsJdbcApi.update("commit");
                    }
                    message = "[账号(员工)创建]已存在！";
                    status = "0";
                    callBack(bimRequestId, status, message, resp, (String) usermap.get("id"));
                    return;
                }
                Staff vo = new Staff();
                vo.setCode((String) reqmap.get("code"));
                vo.setName((String) reqmap.get("name"));
                if((reqmap.get("email") == null || "".equals(reqmap.get("email")))
                        && (reqmap.get("mobile") == null || "".equals(reqmap.get("mobile")))){
                    message = "[账号(员工)创建]手机号和邮箱不能同时为空";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                vo.setEmail((String) reqmap.get("email"));
                vo.setMobile((String) reqmap.get("mobile"));
                if(reqmap.get("certNo") != null && !"".equals(reqmap.get("certNo"))){
                    vo.setCert_type("0001-5130-48de-ae28-4233a47e3797");//默认身份证
                    vo.setCert_no((String) reqmap.get("certNo"));//身份证号
                }

                buff = new StringBuffer();
                buff.append("select a.id deptid,a.parentorgid orgid from iuap_apdoc_basedoc.org_admin a");
                buff.append(" left join iuap_apdoc_basedoc.org_orgs b on b.id=a.parentorgid");
                buff.append(" where a.dr='0' and b.dr ='0'");
                buff.append(" and a.objid='").append(reqmap.get("deptCode")).append("'");
                Map<String, Object> dept = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                if(dept == null || dept.get("deptid") == null){
                    message = "[账号(员工)创建]通过部门id未获取到部门信息！";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                String begindate = (String)reqmap.get("updateTime");
                if(begindate != null && begindate.length() > 19){
                    begindate = begindate.substring(0,19);
                }
                Staff.StaffJob job = new Staff.StaffJob();
                job.setBegindate(begindate);
                job.setDept_id((String)dept.get("deptid"));
                job.setOrg_id((String)dept.get("orgid"));
                job.set_status("Insert");

                List<Staff.StaffJob> staffJobList = new ArrayList<>();
                staffJobList.add(job);
                vo.setMainJobList(staffJobList);

                vo.set_status("Insert");

                List<Staff> List = new ArrayList<>();
                List.add(vo);
                ResponseResult<BatchResult> result = dataService.staffBatchSave(List);
                if (!result.isSuccess2()) {
                    message = "[账号(员工)创建]处理失败:"+result.getMessage();
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                List<Object> rtList = result.getData().getInfos();
                if(rtList == null || rtList.size() == 0){
                    List<Object> msgs = result.getData().getMessages();
                    message = "[账号(员工)创建]业务单元保存失败:"+msgs.get(0);
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                Map<String,Object> info = (Map<String,Object>) result.getData().getInfos().get(0);
                uid = (String) info.get("id");
                vo = new Staff();
                vo.setId(uid);
                vo.setEnable(0);
                ResponseResult<JSONObject> resultUnstop = dataService.staffUnstop(vo);
                if (!resultUnstop.isSuccess2()) {
                    message = "[账号(员工)创建]启用用户处理失败:"+resultUnstop.getMessage();
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                status = "0";
                callBack(bimRequestId, status, message, resp,uid);
            }else{
                message = "[账号(员工)创建]验证签名失败";
                callBack(bimRequestId, status, message, resp,null);
            }
        }catch (Exception e){
            logger.error("[账号(员工)创建]异常:{}",e.getMessage());
            message = "[账号(员工)创建]异常:"+e.getMessage();
            callBack(bimRequestId, status, message, resp,uid);
        }
    }

    /**
     * 账号修改
     * @param
     * @return
     */
    @PostMapping("/UserUpdateService")
    public void userUpdateService(HttpServletRequest req, HttpServletResponse resp) throws IOException{
        String message = "success";
        String status = "500";
        String bimRequestId = "";
        try {
            if("N".equals(bimOpen)){
                message = "未开启数据同步";
                callBack(bimRequestId, status, message, resp,null);
                return;
            }
            String bodyparam = BamboocloudUtils.getRequestBody(req);
            logger.error("[账号(员工)修改]first--param-->" + bodyparam);
            //报文解密
            if(!"Y".equals(bimLocalDebug)){
                bodyparam = BamboocloudUtils.getPlaintext(bodyparam, bimKey, type);
            }
            logger.error("[账号(员工)修改]json--param-->" + bodyparam);
            Map<String,Object> reqmap = (Map<String,Object>) JSON.parse(bodyparam);
            bimRequestId = (String) reqmap.get("bimRequestId");
            //验证签名
            Boolean verify = BamboocloudUtils.verify(reqmap, signType).booleanValue();
            if("Y".equals(bimLocalDebug)){
                verify = true;
            }
            if (verify) {
                String username = (String) reqmap.get("bimRemoteUser");
                String password = (String) reqmap.get("bimRemotePwd");
                boolean check = BamboocloudUtils.checkUsernamePassword(username, password,bimRemoteUser,bimRemotePwd);
                if(!check){
                    message = "[账号(员工)修改]账号密码不匹配";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                String id = (String) reqmap.get("bimUid");//bip员工id
                StringBuffer buff = new StringBuffer();
                buff.append("select * from iuap_apdoc_basedoc.bd_staff");
                buff.append(" where dr ='0'");
                buff.append(" and id='").append(id).append("'");
                Map<String, Object> staff = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                if(staff == null || staff.get("id") == null){
                    message = "[账号(员工)修改]处理失败:通过id["+id+"]未获取到员工信息!";
                    callBack(bimRequestId, status, message, resp,null);
                    return;
                }
                Boolean enable = (Boolean) reqmap.get("__ENABLE__");
                if(enable != null){//启停
                    Staff vo = new Staff();
                    vo.setId(id);
                    vo.setEnable(Integer.parseInt(staff.get("enable") == null?"0":""+staff.get("enable")));
                    ResponseResult<JSONObject> result = null;
                    if(enable){
                        result = dataService.staffUnstop(vo);
                    }else{
                        result = dataService.staffStop(vo);
                    }
                    if (!result.isSuccess2()) {
                        message = "[账号(员工)修改]启停处理失败:"+result.getMessage();
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                }else{//修改
                    String begindate = (String) reqmap.get("updateTime");
                    if(begindate == null || "".equals(begindate)){
                        message = "[账号(员工)修改]更新时间为空";
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    if(begindate != null && begindate.length() > 19){
                        begindate = begindate.substring(0,19);
                    }
                    boolean isnewjob = false;//是否新的部门判断是否需要生成新的记录
                    Map<String, Object> dept = null;
                    String deptCode = (String) reqmap.get("deptCode");
                    if(deptCode != null && !"".equals(deptCode)){
                        buff = new StringBuffer();
                        buff.append("select a.id deptid,a.parentorgid orgid from iuap_apdoc_basedoc.org_admin a");
                        buff.append(" left join iuap_apdoc_basedoc.org_orgs b on b.id=a.parentorgid");
                        buff.append(" where a.dr='0' and b.dr ='0'");
                        buff.append(" and a.objid='").append(deptCode).append("'");
                        dept = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                        if(dept == null || dept.get("deptid") == null){
                            message = "[账号(员工)创建]通过部门编码未获取到部门信息！";
                            callBack(bimRequestId, status, message, resp,null);
                            return;
                        }
                        buff = new StringBuffer();
                        buff.append("select a.id from iuap_apdoc_basedoc.bd_staff_mainjob a");
                        buff.append(" left join iuap_apdoc_basedoc.org_admin b on a.dept_id=b.id");
                        buff.append(" where a.dr ='0' and a.lastestjob='1'");
                        buff.append(" and a.staff_id='").append(id).append("'");
                        buff.append(" and b.objid='").append(deptCode).append("'");
                        Object jobid = ymsJdbcApi.queryForObject(buff.toString(), new ColumnProcessor());
                        if(jobid == null || "".equals(jobid)){
                            isnewjob = true;
                        }
                    }else{
                        buff = new StringBuffer();
                        buff.append("select dept_id deptid,org_id orgid from iuap_apdoc_basedoc.bd_staff_mainjob");
                        buff.append(" where dr ='0' and lastestjob='1'");
                        buff.append(" and staff_id='").append(id).append("'");
                        dept = (Map<String, Object>) ymsJdbcApi.queryForObject(buff.toString(), new MapProcessor());
                        if(dept == null || dept.get("deptid") == null){
                            message = "[账号(员工)创建]通过bimUid未获取到任职信息！";
                            callBack(bimRequestId, status, message, resp,null);
                            return;
                        }
                    }
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 定义你想要的日期时间格式
                    Map<String, Object> mainJob = new HashMap<>();
                    buff = new StringBuffer();
                    buff.append("select * from iuap_apdoc_basedoc.bd_staff_mainjob");
                    buff.append(" where dr ='0'");
                    buff.append(" and staff_id='").append(id).append("'");
                    List<Map<String, Object>> mainJobList = (List<Map<String, Object>>) ymsJdbcApi.queryForList(buff.toString(), new MapListProcessor());
                    if(mainJobList == null || mainJobList.size() == 0){
                        mainJobList = new ArrayList<>();
                        mainJob.put("dept_id",dept.get("deptid"));
                        mainJob.put("org_id",dept.get("orgid"));
                        mainJob.put("begindate",begindate);
                        mainJob.put("_status","Insert");
                        mainJobList.add(mainJob);
                    }else if(isnewjob){
                        //更新，把历史的主职信息截止为更新时间的前一天，同时生成一条新的任职，开始时间为更新时间
                        for (Map<String, Object> map : mainJobList) {
                            Timestamp bdate = (Timestamp) map.get("begindate");
                            if(map.get("lastestjob") != null && Integer.parseInt(""+map.get("lastestjob")) == 1){
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                LocalDate date = LocalDate.parse(begindate.substring(0,10), formatter);
                                // 获取上一天的日期
                                LocalDate previousDay = date.minus(1, ChronoUnit.DAYS);
                                LocalDate bdateDay = LocalDate.parse(sdf.format(bdate).substring(0,10), formatter);
                                if(previousDay.isBefore(bdateDay)){
                                    map.put("enddate",sdf.format(bdate));
                                }else{
                                    // 将日期格式化回字符串
                                    map.put("enddate",previousDay.format(formatter));
                                }
                                map.put("enable",2);
                                map.put("lastestjob",0);
                            }else{
                                if(map.get("enddate") != null){
                                    Timestamp edate = (Timestamp) map.get("enddate");
                                    map.put("enddate",sdf.format(edate));
                                }
                            }
                            map.put("_status","Update");
                            map.put("begindate",sdf.format(bdate));
                            map.put("modifiedtime",null);
                            map.put("pubts",null);
                            map.put("creationtime",null);
                            map.put("ts",null);
                        }
                        mainJob.put("dept_id",dept.get("deptid"));
                        mainJob.put("org_id",dept.get("orgid"));
                        mainJob.put("begindate",begindate);
                        mainJob.put("_status","Insert");
                        mainJobList.add(mainJob);
                    }else{
                        List<Map<String, Object>> newmainJobList = new ArrayList<>();
                        for (Map<String, Object> map : mainJobList) {
                            mainJob = new HashMap<>();
                            Timestamp bdate = (Timestamp) map.get("begindate");
                            mainJob.put("_status","Update");
                            mainJob.put("dept_id",map.get("dept_id"));
                            mainJob.put("org_id",map.get("org_id"));
                            mainJob.put("begindate",sdf.format(bdate));
                            mainJob.put("id",map.get("id"));
                            if(map.get("enddate") != null){
                                Timestamp enddate = (Timestamp) map.get("enddate");
                                mainJob.put("enddate",sdf.format(enddate));
                            }
                            newmainJobList.add(mainJob);
                        }
                        mainJobList = newmainJobList;
                    }

                    staff.put("mainJobList",mainJobList);
                    staff.put("_status","Update");
                    staff.put("modifiedtime",null);
                    staff.put("pubts",null);
                    staff.put("creationtime",null);
                    staff.put("ts",null);
                    for (String key: reqmap.keySet()) {
                        staff.put(key,reqmap.get(key));
                    }
                    ResponseResult<BatchResult> result = dataService.staffBatchUpdate(staff);
                    if (!result.isSuccess2()) {
                        message = "[账号(员工)修改]处理失败:"+result.getMessage();
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                    List<Object> rtList = result.getData().getInfos();
                    if(rtList == null || rtList.size() == 0){
                        List<Object> msgs = result.getData().getMessages();
                        message = "[账号(员工)修改]更新处理失败:"+msgs.get(0);
                        callBack(bimRequestId, status, message, resp,null);
                        return;
                    }
                }
                status = "0";
                callBack(bimRequestId, status, message, resp,null);
            }else{
                message = "[账号(员工)修改]验证签名失败";
                callBack(bimRequestId, status, message, resp,null);
            }
        }catch (Exception e){
            logger.error("[账号(员工)修改]异常:{}",e.getMessage());
            message = "[账号(员工)修改]异常:"+e.getMessage();
            callBack(bimRequestId, status, message, resp,null);
        }
    }

    private void callBack(String bimRequestId, String status, String message, HttpServletResponse resp,String uid) throws IOException {
        Map<String,Object> schema = new HashMap<String,Object>();
        if(uid != null && !"".equals(uid)){
            schema.put("uid", uid);
        }
        schema.put("bimRequestId", bimRequestId);
        schema.put("resultCode", status);
        schema.put("message", message);
        String mapJson = JSON.toJSONString(schema);
        logger.error("[callBack]response---json-->" + mapJson);
        if(!"Y".equals(bimLocalDebug)){
            mapJson = BamboocloudFacade.encrypt(mapJson, bimKey, type);
        }
        resp.setCharacterEncoding("UTF-8");
        PrintWriter out = resp.getWriter();
        out.write(mapJson);
        out.close();
    }

}
