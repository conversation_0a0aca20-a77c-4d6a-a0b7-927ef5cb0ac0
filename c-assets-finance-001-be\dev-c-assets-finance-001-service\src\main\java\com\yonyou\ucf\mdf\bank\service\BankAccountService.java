package com.yonyou.ucf.mdf.bank.service;

import java.util.List;

import com.yonyou.ucf.mdf.bank.vo.BankAccountVO;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;

/**
 * 企业银行账户服务
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
public interface BankAccountService {

	/**
	 * 批量保存企业银行账户
	 * 
	 * @param bankAccountVOList 企业银行账户列表
	 * @return 批量保存结果
	 */
	ResponseResult<BatchResult> batchSaveBankAccount(List<BankAccountVO> bankAccountVOList);

	/**
	 * 根据企业银行编码获取企业银行实体
	 * 
	 * @param code 企业银行编码
	 * @return 企业银行实体
	 */
	BankAccountVO getByCode(String code);
}