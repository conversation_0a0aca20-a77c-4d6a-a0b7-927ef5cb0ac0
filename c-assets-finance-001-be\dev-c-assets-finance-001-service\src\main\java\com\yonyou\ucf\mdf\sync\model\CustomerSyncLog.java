package com.yonyou.ucf.mdf.sync.model;

import java.util.Date;

import com.yonyou.iuap.yms.annotation.YMSEntity;
import com.yonyou.ypd.bill.basic.entity.SuperDO;

/**
 * <AUTHOR>
 * @description 客户同步日志
 * @Date 2025-05-28 13:42:01
 * @since 2023/11/28
 **/
@YMSEntity(name = "cxkingdee-sync.cxkingdee-sync.CustomerSyncLog", domain = "c-assets-finance-001")
public class CustomerSyncLog extends SuperDO {
	public static final String ENTITY_NAME = "cxkingdee-sync.cxkingdee-sync.CustomerSyncLog";
	public static final String CODE = "code";
	public static final String NAME = "name";
	public static final String ORGCODE = "orgCode";
	public static final String ORGNAME = "orgName";
	public static final String KINGDEEDATA = "kingdeeData";
	public static final String REQUESTDATA = "requestData";
	public static final String RESPONEDATA = "responeData";
	public static final String SUCCESS = "success";
	public static final String ERRMSG = "errMsg";
	public static final String ERRSTACK = "errStack";
	public static final String COSTTIME = "costTime";
	public static final String BUSINESSDATE = "businessDate";
	public static final String CREATETIME = "createTime";
	public static final String CREATOR = "creator";
	public static final String ID = "id";
	public static final String MODIFIER = "modifier";
	public static final String MODIFYTIME = "modifyTime";
	public static final String PUBTS = "pubts";
	public static final String YTENANTID = "ytenantId";

	/* 客户编码 */
	private String code;
	/* 客户名称 */
	private String name;
	/* 组织编码 */
	private String orgCode;
	/* 组织名称 */
	private String orgName;
	/* 金蝶客户数据 */
	private String kingdeeData;
	/* 请求客户保存接口数据 */
	private String requestData;
	/* 客户保存接口返回数据 */
	private String responeData;
	/* 成功标识 */
	private String success;
	/* 错误信息 */
	private String errMsg;
	/* 错误堆栈信息 */
	private String errStack;
	/* 耗时 */
	private Integer costTime;
	/* 单据日期 */
	private String businessDate;
	/* 创建时间 */
	private Date createTime;
	/* 创建人 */
	private String creator;
	/* id */
	private String id;
	/* 修改人 */
	private String modifier;
	/* 修改时间 */
	private Date modifyTime;
	/* pubts */
	private Date pubts;
	/* 租户id */
	private String ytenantId;

	public void setCode(String code) {
		this.code = code;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public void setKingdeeData(String kingdeeData) {
		this.kingdeeData = kingdeeData;
	}

	public void setRequestData(String requestData) {
		this.requestData = requestData;
	}

	public void setResponeData(String responeData) {
		this.responeData = responeData;
	}

	public void setSuccess(String success) {
		this.success = success;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public void setErrStack(String errStack) {
		this.errStack = errStack;
	}

	public void setCostTime(Integer costTime) {
		this.costTime = costTime;
	}

	public void setBusinessDate(String businessDate) {
		this.businessDate = businessDate;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public void setPubts(Date pubts) {
		this.pubts = pubts;
	}

	public void setYtenantId(String ytenantId) {
		this.ytenantId = ytenantId;
	}

	public String getCode() {
		return code;
	}

	public String getName() {
		return name;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public String getKingdeeData() {
		return kingdeeData;
	}

	public String getRequestData() {
		return requestData;
	}

	public String getResponeData() {
		return responeData;
	}

	public String getSuccess() {
		return success;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public String getErrStack() {
		return errStack;
	}

	public Integer getCostTime() {
		return costTime;
	}

	public String getBusinessDate() {
		return businessDate;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public String getCreator() {
		return creator;
	}

	public String getId() {
		return id;
	}

	public String getModifier() {
		return modifier;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public Date getPubts() {
		return pubts;
	}

	public String getYtenantId() {
		return ytenantId;
	}

	public String getFullName() {
		if (super.getFullName() != null && super.getFullName().indexOf("#PT#.") != -1) {
			return super.getFullName();
		} else {
			return ENTITY_NAME;
		}
	}
}
