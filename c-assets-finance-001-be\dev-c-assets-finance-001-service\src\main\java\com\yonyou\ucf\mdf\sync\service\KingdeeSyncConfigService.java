package com.yonyou.ucf.mdf.sync.service;

import java.util.List;
import java.util.Map;

import com.yonyou.ucf.mdf.sync.enums.SyncTypeEnum;

/**
 * 同步数据系统配置查询接口
 * 
 * <AUTHOR>
 *
 *         2025年5月29日
 */
public interface KingdeeSyncConfigService {

	/**
	 * 获取数据同步所有配置
	 * 
	 * @param bankBranch 数据同步类型
	 * @return
	 */
	Map<String, List<Map<String, Object>>> queryAllCfg(SyncTypeEnum bankBranch);

	/**
	 * 获取数据同步字段默认值配置
	 * 
	 * @param bankBranch
	 * @return
	 */
	List<Map<String, Object>> queryDefualtValueCfg(SyncTypeEnum bankBranch);

}
