package com.yonyou.ucf.mdf.voucher.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * 凭证查询参数
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@Data
public class VoucherQueryParams {

    /**
     * 分页对象信息
     */
    private Pager pager;

    /**
     * 账簿code
     */
    private String accbookCode;

    /**
     * 科目code集合（自动包含子级科目）
     */
    private List<String> accsubjectCodeList;

    /**
     * 起始期间（默认当前系统年月）格式为yyyy-MM
     */
    private String periodStart;

    /**
     * 截止期间（默认当前系统年月）格式为yyyy-MM
     */
    private String periodEnd;

    /**
     * 起始制单日期 格式为yyyy-MM-dd
     */
    private String makeTimeStart;

    /**
     * 截止制单日期 格式为yyyy-MM-dd
     */
    private String makeTimeEnd;

    /**
     * 凭证状态（00暂存，01保存，02错误，03已审核，04已记账，05作废，空值查全部）
     */
    private List<String> voucherStatusList;

    /**
     * 凭证类型code
     */
    private List<String> voucherTypeCodeList;

    /**
     * 摘要
     */
    private String description;

    /**
     * 制单人用户名
     */
    private List<String> makerNameList;

    /**
     * 审核人用户名
     */
    private List<String> auditorNameList;

    /**
     * 记账人用户名
     */
    private List<String> tallymanNameList;

    /**
     * 凭证号区间左端点
     */
    private Integer billcodeMin;

    /**
     * 凭证号区间右端点
     */
    private Integer billcodeMax;

    /**
     * 分录金额区间左端点
     */
    private BigDecimal moneyRangeMin;

    /**
     * 分录金额区间右端点
     */
    private BigDecimal moneyRangeMax;

    /**
     * 最后操作日期区间左端点 格式为yyyy-MM-dd HH:mm:ss
     */
    private String tsStart;

    /**
     * 最后操作日期区间右端点 格式为yyyy-MM-dd HH:mm:ss
     */
    private String tsEnd;

    /**
     * 分页对象
     */
    @Data
    public static class Pager {
        /**
         * 页码
         */
        private Integer pageIndex;

        /**
         * 每页大小
         */
        private Integer pageSize;
    }
} 