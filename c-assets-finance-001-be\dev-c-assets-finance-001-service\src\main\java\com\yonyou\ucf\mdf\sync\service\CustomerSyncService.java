package com.yonyou.ucf.mdf.sync.service;

import java.time.LocalDateTime;
import java.util.List;

import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomer;

/**
 * 客户数据同步服务
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
public interface CustomerSyncService {

	/**
	 * 同步客户数据
	 * 
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 同步结果
	 */
	void syncCustomer(LocalDateTime startTime, LocalDateTime endTime);

	/**
	 * 发布客户同步事件
	 * 
	 * @param kingdeeCustomers 金蝶客户数据列表
	 */
	void publishEvent(List<KingdeeCustomer> kingdeeCustomers);

}