package com.yonyou.ucf.mdf.sync.handler;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.lmax.disruptor.EventHandler;
import com.yonyou.ucf.mdf.bank.service.BankAccountService;
import com.yonyou.ucf.mdf.bank.vo.BankAccountVO;
import com.yonyou.ucf.mdf.base.enums.ActionEnum;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankAccount;
import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.SyncStatusEnum;
import com.yonyou.ucf.mdf.sync.event.BankAccountSyncEvent;
import com.yonyou.ucf.mdf.sync.model.BankAccountSyncFailRecord;
import com.yonyou.ucf.mdf.sync.model.BankAccountSyncLog;
import com.yonyou.ucf.mdf.sync.service.BankAccountConverter;
import com.yonyou.ucf.mdf.sync.service.BankAccountSyncFailRecordService;
import com.yonyou.ucf.mdf.sync.service.BankAccountSyncLogService;
import com.yonyou.ucf.mdf.sync.util.EhCacheUtil;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 企业银行账户同步事件处理类
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Slf4j
@Component
public class BankAccountSyncEventHandler implements EventHandler<BankAccountSyncEvent> {

	@Autowired
	private BankAccountConverter bankAccountConverter;
	@Autowired
	private BankAccountService bankAccountService;
	@Autowired
	private BankAccountSyncLogService bankAccountSyncLogService;
	@Autowired
	private BankAccountSyncFailRecordService bankAccountSyncFailRecordService;
	@Autowired
	private EhCacheUtil ehCacheUtil;

	@Override
	public void onEvent(BankAccountSyncEvent event, long sequence, boolean endOfBatch) throws Exception {

		KingdeeBankAccount kingdeeBankAccount = event.getKingdeeBankAccount();
		log.info("开始处理企业银行账户同步事件，金蝶企业银行账户：{}，序列号：{}", kingdeeBankAccount, sequence);
		BankAccountVO bankAccountVO = null;
		BankAccountSyncLog syncLog = null;
		long begin = System.currentTimeMillis();
		try {
			// 1. 转换数据
			bankAccountVO = bankAccountConverter.convert(kingdeeBankAccount);
			if (bankAccountVO == null) {
				return;
			}

			// 2. 保存数据
			ResponseResult<BatchResult> responseResult = bankAccountService
					.batchSaveBankAccount(Collections.singletonList(bankAccountVO));

			// 3. 记录同步日志
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog = new BankAccountSyncLog();
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			syncLog.setCode(kingdeeBankAccount.getNumber());
			syncLog.setName(kingdeeBankAccount.getName());
			syncLog.setAccount(kingdeeBankAccount.getBankAccountNumber());
			syncLog.setOrgCode(kingdeeBankAccount.getCompanyNum());
			syncLog.setOrgName(kingdeeBankAccount.getCompanyName());
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeBankAccount));
			syncLog.setRequestData(JSONObject.toJSONString(bankAccountVO));
			syncLog.setResponeData(JSONObject.toJSONString(responseResult));
			syncLog.setCostTime(costTime);
			if (!"200".equals(responseResult.getCode())) {
				syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
				syncLog.setErrMsg(responseResult.getMessage());
			} else {
				BatchResult batchResult = responseResult.getData();
				if (batchResult != null) {
					if (batchResult.getFailCount() != null && batchResult.getFailCount() > 0) {
						syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
						if (CollectionUtil.isNotEmpty(batchResult.getMessages())) {
							syncLog.setErrMsg(batchResult.getMessages().get(0).getMessage());
						}
					} else {
						syncLog.setSuccess(SyncStatusEnum.SUCCESS.getCode());
					}
				} else {
					syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
					syncLog.setErrMsg("调用企业银行账户保存接口，返回数据BatchResult为空，无法判断是否保存成功");
				}
			}

			bankAccountSyncLogService.logSuccess(syncLog);

			log.info("企业银行账户同步事件处理完成，金蝶企业银行账户：{}，序列号：{}", event.getKingdeeBankAccount(), sequence);
		} catch (Exception e) {
			log.error("企业银行账户同步事件处理失败，金蝶企业银行账户：{}，序列号：{}", event.getKingdeeBankAccount(), sequence, e);
			// 记录失败日志
			if (syncLog == null) {
				syncLog = new BankAccountSyncLog();
			}
			long end = System.currentTimeMillis();
			int costTime = (int) (end - begin);
			syncLog.setCode(kingdeeBankAccount.getNumber());
			syncLog.setName(kingdeeBankAccount.getName());
			syncLog.setAccount(kingdeeBankAccount.getBankAccountNumber());
			syncLog.setOrgCode(kingdeeBankAccount.getCompanyNum());
			syncLog.setOrgName(kingdeeBankAccount.getCompanyName());
			syncLog.setSuccess(SyncStatusEnum.FAIL.getCode());
			syncLog.setErrMsg("企业银行账户同步事件处理失败:" + e.getMessage());
			syncLog.set_status(ActionEnum.INSERT.getValueInt());
			syncLog.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			if (bankAccountVO != null) {
				syncLog.setRequestData(JSONObject.toJSONString(bankAccountVO));
			}
			syncLog.setKingdeeData(JSONObject.toJSONString(kingdeeBankAccount));
			syncLog.setCostTime(costTime);
			syncLog.setErrStack(ExceptionUtils.getStackTrace(e));
			bankAccountSyncLogService.logError(syncLog);
		}

		BankAccountSyncFailRecord failRecord = convertFailRecord(syncLog);
		if (failRecord != null) {
			bankAccountSyncFailRecordService.saveFailRecord(failRecord);
		}

		if (SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			// 缓存成功数据
			ehCacheUtil.putSuccess(CacheConstant.CACHE_BANK_ACCOUNT, kingdeeBankAccount.getNumber(),
					kingdeeBankAccount);
			ehCacheUtil.removeFail(CacheConstant.CACHE_BANK_ACCOUNT, kingdeeBankAccount.getNumber());
			deleteFailRecord(syncLog);
		} else {
			// 缓存失败数据
			ehCacheUtil.putFail(CacheConstant.CACHE_BANK_ACCOUNT, kingdeeBankAccount.getNumber(), kingdeeBankAccount);
		}

	}

	/**
	 * 删除同步失败记录
	 * 
	 * @param syncLog
	 */
	private void deleteFailRecord(BankAccountSyncLog syncLog) {
		bankAccountSyncFailRecordService.deleteByCode(syncLog.getCode());
	}

	/**
	 * 同步日志转换失败记录
	 * 
	 * @param syncLog
	 * @return
	 */
	private BankAccountSyncFailRecord convertFailRecord(BankAccountSyncLog syncLog) {
		if (syncLog == null || SyncStatusEnum.isSuccess(syncLog.getSuccess())) {
			return null;
		}
		BankAccountSyncFailRecord failRecord = new BankAccountSyncFailRecord();
		failRecord.setCode(syncLog.getCode());
		failRecord.setName(syncLog.getName());
		failRecord.setAccount(syncLog.getAccount());
		failRecord.setOrgCode(syncLog.getOrgCode());
		failRecord.setOrgName(syncLog.getOrgName());
		failRecord.setCostTime(syncLog.getCostTime());
		failRecord.setBusinessDate(syncLog.getBusinessDate());
		failRecord.setKingdeeData(syncLog.getKingdeeData());
		failRecord.setRequestData(syncLog.getRequestData());
		failRecord.setResponeData(syncLog.getResponeData());
		failRecord.setErrMsg(syncLog.getErrMsg());
		failRecord.setErrStack(syncLog.getErrStack());
		failRecord.setRetryCount(0);
		failRecord.set_status(ActionEnum.INSERT.getValueInt());
		return failRecord;
	}
}