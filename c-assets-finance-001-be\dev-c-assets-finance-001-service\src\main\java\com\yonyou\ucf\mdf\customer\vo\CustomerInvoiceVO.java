package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案发票信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerInvoiceVO {
    /**
     * 发票信息id；新增时不传，更新时必传
     * 示例：123456
     */
    private Long id;

    /**
     * 发票类型；填写发票类型名称
     * 示例：发票类型
     */
    private String bdBillingTypeName;

    /**
     * 发票抬头；新增必填
     * 示例：发票抬头
     */
    private String title;

    /**
     * 税号
     * 示例：税号
     */
    private String taxNo;

    /**
     * 收票手机号
     * 示例：收票手机号
     */
    private String receievInvoiceMobile;

    /**
     * 收票邮箱
     * 示例：收票邮箱
     */
    private String receievInvoiceEmail;

    /**
     * 电话
     * 示例：电话
     */
    private String telephone;

    /**
     * 详细地址
     */
    private MultiLanguageVO address;

    /**
     * 银行类别；填写银行类别名称
     * 示例：银行类别
     */
    private String bankName;

    /**
     * 银行网点；填写银行网点名称
     * 示例：银行网点
     */
    private String bankDotName;

    /**
     * 银行账号
     * 示例：银行账号
     */
    private String bankAccount;

    /**
     * 备注
     * 示例：备注
     */
    private String remarks;

    /**
     * 默认发票信息；必填；发票信息页签下；发票信息的默认值只能设置一个；true：是；false：否
     * 示例：true
     */
    private Boolean isDefault;

    /**
     * 客户档案发票信息特征
     */
    private Object agentInvoiceCharacter;

    /**
     * 客户档案发票信息实体状态；"Insert":新增，"Update":更新，"Delete":删除。不传默认为新增
     * 示例：Insert
     */
    private String entityStatus;

    /**
     * 来源数据唯一标识
     * 示例：123456
     */
    private String sourceUnique;
} 