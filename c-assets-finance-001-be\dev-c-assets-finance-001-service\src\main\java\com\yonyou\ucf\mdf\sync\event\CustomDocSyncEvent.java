package com.yonyou.ucf.mdf.sync.event;

import com.lmax.disruptor.EventFactory;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDoc;

import lombok.Data;

/**
 * 自定义档案同步事件
 * 
 * <AUTHOR>
 * @date 2025-05-31
 */
@Data
public class CustomDocSyncEvent {

	/**
	 * 金蝶自定义档案
	 */
	private KingdeeCustomDoc kingdeeCustomDoc;

	/**
	 * 事件工厂
	 */
	public static final EventFactory<CustomDocSyncEvent> FACTORY = new EventFactory<CustomDocSyncEvent>() {
		@Override
		public CustomDocSyncEvent newInstance() {
			return new CustomDocSyncEvent();
		}
	};
}