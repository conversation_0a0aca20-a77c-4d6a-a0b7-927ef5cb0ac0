package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;

/**
 * 针对各种编码、主键、对照等映射的缓存接口
 * 
 * <AUTHOR>
 *
 *         2025年6月12日
 */
public interface CodeMappingCache {
	/**
	 * 获取映射值
	 * 
	 * @param mappingType 映射类型
	 * @param mappingKey  映射键值
	 * @return
	 */
	String getMapping(MappingTypeEnum mappingType, String mappingKey);

	/**
	 * 预加载缓存
	 * 
	 * @param mappingType
	 */
	void preload(MappingTypeEnum mappingType);

	/**
	 * 手动刷新
	 * 
	 * @param mappingType
	 */
	void refresh(MappingTypeEnum mappingType);
}
