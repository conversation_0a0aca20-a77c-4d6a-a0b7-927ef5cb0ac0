package com.yonyou.ucf.mdf.sync.service.impl;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import com.yonyou.ucf.mdf.sync.constant.CacheConstant;
import com.yonyou.ucf.mdf.sync.enums.MappingTypeEnum;
import com.yonyou.ucf.mdf.sync.service.CodeMappingCache;
import com.yonyou.ucf.mdf.sync.service.CodeMappingRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 本地映射缓存实现
 * 
 * <AUTHOR>
 * @date 2025年6月12日
 */
@Slf4j
@Service
public class LocalCodeMappingCache implements CodeMappingCache {

	@Autowired
	private CacheManager cacheManager;
	@Autowired
	private CodeMappingRepository codeMappingRepository;

	private Cache getCodeMappingCache() {
		return cacheManager.getCache(CacheConstant.CACHE_CODE_MAPPING);
	}

	@Override
	public String getMapping(MappingTypeEnum mappingType, String mappingKey) {
		String cacheKey = mappingType.name() + ":" + mappingKey;
		Cache.ValueWrapper valueWrapper = getCodeMappingCache().get(cacheKey);

		if (valueWrapper != null) {
			log.debug("缓存命中: {}", cacheKey);
			return (String) valueWrapper.get();
		}

		log.debug("缓存未命中: {}, 降级查询数据库", cacheKey);
		String dbValue = queryFromDatabase(mappingType, mappingKey);

		if (dbValue != null) {
			getCodeMappingCache().put(cacheKey, dbValue);
		}

		return dbValue;
	}

	private String queryFromDatabase(MappingTypeEnum mappingType, String mappingKey) {
		return codeMappingRepository.queryValueByKey(mappingType, mappingKey);
	}

	@Override
	public void preload(MappingTypeEnum mappingType) {
		log.info("预加载映射类型: {}", mappingType);
		Map<String, String> mappings = codeMappingRepository.query(mappingType);
		if (mappings != null && !mappings.isEmpty()) {
			for (String mappingKey : mappings.keySet()) {
				String cacheKey = mappingType.name() + ":" + mappingKey;
				getCodeMappingCache().put(cacheKey, mappings.get(mappingKey));
			}
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void refresh(MappingTypeEnum mappingType) {
		log.info("刷新映射类型缓存: {}", mappingType);
		Cache cache = getCodeMappingCache();
		javax.cache.Cache<Object, Object> nativeCache = (javax.cache.Cache<Object, Object>) cache.getNativeCache();
		for (Object key : nativeCache) {
			if (key instanceof String && ((String) key).startsWith(mappingType + ":")) {
				cache.evict(key);
			}
		}
		preload(mappingType);
	}

}
