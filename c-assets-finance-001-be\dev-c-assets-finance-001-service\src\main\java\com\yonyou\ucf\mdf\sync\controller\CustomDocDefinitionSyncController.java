package com.yonyou.ucf.mdf.sync.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.base.BizObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yonyou.ucf.mdf.api.util.JsonUtils;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeCustomDocDefinition;
import com.yonyou.ucf.mdf.sync.model.SyncDate;
import com.yonyou.ucf.mdf.sync.model.TaskResult;
import com.yonyou.ucf.mdf.sync.service.CustomDocDefinitionSyncService;

/**
 * 自定义档案定义同步控制器
 * 
 * <AUTHOR>
 * @date 2025年5月30日
 */
@RestController
@RequestMapping("/rest")
public class CustomDocDefinitionSyncController extends BaseSyncController {

	@Autowired
	private CustomDocDefinitionSyncService customDocDefinitionSyncService;

	/**
	 * 同步自定义档案定义数据
	 * 
	 * @param syncDate 同步日期参数
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/custom-doc-definition/sync")
	public TaskResult syncCustomDocDefinition(@RequestBody(required = false) SyncDate syncDate) throws Exception {
		LocalDateTime[] timeRange = getSyncTimeRange(syncDate);
		try {
			customDocDefinitionSyncService.syncCustomDocDefinition(timeRange[0], timeRange[1]);
			return createTaskResult("自定义档案定义同步", null);
		} catch (Exception e) {
			return createTaskResult("自定义档案定义同步", e);
		}
	}

	/**
	 * 根据勾选的同步失败记录重试
	 * 
	 * @param rows 同步失败记录列表
	 * @return 同步结果
	 * @throws Exception
	 */
	@PostMapping("/custom-doc-definition/selectretry")
	public TaskResult syncCustomDocDefinitionRetry(@RequestBody List<BizObject> rows) throws Exception {
		try {
			if (CollectionUtils.isNotEmpty(rows)) {
				List<KingdeeCustomDocDefinition> kingdeeCustomDocDefinitions = rows.stream().map(row -> {
					String kingdeeData = row.getString("kingdeeData");
					if (StringUtils.isBlank(kingdeeData)) {
						return null;
					}
					return JsonUtils.parseObject(kingdeeData, KingdeeCustomDocDefinition.class);
				}).filter(Objects::nonNull).collect(Collectors.toList());

				if (!kingdeeCustomDocDefinitions.isEmpty()) {
					customDocDefinitionSyncService.publishEvent(kingdeeCustomDocDefinitions);
				}
			}
			return createTaskResult("自定义档案定义同步", null);
		} catch (Exception e) {
			return createTaskResult("自定义档案定义同步", e);
		}
	}
}