package com.yonyou.ucf.mdf.api.util;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/21 08:57
 * @DESCRIPTION URL参数构建类
 */
public class UrlUtil {
    public static String addUrlParams(String url, Map<String, Object> urlParams) {
        StringBuilder completeUrl = new StringBuilder(url);
        if (urlParams != null) {
            int i = 0;

            for(Iterator<Map.Entry<String, Object>> var4 = urlParams.entrySet().iterator(); var4.hasNext(); ++i) {
                Map.Entry<String, Object> paramsEntry = var4.next();
                if (i == 0) {
                    if (completeUrl.toString().contains("?")) {
                        completeUrl.append("&");
                    } else {
                        completeUrl.append("?");
                    }
                } else {
                    completeUrl.append("&");
                }

                completeUrl.append(paramsEntry.getKey()).append("=").append(paramsEntry.getValue());
            }
        }

        return completeUrl.toString();
    }

    public static String addUrlParams(String url, String key, String value) {
        Map<String, Object> map = new HashMap<>();
        map.put(key, value);
        return addUrlParams(url, map);
    }
}
