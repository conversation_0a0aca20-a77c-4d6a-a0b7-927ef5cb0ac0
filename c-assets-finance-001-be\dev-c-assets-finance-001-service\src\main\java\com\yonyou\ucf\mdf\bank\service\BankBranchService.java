package com.yonyou.ucf.mdf.bank.service;

import java.util.List;

import com.yonyou.ucf.mdf.bank.vo.BankBranchVO;
import com.yonyou.ucf.mdf.base.vo.BatchResult;
import com.yonyou.ucf.mdf.base.vo.ResponseResult;

/**
 * 银行网点服务接口
 * 
 * <AUTHOR>
 * @date 2025年5月26日
 */
public interface BankBranchService {

	/**
	 * 批量保存银行网点信息
	 * 
	 * @param bankBranchVOList 银行网点信息列表
	 * @return 批量保存结果
	 */
	ResponseResult<BatchResult> batchSaveBankBranch(List<BankBranchVO> bankBranchVOList);

	/**
	 * 通过银行网点编码获取id（从数据库获取）
	 * 
	 * @param code
	 * @return
	 */
	String findIdByCode(String code);
}