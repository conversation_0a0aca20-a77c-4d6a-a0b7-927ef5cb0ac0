package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.sync.model.BankBranchSyncFailRecord;

/**
 * 银行网点同步失败记录服务接口
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
public interface BankBranchSyncFailRecordService {

	/**
	 * 保存失败记录
	 * 
	 * @param failRecord 失败记录
	 */
	void saveFailRecord(BankBranchSyncFailRecord failRecord);

	/**
	 * 根据编码删除
	 * 
	 * @param code
	 */
	void deleteByCode(String code);
}