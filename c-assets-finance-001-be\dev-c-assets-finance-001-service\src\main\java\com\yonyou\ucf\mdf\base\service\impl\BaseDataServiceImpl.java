package com.yonyou.ucf.mdf.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yonyou.iuap.yms.api.IYmsJdbcApi;
import com.yonyou.iuap.yms.dao.BaseDAO;
import com.yonyou.iuap.yms.processor.MapListProcessor;
import com.yonyou.ucf.mdf.api.common.BatchResult;
import com.yonyou.ucf.mdf.api.common.BatchResultAI;
import com.yonyou.ucf.mdf.api.common.ResponseResult;
import com.yonyou.ucf.mdf.api.util.BipOpenApiRequest;
import com.yonyou.ucf.mdf.base.service.BaseDataService;
import com.yonyou.ucf.mdf.base.service.vo.AdminOrgVO;
import com.yonyou.ucf.mdf.base.service.vo.AttributeEntity;
import com.yonyou.ucf.mdf.base.service.vo.BaseOrg;
import com.yonyou.ucf.mdf.base.service.vo.Staff;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QuerySchema;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/21 16:30
 * @DESCRIPTION 基础数据实现类
 */
@Service
public class BaseDataServiceImpl implements BaseDataService {
    @Autowired
    private IBillQueryRepository queryRepository;

    @Autowired
    private BipOpenApiRequest request;

    @Resource(name = "baseDAO", type = BaseDAO.class)
    private IYmsJdbcApi ymsJdbcApi;


    @Override
    public ResponseResult<BatchResult> orgBatchSave(List<BaseOrg> baseOrgList) {
        return request.doPost(orgBatchSaveUrl, baseOrgList.get(0), new TypeReference<ResponseResult<BatchResult>>() {
        });
    }

    @Override
    public ResponseResult<BatchResult> orgBatchUpdate(Map<String, Object> org) {
        return request.doPost(orgBatchSaveUrl, org, new TypeReference<ResponseResult<BatchResult>>() {
        });
    }

    @Override
    public ResponseResult<BatchResultAI> deptBatchSave(List<AdminOrgVO> adminOrgVOList) {
        return request.doPost(deptBatchSaveUrl, adminOrgVOList.get(0), new TypeReference<ResponseResult<BatchResultAI>>() {
        });
    }

    @Override
    public ResponseResult<BatchResultAI> deptBatchUpdate(Map<String, Object> dept) {
        return request.doPost(deptBatchSaveUrl, dept, new TypeReference<ResponseResult<BatchResultAI>>() {
        });
    }

    @Override
    public ResponseResult<BatchResult> staffBatchSave(List<Staff> staffList) {
        return request.doPost(StaffBatchSave, staffList.get(0), new TypeReference<ResponseResult<BatchResult>>() {
        });
    }

    @Override
    public ResponseResult<BatchResult> staffBatchUpdate(Map<String, Object> staff) {
        return request.doPost(StaffBatchSave, staff, new TypeReference<ResponseResult<BatchResult>>() {
        });
    }

    @Override
    public ResponseResult<JSONObject> staffUnstop(Staff staff) {
        return request.doPostByObj(StaffUnstop, staff, new TypeReference<ResponseResult<JSONObject>>() {
        });
    }

    @Override
    public ResponseResult<JSONObject> staffStop(Staff staff) {
        return request.doPostByObj(StaffStop, staff, new TypeReference<ResponseResult<JSONObject>>() {
        });
    }

    @Override
    public Map<String, Object> selectBaseOrgByCode(String code) {
        return getStringObjectMap(code, baseOrgFullName, orgDomain);
    }

    @Override
    public List<Map<String, Object>> selectBaseOrgByCodes(Object[] codes) {
        return getMaps(codes, baseOrgFullName, orgDomain);
    }

    @Override
    public List<Map<String, Object>> selectBaseOrgBySchema(QuerySchema querySchema) {
        List<Map<String, Object>> maps = queryRepository.queryMapBySchema(baseOrgFullName, querySchema, orgDomain);
        if (CollUtil.isNotEmpty(maps)) {
            return maps;
        }
        return null;
    }

    @Override
    public Map<String, Object> selectDeptByCode(String code) {
        return getStringObjectMap(code, deptFullName, orgDomain);
    }

    @Override
    public List<Map<String, Object>> selectDeptByCodes(Object[] codes) {
        return getMaps(codes, deptFullName, orgDomain);
    }

    @Override
    public List<Map<String, Object>> selectDeptBySchema(QuerySchema querySchema) {
        List<Map<String, Object>> maps = queryRepository.queryMapBySchema(deptFullName, querySchema, orgDomain);
        if (CollUtil.isNotEmpty(maps)) {
            return maps;
        }
        return null;
    }

    @Override
    public Map<String, Object> selectStaffByCode(String code) {
        return getStringObjectMap(code, staffFullName, staffDomain);
    }

    @Nullable
    private Map<String, Object> getStringObjectMap(String code, String staffFullName, String staffDomain) {
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("查询编码code不能为空");
        }
        QuerySchema querySchema = QuerySchema.create()
                .addSelect("*")
                .addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(code)));
        List<Map<String, Object>> maps = queryRepository.queryMapBySchema(staffFullName, querySchema, staffDomain);
        if (CollUtil.isNotEmpty(maps)) {
            return maps.get(0);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> selectStaffByCodes(Object[] codes) {
         return getMaps(codes, staffFullName, staffDomain);
    }

    @Nullable
    private List<Map<String, Object>> getMaps(Object[] codes, String staffFullName, String staffDomain) {
        if (codes == null || codes.length == 0) {
            throw new RuntimeException("查询编码codes不能为空");
        }
        QuerySchema querySchema = QuerySchema.create()
                .addSelect("*")
                .addCondition(QueryConditionGroup.and(QueryCondition.name("code").in(codes)));
        List<Map<String, Object>> maps = queryRepository.queryMapBySchema(staffFullName, querySchema, staffDomain);
        if (CollUtil.isNotEmpty(maps)) {
            return maps;
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> selectStaffBySchema(QuerySchema querySchema) {
        List<Map<String, Object>> maps = queryRepository.queryMapBySchema(staffFullName, querySchema, staffDomain);
        if (CollUtil.isNotEmpty(maps)) {
            return maps;
        }
        return null;
    }

    @Override
    public List<AttributeEntity> getSchemeAttribute(String attrType) {
        List<AttributeEntity> list = new ArrayList<>();
        StringBuffer buff = new StringBuffer();
        buff.append("select * from c_assets_finance_001_db.third_scheme_attr");
        buff.append(" where dr='0'");
        if(attrType != null && !"".equals(attrType)){
            buff.append(" and attr_type='").append(attrType).append("'");
        }
        List<Map<String,Object>> attrs = (List<Map<String,Object>>) ymsJdbcApi.queryForList(buff.toString(),new MapListProcessor());
        if(attrs != null && attrs.size() > 0){
            AttributeEntity vo = null;
            Map<String,Object> map = null;
            for (int i = 0; i < attrs.size(); i++) {
                vo = new AttributeEntity();
                map = attrs.get(i);
                vo.setName((String) map.get("name_"));
                vo.setType((String) map.get("type_"));
                if("Y".equals(map.get("is_required"))){
                    vo.setRequired(true);
                }else{
                    vo.setRequired(false);
                }
                if("Y".equals(map.get("is_multivalued"))){
                    vo.setMultivalued(true);
                }else{
                    vo.setMultivalued(false);
                }
                list.add(vo);
            }
        }
        return list;
    }
}
