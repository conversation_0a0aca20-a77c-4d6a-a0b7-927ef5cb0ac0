package com.yonyou.ucf.mdf.sync.service;

import com.yonyou.ucf.mdf.bank.vo.BankBranchVO;
import com.yonyou.ucf.mdf.kingdee.vo.KingdeeBankBranch;

/**
 * 银行网点数据转换器
 * 
 * <AUTHOR>
 * @date 2025年5月28日
 */
public interface BankBranchConverter {
    
    /**
     * 将金蝶银行网点数据转换为本地银行网点数据
     * 
     * @param kingdeeBankBranch 金蝶银行网点数据
     * @return 本地银行网点数据
     */
    BankBranchVO convert(KingdeeBankBranch kingdeeBankBranch);
} 