package com.yonyou.ucf.mdf.customer.vo;

import lombok.Data;

/**
 * 客户档案业务信息
 * <AUTHOR>
 * @date 2025年5月26日
 */
@Data
public class CustomerAppliedDetailVO {
    /**
     * 助记码
     * 示例：助记码
     */
    private String searchCode;

    /**
     * 客户级别；填写客户级别编码
     * 示例：客户级别
     */
    private String customerLevelCode;

    /**
     * 销售渠道；填写销售渠道编码
     * 示例：销售渠道
     */
    private String customerTypeCode;

    /**
     * 发货仓库；填写发货仓库编码
     * 示例：发货仓库
     */
    private String deliveryWarehouseCode;

    /**
     * 交易币种；填写交易币种名称
     * 示例：交易币种
     */
    private String transactionCurrencyName;

    /**
     * 汇率类型；填写汇率类型编码
     * 示例：汇率类型
     */
    private String exchangeRateTypeCode;

    /**
     * 销项税率；填写税率档案编码
     * 示例：销项税率
     */
    private String taxRateNtaxRate;

    /**
     * 支付方式；0：款到发货，99：其他
     * 示例：0
     */
    private Short payWay;

    /**
     * 信用期限
     * 示例：10
     */
    private Integer creditServiceDay;

    /**
     * 收款协议；填写收款协议名称
     * 示例：收款协议
     */
    private String collectionAgreementName;

    /**
     * 结算方式；填写结算方式名称
     * 示例：结算方式
     */
    private String settlementMethodName;

    /**
     * 发运方式；填写发运方式名称
     * 示例：发运方式
     */
    private String shipmentMethodName;

    /**
     * 需要签回；true：是，false:否
     * 示例：true
     */
    private Boolean signBack;

    /**
     * 是否交易客户；字符类型；'0'：是，'1':否
     * 示例："0"
     */
    private String isTradeCustomers;

    /**
     * 启用状态；false:启用;ture:停用
     * 示例：false
     */
    private Boolean stopStatus;
} 